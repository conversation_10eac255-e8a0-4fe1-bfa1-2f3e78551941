package com.example.repository;

import com.example.entity.LaborPayment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Repository
public interface LaborPaymentRepository extends JpaRepository<LaborPayment, Long>, JpaSpecificationExecutor<LaborPayment> {

    /**
     * 根据技师ID查找工时费记录
     */
    Page<LaborPayment> findByTechnicianTechnicianId(Long technicianId, Pageable pageable);

    /**
     * 根据技师ID和年份查找工时费记录
     */
    List<LaborPayment> findByTechnicianTechnicianIdAndYear(Long technicianId, Integer year);

    /**
     * 根据技师ID、年份和月份查找工时费记录
     */
    Optional<LaborPayment> findByTechnicianTechnicianIdAndYearAndMonth(Long technicianId, Integer year, Integer month);

    /**
     * 根据支付状态查找工时费记录
     */
    Page<LaborPayment> findByPaymentStatus(LaborPayment.PaymentStatus paymentStatus, Pageable pageable);

    /**
     * 根据年份查找工时费记录
     */
    Page<LaborPayment> findByYear(Integer year, Pageable pageable);

    /**
     * 根据年份和月份查找工时费记录
     */
    Page<LaborPayment> findByYearAndMonth(Integer year, Integer month, Pageable pageable);

    /**
     * 计算技师的年度总收入
     */
    @Query("SELECT COALESCE(SUM(lp.totalAmount), 0) FROM LaborPayment lp " +
           "WHERE lp.technician.technicianId = :technicianId AND lp.year = :year")
    BigDecimal calculateYearlyEarningsByTechnicianId(@Param("technicianId") Long technicianId,
                                                     @Param("year") Integer year);

    /**
     * 计算技师的月度总收入
     */
    @Query("SELECT COALESCE(SUM(lp.totalAmount), 0) FROM LaborPayment lp " +
           "WHERE lp.technician.technicianId = :technicianId AND lp.year = :year AND lp.month = :month")
    BigDecimal calculateMonthlyEarningsByTechnicianId(@Param("technicianId") Long technicianId,
                                                      @Param("year") Integer year,
                                                      @Param("month") Integer month);

    /**
     * 计算技师的年度总工时
     */
    @Query("SELECT COALESCE(SUM(lp.totalHours), 0) FROM LaborPayment lp " +
           "WHERE lp.technician.technicianId = :technicianId AND lp.year = :year")
    BigDecimal calculateYearlyHoursByTechnicianId(@Param("technicianId") Long technicianId,
                                                  @Param("year") Integer year);

    /**
     * 统计年度工时费支出
     */
    @Query("SELECT COALESCE(SUM(lp.totalAmount), 0) FROM LaborPayment lp WHERE lp.year = :year")
    BigDecimal calculateYearlyTotalPayments(@Param("year") Integer year);

    /**
     * 统计季度工时费支出
     */
    @Query("SELECT COALESCE(SUM(lp.totalAmount), 0) FROM LaborPayment lp " +
           "WHERE lp.year = :year AND lp.month BETWEEN :startMonth AND :endMonth")
    BigDecimal calculateQuarterlyTotalPayments(@Param("year") Integer year,
                                              @Param("startMonth") Integer startMonth,
                                              @Param("endMonth") Integer endMonth);

    /**
     * 检查技师某月的工时费记录是否存在
     */
    boolean existsByTechnicianTechnicianIdAndYearAndMonth(Long technicianId, Integer year, Integer month);

    /**
     * 获取待支付的工时费记录
     */
    List<LaborPayment> findByPaymentStatusOrderByCreateTimeAsc(LaborPayment.PaymentStatus paymentStatus);
}
