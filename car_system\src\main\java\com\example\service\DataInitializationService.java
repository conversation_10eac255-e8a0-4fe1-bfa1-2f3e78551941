package com.example.service;

import com.example.config.AppProperties;
import com.example.entity.*;
import com.example.repository.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Set;

@Service
public class DataInitializationService implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(DataInitializationService.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private FaultTypeRepository faultTypeRepository;

    @Autowired
    private MaterialRepository materialRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private AppProperties appProperties;

    @Override
    public void run(String... args) throws Exception {
        logger.info("Starting data initialization...");

        initializeDefaultAdmin();
        initializeFaultTypes();
        initializeMaterials();

        // 测试数据初始化已移至TestDataInitializer中，在应用完全启动后执行

        logger.info("Data initialization completed.");
    }

    /**
     * 初始化默认管理员
     */
    private void initializeDefaultAdmin() {
        AppProperties.DefaultAdmin defaultAdmin = appProperties.getDefaultAdmin();
        if (!userRepository.existsByUsername(defaultAdmin.getUsername())) {
            User admin = new User();
            admin.setUsername(defaultAdmin.getUsername());
            admin.setPassword(passwordEncoder.encode(defaultAdmin.getPassword()));
            admin.setRealName(defaultAdmin.getRealName());
            admin.setPhone(defaultAdmin.getPhone());
            admin.setEmail(defaultAdmin.getEmail());
            admin.setUserType(User.UserType.ADMIN);
            admin.setStatus(1);

            userRepository.save(admin);
            logger.info("Default admin user created: {}", defaultAdmin.getUsername());
        } else {
            logger.info("Default admin user already exists: {}", defaultAdmin.getUsername());
        }
    }

    /**
     * 初始化故障类型
     */
    private void initializeFaultTypes() {
        if (faultTypeRepository.count() == 0) {
            // 单工种故障类型
            createFaultType("发动机故障", "发动机系统故障维修，包括启动困难、异响、动力不足等",
                          Set.of(Technician.Specialty.ENGINE), 2, 4);

            createFaultType("变速箱故障", "变速箱系统故障维修，包括换挡困难、异响、漏油等",
                          Set.of(Technician.Specialty.TRANSMISSION), 2, 6);

            createFaultType("制动系统故障", "制动系统故障维修，包括刹车失灵、异响、制动距离长等",
                          Set.of(Technician.Specialty.BRAKE), 1, 2);

            createFaultType("电气系统故障", "电气系统故障维修，包括电路故障、灯光故障、电瓶问题等",
                          Set.of(Technician.Specialty.ELECTRICAL), 1, 3);

            createFaultType("空调系统故障", "空调系统故障维修，包括制冷不良、异响、漏氟等",
                          Set.of(Technician.Specialty.HVAC), 1, 2);

            createFaultType("底盘故障", "底盘系统故障维修，包括悬挂异响、转向困难、减震器故障等",
                          Set.of(Technician.Specialty.CHASSIS), 2, 4);

            createFaultType("车身损坏", "车身维修，包括钣金修复、喷漆、内饰修复等",
                          Set.of(Technician.Specialty.BODY), 1, 8);

            createFaultType("轮胎故障", "轮胎相关维修，包括更换轮胎、补胎、四轮定位等",
                          Set.of(Technician.Specialty.TIRE), 1, 1);

            // 多工种故障类型
            createFaultType("综合电气故障", "涉及多个电气系统的复杂故障",
                          Set.of(Technician.Specialty.ELECTRICAL, Technician.Specialty.ENGINE), 2, 6);

            createFaultType("底盘制动故障", "涉及底盘和制动系统的复杂故障",
                          Set.of(Technician.Specialty.CHASSIS, Technician.Specialty.BRAKE), 2, 4);

            logger.info("Fault types initialized");
        } else {
            logger.info("Fault types already exist, skipping initialization");
        }
    }

    /**
     * 创建故障类型
     */
    private void createFaultType(String typeName, String description, Set<Technician.Specialty> specialties,
                               int techCount, int estimatedHours) {
        FaultType faultType = new FaultType();
        faultType.setTypeName(typeName);
        faultType.setDescription(description);
        faultType.setRequiredSpecialties(specialties);
        faultType.setRequiredTechCount(techCount);
        faultType.setEstimatedHours(estimatedHours);
        faultType.setStatus(1);

        faultTypeRepository.save(faultType);
    }

    /**
     * 初始化材料
     */
    private void initializeMaterials() {
        if (materialRepository.count() == 0) {
            // 润滑油类
            createMaterial("机油", "5W-30", "升", new BigDecimal("50.00"), 100, "润滑油");
            createMaterial("变速箱油", "ATF", "升", new BigDecimal("80.00"), 50, "润滑油");
            createMaterial("制动液", "DOT4", "升", new BigDecimal("30.00"), 30, "润滑油");

            // 滤清器类
            createMaterial("机油滤清器", "通用型", "个", new BigDecimal("25.00"), 200, "滤清器");
            createMaterial("空气滤清器", "通用型", "个", new BigDecimal("35.00"), 150, "滤清器");
            createMaterial("燃油滤清器", "通用型", "个", new BigDecimal("40.00"), 100, "滤清器");

            // 电气配件
            createMaterial("火花塞", "标准型", "个", new BigDecimal("15.00"), 300, "电气配件");
            createMaterial("蓄电池", "12V 60Ah", "个", new BigDecimal("300.00"), 20, "电气配件");
            createMaterial("保险丝", "10A", "个", new BigDecimal("2.00"), 500, "电气配件");

            // 制动配件
            createMaterial("刹车片", "前轮", "套", new BigDecimal("120.00"), 80, "制动配件");
            createMaterial("刹车盘", "前轮", "个", new BigDecimal("200.00"), 40, "制动配件");

            // 轮胎配件
            createMaterial("轮胎", "205/55R16", "个", new BigDecimal("400.00"), 60, "轮胎配件");
            createMaterial("内胎", "16寸", "个", new BigDecimal("50.00"), 100, "轮胎配件");

            logger.info("Materials initialized");
        } else {
            logger.info("Materials already exist, skipping initialization");
        }
    }

    /**
     * 创建材料
     */
    private void createMaterial(String name, String specification, String unit, BigDecimal price,
                              int inventory, String category) {
        Material material = new Material();
        material.setMaterialName(name);
        material.setSpecification(specification);
        material.setUnit(unit);
        material.setUnitPrice(price);
        material.setInventory(inventory);
        material.setCategory(category);
        material.setStatus(1);

        materialRepository.save(material);
    }
}
