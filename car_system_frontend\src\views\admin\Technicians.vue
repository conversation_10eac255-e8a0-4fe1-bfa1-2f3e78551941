<template>
  <div class="admin-technicians">
    <div class="page-header">
      <h1>技师管理</h1>
      <p>管理系统中的所有维修技师</p>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline class="filter-form">
        <el-form-item label="搜索技师">
          <el-input
            v-model="filters.search"
            placeholder="姓名、用户名、手机号"
            style="width: 200px"
            clearable
            @keyup.enter="fetchTechnicians"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="工种">
          <el-select
            v-model="filters.specialty"
            placeholder="全部工种"
            clearable
            style="width: 150px"
            @change="fetchTechnicians"
          >
            <el-option
              v-for="specialty in specialties"
              :key="specialty.code"
              :label="specialty.name"
              :value="specialty.code"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="状态">
          <el-select
            v-model="filters.status"
            placeholder="全部状态"
            clearable
            style="width: 120px"
            @change="fetchTechnicians"
          >
            <el-option label="在职" :value="1" />
            <el-option label="离职" :value="0" />
          </el-select>
        </el-form-item>

        <el-form-item label="入职时间">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="fetchTechnicians">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetFilters">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>

        </el-form-item>
      </el-form>
    </el-card>

    <!-- 技师列表 -->
    <el-card>
      <!-- 移动端卡片视图 -->
      <div v-if="isMobile" class="mobile-technician-list">
        <div v-for="technician in technicians" :key="technician.technicianId" class="mobile-technician-card">
          <el-card shadow="hover">
            <div class="technician-info">
              <div class="technician-header">
                <div class="technician-avatar">
                  <el-avatar :size="50">{{ technician.realName?.charAt(0) || 'T' }}</el-avatar>
                </div>
                <div class="technician-basic">
                  <h3 class="technician-name">{{ technician.realName }}</h3>
                  <div class="technician-username">@{{ technician.username }}</div>
                  <el-tag :type="getSpecialtyType(technician.specialty)" size="small">
                    {{ getSpecialtyName(technician.specialty) }}
                  </el-tag>
                </div>
              </div>
              <div class="technician-details">
                <div class="detail-item">
                  <span class="label">手机号:</span>
                  <span class="value">{{ technician.phone }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">邮箱:</span>
                  <span class="value">{{ technician.email }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">时薪:</span>
                  <span class="value">¥{{ technician.hourlyRate }}/小时</span>
                </div>
                <div class="detail-item">
                  <span class="label">评分:</span>
                  <span class="value">
                    <el-rate
                      v-model="technician.rating"
                      disabled
                      show-score
                      text-color="#ff9900"
                      score-template="{value}"
                    />
                  </span>
                </div>
                <div class="detail-item">
                  <span class="label">工作负载:</span>
                  <span class="value">{{ technician.workload || 0 }} 个任务</span>
                </div>
                <div class="detail-item">
                  <span class="label">入职时间:</span>
                  <span class="value">{{ formatDate(technician.hireDate) }}</span>
                </div>
              </div>
              <div class="technician-actions">
                <el-button type="danger" size="small" @click="deleteTechnician(technician)">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 桌面端表格视图 -->
      <div v-else class="table-responsive">
        <el-table
          v-loading="loading"
          :data="technicians"
          style="width: 100%"
          empty-text="暂无技师数据"
          stripe
          highlight-current-row
        >
          <el-table-column label="技师信息" min-width="200">
            <template #default="{ row }">
              <div class="technician-cell">
                <el-avatar :size="40">{{ row.realName?.charAt(0) || 'T' }}</el-avatar>
                <div class="technician-info">
                  <div class="technician-name">{{ row.realName }}</div>
                  <div class="technician-username">@{{ row.username }}</div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="工种" width="120">
            <template #default="{ row }">
              <el-tag :type="getSpecialtyType(row.specialty)" size="small">
                {{ getSpecialtyName(row.specialty) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="phone" label="手机号" width="130" />
          <el-table-column prop="email" label="邮箱" min-width="180" show-overflow-tooltip />

          <el-table-column label="时薪" width="100" align="center">
            <template #default="{ row }">
              ¥{{ row.hourlyRate }}
            </template>
          </el-table-column>

          <el-table-column label="评分" width="120" align="center">
            <template #default="{ row }">
              <el-rate
                v-model="row.rating"
                disabled
                show-score
                text-color="#ff9900"
                score-template="{value}"
              />
            </template>
          </el-table-column>

          <el-table-column label="工作负载" width="100" align="center">
            <template #default="{ row }">
              <el-tag type="info">{{ row.workload || 0 }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column label="入职时间" width="120">
            <template #default="{ row }">
              {{ formatDate(row.hireDate) }}
            </template>
          </el-table-column>

          <el-table-column label="状态" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.status === 1 ? 'success' : 'danger'" size="small">
                {{ row.status === 1 ? '在职' : '离职' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="100" fixed="right">
            <template #default="{ row }">
              <el-button
                type="text"
                size="small"
                style="color: #f56c6c"
                @click="deleteTechnician(row)"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchTechnicians"
          @current-change="fetchTechnicians"
        />
      </div>
    </el-card>




  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Refresh, Delete
} from '@element-plus/icons-vue'
import { adminAPI } from '@/api/admin'
import { technicianAPI } from '@/api/technician'
import { useAppStore } from '@/stores/app'
import dayjs from 'dayjs'

const appStore = useAppStore()

// 计算属性
const isMobile = computed(() => appStore.isMobile)

// 响应式数据
const loading = ref(false)
const technicians = ref([])
const specialties = ref([])

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 筛选条件
const filters = reactive({
  search: '',
  specialty: '',
  status: null,
  dateRange: null
})



// 工种映射
const specialtyMap = {
  engine: { name: '发动机维修', type: 'primary' },
  transmission: { name: '变速箱维修', type: 'success' },
  brake: { name: '制动系统维修', type: 'warning' },
  electrical: { name: '电气系统维修', type: 'danger' },
  hvac: { name: '空调系统维修', type: 'info' },
  chassis: { name: '底盘维修', type: '' },
  body: { name: '车身维修', type: 'primary' },
  tire: { name: '轮胎维修', type: 'success' }
}

// 获取技师列表
const fetchTechnicians = async () => {
  try {
    loading.value = true

    const params = {
      page: pagination.page,
      size: pagination.size
    }

    // 添加搜索条件
    if (filters.search) params.search = filters.search
    if (filters.specialty) params.specialty = filters.specialty
    if (filters.status !== null) params.status = filters.status
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.startDate = filters.dateRange[0]
      params.endDate = filters.dateRange[1]
    }

    const response = await adminAPI.getAllTechnicians(params)
    console.log('Admin Technicians API Response:', response)
    console.log('Response data structure:', response.data)
    console.log('Technicians content:', response.data.data?.content)
    console.log('Page info:', response.data.data?.page)

    technicians.value = response.data.data?.content || []
    pagination.total = response.data.data?.page?.totalElements || 0

    console.log('Final technicians array:', technicians.value)
    console.log('Final pagination total:', pagination.total)
  } catch (error) {
    console.error('Failed to fetch technicians:', error)
    ElMessage.error('获取技师列表失败')
  } finally {
    loading.value = false
  }
}

// 获取工种列表
const fetchSpecialties = async () => {
  try {
    const response = await technicianAPI.getSpecialties()
    const specialtyList = response.data.data || response.data || []

    // 安全地转换为选项格式，处理可能的对象或字符串数据
    specialties.value = specialtyList.map(item => {
      // 如果 item 是字符串，直接使用
      if (typeof item === 'string') {
        return {
          code: item,
          name: specialtyMap[item]?.name || item
        }
      }
      // 如果 item 是对象，提取 code 属性
      else if (typeof item === 'object' && item !== null) {
        const code = item.code || item
        return {
          code: code,
          name: specialtyMap[code]?.name || code
        }
      }
      // 其他情况，使用默认值
      else {
        return {
          code: item,
          name: String(item)
        }
      }
    })
  } catch (error) {
    console.error('Failed to fetch specialties:', error)
    // 使用默认工种列表
    specialties.value = Object.keys(specialtyMap).map(code => ({
      code,
      name: specialtyMap[code].name
    }))
  }
}

// 重置筛选条件
const resetFilters = () => {
  Object.assign(filters, {
    search: '',
    specialty: '',
    status: null,
    dateRange: null
  })
  pagination.page = 1
  fetchTechnicians()
}



// 删除技师
const deleteTechnician = async (technician) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除技师 "${technician.realName}" 吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await adminAPI.deleteTechnician(technician.technicianId)
    ElMessage.success('技师删除成功')
    fetchTechnicians()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete technician:', error)
      ElMessage.error('删除技师失败')
    }
  }
}





// 获取工种名称
const getSpecialtyName = (specialty) => {
  return specialtyMap[specialty]?.name || specialty
}

// 获取工种类型
const getSpecialtyType = (specialty) => {
  return specialtyMap[specialty]?.type || ''
}



// 格式化日期
const formatDate = (date) => {
  if (!date) return '-'
  return dayjs(date).format('YYYY-MM-DD')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchTechnicians()
  fetchSpecialties()
})
</script>

<style scoped>
.admin-technicians {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-form .el-form-item {
  margin-bottom: 0;
}

/* 移动端技师卡片 */
.mobile-technician-list {
  display: grid;
  gap: 16px;
}

.mobile-technician-card .technician-info {
  padding: 0;
}

.technician-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.technician-avatar {
  margin-right: 12px;
}

.technician-basic {
  flex: 1;
}

.technician-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.technician-username {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #909399;
}

.technician-details {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item .label {
  color: #606266;
  font-weight: 500;
}

.detail-item .value {
  color: #303133;
  text-align: right;
}

.technician-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 桌面端表格 */
.table-responsive {
  overflow-x: auto;
}

.technician-cell {
  display: flex;
  align-items: center;
}

.technician-cell .technician-info {
  margin-left: 12px;
}

.technician-cell .technician-name {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.technician-cell .technician-username {
  margin: 0;
  font-size: 12px;
  color: #909399;
}

/* 分页 */
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 表单提示 */
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-technicians {
    padding: 16px;
  }

  .filter-form {
    flex-direction: column;
  }

  .filter-form .el-form-item {
    width: 100%;
  }

  .technician-actions {
    justify-content: center;
  }

  .pagination-container {
    overflow-x: auto;
  }
}

@media (max-width: 480px) {
  .admin-technicians {
    padding: 12px;
  }

  .page-header h1 {
    font-size: 20px;
  }

  .technician-actions .el-button {
    flex: 1;
    min-width: 0;
  }
}
</style>