package com.example.dto.response;

import java.math.BigDecimal;

/**
 * 工作负载统计DTO
 */
public class WorkloadStatisticsDTO {
    
    private String specialty;
    private String specialtyName;
    private Integer assignedCount;
    private Integer completedCount;
    private BigDecimal assignmentPercentage;
    private BigDecimal completionRate;
    private BigDecimal avgWorkingHours;
    
    public WorkloadStatisticsDTO() {}
    
    public WorkloadStatisticsDTO(String specialty, String specialtyName, Integer assignedCount, 
                                Integer completedCount, BigDecimal assignmentPercentage,
                                BigDecimal completionRate, BigDecimal avgWorkingHours) {
        this.specialty = specialty;
        this.specialtyName = specialtyName;
        this.assignedCount = assignedCount;
        this.completedCount = completedCount;
        this.assignmentPercentage = assignmentPercentage;
        this.completionRate = completionRate;
        this.avgWorkingHours = avgWorkingHours;
    }
    
    // Getters and Setters
    public String getSpecialty() {
        return specialty;
    }
    
    public void setSpecialty(String specialty) {
        this.specialty = specialty;
    }
    
    public String getSpecialtyName() {
        return specialtyName;
    }
    
    public void setSpecialtyName(String specialtyName) {
        this.specialtyName = specialtyName;
    }
    
    public Integer getAssignedCount() {
        return assignedCount;
    }
    
    public void setAssignedCount(Integer assignedCount) {
        this.assignedCount = assignedCount;
    }
    
    public Integer getCompletedCount() {
        return completedCount;
    }
    
    public void setCompletedCount(Integer completedCount) {
        this.completedCount = completedCount;
    }
    
    public BigDecimal getAssignmentPercentage() {
        return assignmentPercentage;
    }
    
    public void setAssignmentPercentage(BigDecimal assignmentPercentage) {
        this.assignmentPercentage = assignmentPercentage;
    }
    
    public BigDecimal getCompletionRate() {
        return completionRate;
    }
    
    public void setCompletionRate(BigDecimal completionRate) {
        this.completionRate = completionRate;
    }
    
    public BigDecimal getAvgWorkingHours() {
        return avgWorkingHours;
    }
    
    public void setAvgWorkingHours(BigDecimal avgWorkingHours) {
        this.avgWorkingHours = avgWorkingHours;
    }
}
