package com.example.repository;

import com.example.entity.RepairOrder;
import com.example.entity.Technician;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface RepairOrderRepository extends JpaRepository<RepairOrder, Long>, JpaSpecificationExecutor<RepairOrder> {

    /**
     * 根据用户ID查找工单
     */
    Page<RepairOrder> findByUserUserId(Long userId, Pageable pageable);

    /**
     * 根据用户ID和状态查找工单
     */
    Page<RepairOrder> findByUserUserIdAndStatus(Long userId, RepairOrder.OrderStatus status, Pageable pageable);

    /**
     * 根据技师ID查找分配的工单
     */
    @Query("SELECT ro FROM RepairOrder ro JOIN ro.technicianAssignments ta WHERE ta.technician.technicianId = :technicianId")
    Page<RepairOrder> findByAssignedTechnicianId(@Param("technicianId") Long technicianId, Pageable pageable);

    /**
     * 根据技师ID和状态查找分配的工单
     */
    @Query("SELECT ro FROM RepairOrder ro JOIN ro.technicianAssignments ta WHERE ta.technician.technicianId = :technicianId AND ro.status = :status")
    Page<RepairOrder> findByAssignedTechnicianIdAndStatus(@Param("technicianId") Long technicianId,
                                                          @Param("status") RepairOrder.OrderStatus status,
                                                          Pageable pageable);

    /**
     * 根据状态查找工单
     */
    Page<RepairOrder> findByStatus(RepairOrder.OrderStatus status, Pageable pageable);

    /**
     * 根据车辆ID查找工单
     */
    Page<RepairOrder> findByVehicleVehicleId(Long vehicleId, Pageable pageable);

    /**
     * 根据故障类型ID查找工单
     */
    Page<RepairOrder> findByFaultTypeFaultTypeId(Long faultTypeId, Pageable pageable);

    /**
     * 根据时间范围查找工单
     */
    @Query("SELECT ro FROM RepairOrder ro WHERE ro.submitTime BETWEEN :startTime AND :endTime")
    Page<RepairOrder> findBySubmitTimeBetween(@Param("startTime") LocalDateTime startTime,
                                             @Param("endTime") LocalDateTime endTime,
                                             Pageable pageable);

    /**
     * 根据时间范围和状态查找工单
     */
    @Query("SELECT ro FROM RepairOrder ro WHERE ro.submitTime BETWEEN :startTime AND :endTime AND ro.status = :status")
    Page<RepairOrder> findBySubmitTimeBetweenAndStatus(@Param("startTime") LocalDateTime startTime,
                                                       @Param("endTime") LocalDateTime endTime,
                                                       @Param("status") RepairOrder.OrderStatus status,
                                                       Pageable pageable);

    /**
     * 根据工种查找工单
     */
    @Query("SELECT DISTINCT ro FROM RepairOrder ro JOIN ro.technicianAssignments ta WHERE ta.technician.specialty = :specialty")
    Page<RepairOrder> findByTechnicianSpecialty(@Param("specialty") Technician.Specialty specialty, Pageable pageable);

    /**
     * 根据用户ID和车辆ID查找工单（用于权限验证）
     */
    Optional<RepairOrder> findByOrderIdAndUserUserId(Long orderId, Long userId);

    /**
     * 根据技师ID和工单ID查找工单（用于权限验证）
     */
    @Query("SELECT ro FROM RepairOrder ro JOIN ro.technicianAssignments ta WHERE ro.orderId = :orderId AND ta.technician.technicianId = :technicianId")
    Optional<RepairOrder> findByOrderIdAndAssignedTechnicianId(@Param("orderId") Long orderId,
                                                               @Param("technicianId") Long technicianId);

    /**
     * 统计用户的工单数量
     */
    long countByUserUserId(Long userId);

    /**
     * 统计技师的工单数量
     */
    @Query("SELECT COUNT(ro) FROM RepairOrder ro JOIN ro.technicianAssignments ta WHERE ta.technician.technicianId = :technicianId")
    long countByAssignedTechnicianId(@Param("technicianId") Long technicianId);

    /**
     * 统计技师的已完成工单数量
     */
    @Query("SELECT COUNT(ro) FROM RepairOrder ro JOIN ro.technicianAssignments ta WHERE ta.technician.technicianId = :technicianId AND ro.status = 'COMPLETED'")
    long countCompletedOrdersByTechnicianId(@Param("technicianId") Long technicianId);

    /**
     * 根据紧急程度查找工单
     */
    Page<RepairOrder> findByUrgencyLevel(RepairOrder.UrgencyLevel urgencyLevel, Pageable pageable);

    /**
     * 查找待分配的工单
     */
    List<RepairOrder> findByStatusOrderBySubmitTimeAsc(RepairOrder.OrderStatus status);

    /**
     * 按品牌分组统计维修数据
     */
    @Query("SELECT v.brand, COUNT(ro), AVG(ro.totalCost), SUM(ro.totalCost) " +
           "FROM RepairOrder ro JOIN ro.vehicle v " +
           "WHERE ro.submitTime BETWEEN :startTime AND :endTime " +
           "GROUP BY v.brand ORDER BY COUNT(ro) DESC")
    List<Object[]> getRepairStatsByBrand(@Param("startTime") LocalDateTime startTime,
                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 按车型分组统计维修数据
     */
    @Query("SELECT v.brand, v.model, COUNT(ro), AVG(ro.totalCost), SUM(ro.totalCost) " +
           "FROM RepairOrder ro JOIN ro.vehicle v " +
           "WHERE ro.submitTime BETWEEN :startTime AND :endTime " +
           "GROUP BY v.brand, v.model ORDER BY COUNT(ro) DESC")
    List<Object[]> getRepairStatsByModel(@Param("startTime") LocalDateTime startTime,
                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 按故障类型分组统计维修数据
     */
    @Query("SELECT ft.typeName, COUNT(ro), AVG(ro.totalCost), SUM(ro.totalCost) " +
           "FROM RepairOrder ro JOIN ro.faultType ft " +
           "WHERE ro.submitTime BETWEEN :startTime AND :endTime " +
           "GROUP BY ft.typeName ORDER BY COUNT(ro) DESC")
    List<Object[]> getRepairStatsByFaultType(@Param("startTime") LocalDateTime startTime,
                                            @Param("endTime") LocalDateTime endTime);

    /**
     * 按工种统计工作负载
     */
    @Query(value = "SELECT t.specialty, COUNT(ro.order_id), " +
           "SUM(CASE WHEN ro.status = 'COMPLETED' THEN 1 ELSE 0 END), " +
           "AVG(CASE WHEN ro.status = 'COMPLETED' AND t.hourly_rate > 0 THEN ro.total_labor_cost/t.hourly_rate ELSE 0 END) " +
           "FROM repair_orders ro " +
           "JOIN order_technician_assignments ota ON ro.order_id = ota.order_id " +
           "JOIN technicians t ON ota.technician_id = t.technician_id " +
           "WHERE ro.submit_time BETWEEN :startTime AND :endTime " +
           "GROUP BY t.specialty", nativeQuery = true)
    List<Object[]> getWorkloadStatsBySpecialty(@Param("startTime") LocalDateTime startTime,
                                              @Param("endTime") LocalDateTime endTime);

    /**
     * 获取故障模式统计
     */
    @Query("SELECT v.brand, v.model, ft.typeName, COUNT(ro) " +
           "FROM RepairOrder ro JOIN ro.vehicle v JOIN ro.faultType ft " +
           "WHERE (:brand IS NULL OR v.brand = :brand) " +
           "AND (:model IS NULL OR v.model = :model) " +
           "GROUP BY v.brand, v.model, ft.typeName " +
           "ORDER BY COUNT(ro) DESC")
    List<Object[]> getFaultPatternStats(@Param("brand") String brand, @Param("model") String model);

    /**
     * 获取技师绩效统计
     */
    @Query(value = "SELECT t.technician_id, t.real_name, t.specialty, " +
           "COUNT(ro.order_id), SUM(CASE WHEN t.hourly_rate > 0 THEN ro.total_labor_cost/t.hourly_rate ELSE 0 END), SUM(ro.total_labor_cost) " +
           "FROM repair_orders ro " +
           "JOIN order_technician_assignments ota ON ro.order_id = ota.order_id " +
           "JOIN technicians t ON ota.technician_id = t.technician_id " +
           "WHERE ro.status = 'COMPLETED' " +
           "AND ro.actual_completion_time BETWEEN :startTime AND :endTime " +
           "AND (:specialty IS NULL OR t.specialty = :specialty) " +
           "GROUP BY t.technician_id, t.real_name, t.specialty " +
           "ORDER BY COUNT(ro.order_id) DESC", nativeQuery = true)
    List<Object[]> getTechnicianPerformanceStats(@Param("specialty") String specialty,
                                                @Param("startTime") LocalDateTime startTime,
                                                @Param("endTime") LocalDateTime endTime);

    /**
     * 根据用户ID和车辆ID查找工单
     */
    Page<RepairOrder> findByUserUserIdAndVehicleVehicleId(Long userId, Long vehicleId, Pageable pageable);

    /**
     * 根据用户ID、车辆ID和时间范围查找工单
     */
    Page<RepairOrder> findByUserUserIdAndVehicleVehicleIdAndSubmitTimeBetween(Long userId, Long vehicleId,
                                                                              LocalDateTime startDate, LocalDateTime endDate,
                                                                              Pageable pageable);

    /**
     * 根据用户ID和时间范围查找工单
     */
    Page<RepairOrder> findByUserUserIdAndSubmitTimeBetween(Long userId, LocalDateTime startDate, LocalDateTime endDate,
                                                           Pageable pageable);

    /**
     * 根据技师ID查找分配的工单（使用包含关系）
     */
    @Query("SELECT ro FROM RepairOrder ro JOIN ro.technicianAssignments ta WHERE ta.technician.technicianId = :technicianId")
    Page<RepairOrder> findByAssignedTechniciansContaining(@Param("technicianId") Long technicianId, Pageable pageable);

    /**
     * 根据技师ID和状态查找分配的工单（使用包含关系）
     */
    @Query("SELECT ro FROM RepairOrder ro JOIN ro.technicianAssignments ta WHERE ta.technician.technicianId = :technicianId AND ro.status = :status")
    Page<RepairOrder> findByAssignedTechniciansContainingAndStatus(@Param("technicianId") Long technicianId,
                                                                   @Param("status") RepairOrder.OrderStatus status,
                                                                   Pageable pageable);

    /**
     * 根据技师ID和状态列表查找分配的工单（使用包含关系）
     */
    @Query("SELECT ro FROM RepairOrder ro JOIN ro.technicianAssignments ta WHERE ta.technician.technicianId = :technicianId AND ro.status IN :statuses")
    Page<RepairOrder> findByAssignedTechniciansContainingAndStatusIn(@Param("technicianId") Long technicianId,
                                                                     @Param("statuses") List<RepairOrder.OrderStatus> statuses,
                                                                     Pageable pageable);

    /**
     * 根据用户ID、车辆ID和状态查找工单
     */
    Page<RepairOrder> findByUserUserIdAndVehicleVehicleIdAndStatus(Long userId, Long vehicleId,
                                                                   RepairOrder.OrderStatus status, Pageable pageable);

    /**
     * 根据用户ID、车辆ID、状态和时间范围查找工单
     */
    Page<RepairOrder> findByUserUserIdAndVehicleVehicleIdAndStatusAndSubmitTimeBetween(Long userId, Long vehicleId,
                                                                                       RepairOrder.OrderStatus status,
                                                                                       LocalDateTime startDate, LocalDateTime endDate,
                                                                                       Pageable pageable);

    /**
     * 根据用户ID、状态和时间范围查找工单
     */
    Page<RepairOrder> findByUserUserIdAndStatusAndSubmitTimeBetween(Long userId, RepairOrder.OrderStatus status,
                                                                    LocalDateTime startDate, LocalDateTime endDate,
                                                                    Pageable pageable);

    /**
     * 根据用户ID和状态列表统计工单数量
     */
    long countByUserUserIdAndStatusIn(Long userId, List<RepairOrder.OrderStatus> statuses);

    /**
     * 根据技师ID和状态列表统计分配的工单数量
     */
    @Query("SELECT COUNT(ro) FROM RepairOrder ro JOIN ro.technicianAssignments ta WHERE ta.technician.technicianId = :technicianId AND ro.status IN :statuses")
    long countByAssignedTechniciansContainingAndStatusIn(@Param("technicianId") Long technicianId,
                                                         @Param("statuses") List<RepairOrder.OrderStatus> statuses);
}
