package com.example.dto.response;

import java.math.BigDecimal;

/**
 * 成本分析DTO
 */
public class CostAnalysisDTO {
    
    private Integer year;
    private Integer quarter;
    private BigDecimal totalLaborCost;
    private BigDecimal totalMaterialCost;
    private BigDecimal totalCost;
    private BigDecimal laborPercentage;
    private BigDecimal materialPercentage;
    
    public CostAnalysisDTO() {}
    
    public CostAnalysisDTO(Integer year, Integer quarter, BigDecimal totalLaborCost, 
                          BigDecimal totalMaterialCost, BigDecimal totalCost,
                          BigDecimal laborPercentage, BigDecimal materialPercentage) {
        this.year = year;
        this.quarter = quarter;
        this.totalLaborCost = totalLaborCost;
        this.totalMaterialCost = totalMaterialCost;
        this.totalCost = totalCost;
        this.laborPercentage = laborPercentage;
        this.materialPercentage = materialPercentage;
    }
    
    // Getters and Setters
    public Integer getYear() {
        return year;
    }
    
    public void setYear(Integer year) {
        this.year = year;
    }
    
    public Integer getQuarter() {
        return quarter;
    }
    
    public void setQuarter(Integer quarter) {
        this.quarter = quarter;
    }
    
    public BigDecimal getTotalLaborCost() {
        return totalLaborCost;
    }
    
    public void setTotalLaborCost(BigDecimal totalLaborCost) {
        this.totalLaborCost = totalLaborCost;
    }
    
    public BigDecimal getTotalMaterialCost() {
        return totalMaterialCost;
    }
    
    public void setTotalMaterialCost(BigDecimal totalMaterialCost) {
        this.totalMaterialCost = totalMaterialCost;
    }
    
    public BigDecimal getTotalCost() {
        return totalCost;
    }
    
    public void setTotalCost(BigDecimal totalCost) {
        this.totalCost = totalCost;
    }
    
    public BigDecimal getLaborPercentage() {
        return laborPercentage;
    }
    
    public void setLaborPercentage(BigDecimal laborPercentage) {
        this.laborPercentage = laborPercentage;
    }
    
    public BigDecimal getMaterialPercentage() {
        return materialPercentage;
    }
    
    public void setMaterialPercentage(BigDecimal materialPercentage) {
        this.materialPercentage = materialPercentage;
    }
}
