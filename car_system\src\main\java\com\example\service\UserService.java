package com.example.service;

import com.example.dto.request.UserRegistrationRequest;
import com.example.dto.request.UserUpdateRequest;
import com.example.dto.response.PageResponse;
import com.example.dto.response.UserDTO;
import com.example.dto.response.VehicleDTO;
import com.example.dto.response.OrderDTO;
import com.example.dto.response.FeedbackDTO;
import com.example.entity.User;
import com.example.entity.Vehicle;
import com.example.entity.RepairOrder;
import com.example.entity.OrderFeedback;
import com.example.exception.BusinessException;
import com.example.exception.ResourceNotFoundException;
import com.example.repository.UserRepository;
import com.example.repository.VehicleRepository;
import com.example.repository.RepairOrderRepository;
import com.example.repository.OrderFeedbackRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.data.jpa.domain.Specification;
import jakarta.persistence.criteria.Predicate;

/**
 * 用户服务类
 */
@Service
@Transactional
public class UserService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private VehicleRepository vehicleRepository;

    @Autowired
    private RepairOrderRepository repairOrderRepository;

    @Autowired
    private OrderFeedbackRepository orderFeedbackRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    /**
     * 用户注册
     */
    public UserDTO registerUser(UserRegistrationRequest request) {
        // 检查用户名是否已存在
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new BusinessException("用户名已存在");
        }

        // 检查手机号是否已存在
        if (userRepository.existsByPhone(request.getPhone())) {
            throw new BusinessException("手机号已被注册");
        }

        // 检查邮箱是否已存在（如果提供了邮箱）
        if (request.getEmail() != null && !request.getEmail().trim().isEmpty()
            && userRepository.existsByEmail(request.getEmail())) {
            throw new BusinessException("邮箱已被注册");
        }

        // 创建新用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setRealName(request.getRealName());
        user.setPhone(request.getPhone());
        user.setEmail(request.getEmail());
        user.setAddress(request.getAddress());
        user.setUserType(User.UserType.USER);
        user.setStatus(1); // 默认启用状态

        User savedUser = userRepository.save(user);
        return convertToDTO(savedUser);
    }

    /**
     * 获取当前用户信息
     */
    @Transactional(readOnly = true)
    public UserDTO getCurrentUser(Long userId) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));
        return convertToDTO(user);
    }

    /**
     * 更新当前用户信息
     */
    public UserDTO updateCurrentUser(Long userId, UserUpdateRequest request) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));

        // 检查手机号是否被其他用户使用
        if (!user.getPhone().equals(request.getPhone()) && userRepository.existsByPhone(request.getPhone())) {
            throw new BusinessException("手机号已被其他用户使用");
        }

        // 检查邮箱是否被其他用户使用
        if (request.getEmail() != null && !request.getEmail().trim().isEmpty()
            && !request.getEmail().equals(user.getEmail())
            && userRepository.existsByEmail(request.getEmail())) {
            throw new BusinessException("邮箱已被其他用户使用");
        }

        // 更新用户信息（不包括用户名和密码）
        user.setRealName(request.getRealName());
        user.setPhone(request.getPhone());
        user.setEmail(request.getEmail());
        user.setAddress(request.getAddress());

        User updatedUser = userRepository.save(user);
        return convertToDTO(updatedUser);
    }

    /**
     * 获取当前用户的车辆列表
     */
    @Transactional(readOnly = true)
    public PageResponse<VehicleDTO> getCurrentUserVehicles(Long userId, Pageable pageable) {
        Page<Vehicle> vehiclePage = vehicleRepository.findByUserUserId(userId, pageable);

        List<VehicleDTO> vehicleDTOs = vehiclePage.getContent().stream()
            .map(this::convertVehicleToDTO)
            .collect(Collectors.toList());

        return PageResponse.success(vehicleDTOs, new PageResponse.PageInfo(
                vehiclePage.getNumber() + 1,
                vehiclePage.getSize(),
                vehiclePage.getTotalElements(),
                vehiclePage.getTotalPages()
        ));
    }

    /**
     * 获取当前用户的工单列表
     */
    @Transactional(readOnly = true)
    public PageResponse<OrderDTO> getCurrentUserOrders(Long userId, String status, String licensePlate,
                                                      Long faultTypeId, LocalDateTime startDate, LocalDateTime endDate,
                                                      Pageable pageable) {
        // 使用Specification构建复杂查询
        Specification<RepairOrder> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 用户ID条件
            predicates.add(criteriaBuilder.equal(root.get("user").get("userId"), userId));

            // 状态筛选
            if (status != null && !status.trim().isEmpty()) {
                if (status.contains(",")) {
                    // 支持多状态查询，状态之间用逗号分隔
                    String[] statusArray = status.split(",");
                    List<RepairOrder.OrderStatus> statusList = new ArrayList<>();
                    for (String s : statusArray) {
                        try {
                            statusList.add(RepairOrder.OrderStatus.valueOf(s.trim().toUpperCase()));
                        } catch (IllegalArgumentException e) {
                            // 忽略无效的状态值
                            continue;
                        }
                    }
                    if (!statusList.isEmpty()) {
                        predicates.add(root.get("status").in(statusList));
                    }
                } else {
                    // 单状态查询
                    try {
                        RepairOrder.OrderStatus orderStatus = RepairOrder.OrderStatus.valueOf(status.toUpperCase());
                        predicates.add(criteriaBuilder.equal(root.get("status"), orderStatus));
                    } catch (IllegalArgumentException e) {
                        // 忽略无效的状态值
                    }
                }
            }

            // 车牌号筛选
            if (licensePlate != null && !licensePlate.trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("vehicle").get("licensePlate")),
                    "%" + licensePlate.trim().toLowerCase() + "%"
                ));
            }

            // 故障类型ID筛选
            if (faultTypeId != null) {
                predicates.add(criteriaBuilder.equal(root.get("faultType").get("faultTypeId"), faultTypeId));
            }

            // 时间范围筛选
            if (startDate != null && endDate != null) {
                predicates.add(criteriaBuilder.between(root.get("submitTime"), startDate, endDate));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        Page<RepairOrder> orderPage = repairOrderRepository.findAll(spec, pageable);

        List<OrderDTO> orderDTOs = orderPage.getContent().stream()
            .map(this::convertOrderToDTO)
            .collect(Collectors.toList());

        return PageResponse.success(orderDTOs, new PageResponse.PageInfo(
                orderPage.getNumber() + 1,
                orderPage.getSize(),
                orderPage.getTotalElements(),
                orderPage.getTotalPages()
        ));
    }

    /**
     * 获取当前用户的维修历史（只返回已完成的订单）
     */
    @Transactional(readOnly = true)
    public PageResponse<OrderDTO> getCurrentUserRepairHistory(Long userId, Long vehicleId, Long faultTypeId,
                                                             LocalDateTime startDate, LocalDateTime endDate,
                                                             Pageable pageable) {
        // 使用Specification构建复杂查询
        Specification<RepairOrder> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 用户ID条件
            predicates.add(criteriaBuilder.equal(root.get("user").get("userId"), userId));

            // 只查询已完成的订单作为维修历史
            predicates.add(criteriaBuilder.equal(root.get("status"), RepairOrder.OrderStatus.COMPLETED));

            // 车辆ID筛选
            if (vehicleId != null) {
                predicates.add(criteriaBuilder.equal(root.get("vehicle").get("vehicleId"), vehicleId));
            }

            // 故障类型ID筛选
            if (faultTypeId != null) {
                predicates.add(criteriaBuilder.equal(root.get("faultType").get("faultTypeId"), faultTypeId));
            }

            // 时间范围筛选
            if (startDate != null && endDate != null) {
                predicates.add(criteriaBuilder.between(root.get("submitTime"), startDate, endDate));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        Page<RepairOrder> orderPage = repairOrderRepository.findAll(spec, pageable);

        List<OrderDTO> orderDTOs = orderPage.getContent().stream()
            .map(this::convertOrderToDTO)
            .collect(Collectors.toList());

        return PageResponse.success(orderDTOs, new PageResponse.PageInfo(
                orderPage.getNumber() + 1,
                orderPage.getSize(),
                orderPage.getTotalElements(),
                orderPage.getTotalPages()
        ));
    }

    /**
     * 获取当前用户的反馈历史
     */
    @Transactional(readOnly = true)
    public PageResponse<FeedbackDTO> getCurrentUserFeedbacks(Long userId, Integer rating,
                                                           LocalDate startDate, LocalDate endDate,
                                                           Pageable pageable) {
        // 使用Specification构建复杂查询
        Specification<OrderFeedback> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 用户ID条件 - 通过repairOrder.user访问
            predicates.add(criteriaBuilder.equal(root.get("repairOrder").get("user").get("userId"), userId));

            // 评分筛选
            if (rating != null) {
                predicates.add(criteriaBuilder.equal(root.get("rating"), rating));
            }

            // 时间范围筛选
            if (startDate != null && endDate != null) {
                LocalDateTime startDateTime = startDate.atStartOfDay();
                LocalDateTime endDateTime = endDate.atTime(23, 59, 59);
                predicates.add(criteriaBuilder.between(root.get("feedbackTime"), startDateTime, endDateTime));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        Page<OrderFeedback> feedbackPage = orderFeedbackRepository.findAll(spec, pageable);

        List<FeedbackDTO> feedbackDTOs = feedbackPage.getContent().stream()
            .map(this::convertFeedbackToDTO)
            .collect(Collectors.toList());

        return PageResponse.success(feedbackDTOs, new PageResponse.PageInfo(
                feedbackPage.getNumber() + 1,
                feedbackPage.getSize(),
                feedbackPage.getTotalElements(),
                feedbackPage.getTotalPages()
        ));
    }

    /**
     * 将User实体转换为UserDTO
     */
    private UserDTO convertToDTO(User user) {
        return new UserDTO(
            user.getUserId(),
            user.getUsername(),
            user.getRealName(),
            user.getPhone(),
            user.getEmail(),
            user.getAddress(),
            user.getStatus(),
            user.getUserType().name().toLowerCase(),
            user.getCreateTime(),
            user.getUpdateTime()
        );
    }

    /**
     * 将Vehicle实体转换为VehicleDTO
     */
    private VehicleDTO convertVehicleToDTO(Vehicle vehicle) {
        return new VehicleDTO(
            vehicle.getVehicleId(),
            vehicle.getUser().getUserId(),
            vehicle.getLicensePlate(),
            vehicle.getBrand(),
            vehicle.getModel(),
            vehicle.getYear(),
            vehicle.getVin(),
            vehicle.getColor(),
            vehicle.getEngineNumber(),
            vehicle.getRegisterDate(),
            vehicle.getCreateTime(),
            vehicle.getUpdateTime()
        );
    }

    /**
     * 将RepairOrder实体转换为OrderDTO（简化版本）
     */
    private OrderDTO convertOrderToDTO(RepairOrder order) {
        OrderDTO dto = new OrderDTO();
        dto.setOrderId(order.getOrderId());
        dto.setUserId(order.getUser().getUserId());
        dto.setVehicleId(order.getVehicle().getVehicleId());
        dto.setFaultTypeId(order.getFaultType().getFaultTypeId());
        dto.setDescription(order.getDescription());
        dto.setUrgencyLevel(order.getUrgencyLevel().name().toLowerCase());
        dto.setSubmitTime(order.getSubmitTime());
        dto.setPreferredTime(order.getPreferredTime());
        dto.setEstimatedCompletionTime(order.getEstimatedCompletionTime());
        dto.setActualCompletionTime(order.getActualCompletionTime());
        dto.setStatus(order.getStatus().name().toLowerCase());
        dto.setPaymentStatus(order.getPaymentStatus().name().toLowerCase());
        dto.setContactPhone(order.getContactPhone());
        dto.setTotalLaborCost(order.getTotalLaborCost());
        dto.setTotalMaterialCost(order.getTotalMaterialCost());
        dto.setTotalCost(order.getTotalCost());
        dto.setWorkResult(order.getWorkResult());
        dto.setWorkingHours(order.getWorkingHours());

        // 设置关联对象信息
        dto.setUser(new OrderDTO.UserInfo(
            order.getUser().getUserId(),
            order.getUser().getUsername(),
            order.getUser().getRealName(),
            order.getUser().getPhone()
        ));

        dto.setVehicle(new OrderDTO.VehicleInfo(
            order.getVehicle().getVehicleId(),
            order.getVehicle().getLicensePlate(),
            order.getVehicle().getBrand(),
            order.getVehicle().getModel()
        ));

        // 转换工种枚举为字符串列表
        List<String> specialtyStrings = order.getFaultType().getRequiredSpecialties().stream()
            .map(specialty -> specialty.name().toLowerCase())
            .collect(Collectors.toList());

        dto.setFaultType(new OrderDTO.FaultTypeInfo(
            order.getFaultType().getFaultTypeId(),
            order.getFaultType().getTypeName(),
            specialtyStrings,
            order.getFaultType().getEstimatedHours()
        ));

        // 设置技师信息
        if (order.getAssignedTechnicians() != null && !order.getAssignedTechnicians().isEmpty()) {
            List<OrderDTO.TechnicianInfo> technicianInfos = order.getAssignedTechnicians().stream()
                .map(technician -> new OrderDTO.TechnicianInfo(
                    technician.getTechnicianId(),
                    technician.getRealName(),
                    technician.getSpecialty().name().toLowerCase(),
                    technician.getPhone()
                ))
                .collect(Collectors.toList());
            dto.setAssignedTechnicians(technicianInfos);
        }

        // 反馈信息
        if (order.getFeedback() != null) {
            OrderFeedback feedback = order.getFeedback();
            dto.setFeedback(new OrderDTO.FeedbackInfo(
                    feedback.getFeedbackId(),
                    feedback.getRating(),
                    feedback.getComment(),
                    feedback.getFeedbackTime()
            ));
        }

        return dto;
    }

    /**
     * 将OrderFeedback实体转换为FeedbackDTO
     */
    private FeedbackDTO convertFeedbackToDTO(OrderFeedback feedback) {
        RepairOrder order = feedback.getRepairOrder();

        // 构建车辆信息字符串
        String vehicleInfo = String.format("%s %s %s (%s)",
            order.getVehicle().getBrand(),
            order.getVehicle().getModel(),
            order.getVehicle().getYear(),
            order.getVehicle().getLicensePlate()
        );

        // 构建技师名称字符串
        String technicianNames = order.getAssignedTechnicians().stream()
            .map(technician -> technician.getRealName())
            .collect(Collectors.joining(", "));

        return new FeedbackDTO(
            feedback.getFeedbackId(),
            order.getOrderId(),
            feedback.getRating(),
            feedback.getComment(),
            feedback.getFeedbackTime(),
            order.getDescription(),
            order.getFaultType().getTypeName(),
            vehicleInfo,
            order.getSubmitTime(),
            order.getActualCompletionTime(),
            technicianNames
        );
    }
}
