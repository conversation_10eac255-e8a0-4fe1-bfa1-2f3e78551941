package com.example.controller;

import com.example.dto.request.FaultTypeRequest;
import com.example.dto.response.ApiResponse;
import com.example.dto.response.FaultTypeDTO;
import com.example.dto.response.PageResponse;
import com.example.service.FaultTypeService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 故障类型控制器
 */
@RestController
@RequestMapping("/fault-types")
public class FaultTypeController {

    @Autowired
    private FaultTypeService faultTypeService;

    /**
     * 获取故障类型列表
     */
    @GetMapping
    public ApiResponse<PageResponse<FaultTypeDTO>> getFaultTypes(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String specialty,
            @RequestParam(required = false) Integer status) {

        Pageable pageable = PageRequest.of(page - 1, size);
        PageResponse<FaultTypeDTO> faultTypes = faultTypeService.getFaultTypes(search, specialty, status, pageable);
        return ApiResponse.success("获取故障类型列表成功", faultTypes);
    }

    /**
     * 获取故障类型详情
     */
    @GetMapping("/{id}")
    public ApiResponse<FaultTypeDTO> getFaultType(@PathVariable Long id) {
        FaultTypeDTO faultType = faultTypeService.getFaultType(id);
        return ApiResponse.success("获取故障类型详情成功", faultType);
    }

    /**
     * 创建故障类型
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<FaultTypeDTO> createFaultType(@Valid @RequestBody FaultTypeRequest request) {
        FaultTypeDTO faultType = faultTypeService.createFaultType(request);
        return ApiResponse.success("创建故障类型成功", faultType);
    }

    /**
     * 更新故障类型
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<FaultTypeDTO> updateFaultType(@PathVariable Long id,
                                                    @Valid @RequestBody FaultTypeRequest request) {
        FaultTypeDTO faultType = faultTypeService.updateFaultType(id, request);
        return ApiResponse.success("更新故障类型成功", faultType);
    }

    /**
     * 删除故障类型
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> deleteFaultType(@PathVariable Long id) {
        faultTypeService.deleteFaultType(id);
        return ApiResponse.success("删除故障类型成功", null);
    }
}
