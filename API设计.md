# 车辆维修管理系统 API 文档 (完整设计版)

## 概述

本文档描述了车辆维修管理系统的完整 API 接口。系统支持三种用户角色：普通用户(user)、维修技师(technician)和管理员(admin)。

**基础URL**: `http://localhost:8080/api`

## 核心业务逻辑

### 工种分类体系
系统预定义8个标准工种，技师注册时只能选择其中一种：
- `engine` - 发动机维修
- `transmission` - 变速箱维修
- `brake` - 制动系统维修
- `electrical` - 电气系统维修
- `hvac` - 空调系统维修
- `chassis` - 底盘维修
- `body` - 车身维修
- `tire` - 轮胎维修

### 故障类型与工种对应关系
- **单工种故障**：只需要一个工种的技师处理
- **多工种故障**：需要多个不同工种的技师协作处理
- **技师分配逻辑**：系统根据故障类型的工种需求自动匹配并分配技师

### 管理员账户创建机制
- **系统初始化**：首次启动时创建默认管理员（admin/admin123）
- **管理员邀请**：现有管理员可创建新管理员账户
- **数据库直接操作**：运维人员可直接在数据库中创建

## 认证机制

使用JWT进行身份认证：
```
Authorization: Bearer <token>
```

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": <响应数据>,
  "timestamp": "2024-01-10T10:00:00Z",
  "code": 200
}
```

### 分页响应
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "content": [<数据列表>],
    "page": {
      "number": 1,
      "size": 20,
      "totalElements": 100,
      "totalPages": 5
    }
  },
  "timestamp": "2024-01-10T10:00:00Z",
  "code": 200
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误信息",
  "errors": [
    {
      "field": "specialty",
      "message": "工种必须从预定义列表中选择"
    }
  ],
  "timestamp": "2024-01-10T10:00:00Z",
  "code": 400,
  "path": "/api/technicians"
}
```

## API 接口列表

### 1. 认证相关 API

#### 1.1 用户登录
- **接口路径**: `POST /api/auth/login`
- **描述**: 用户、技师、管理员统一登录接口
- **使用场景**: 所有用户类型的登录认证
- **请求参数**:
```json
{
  "username": "用户名",
  "password": "密码",
  "userType": "用户类型(user/technician/admin)"
}
```
- **响应示例**:
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzUxMiJ9...",
    "user": {
      "userId": 1,
      "username": "testuser",
      "realName": "测试用户",
      "userType": "user"
    },
    "expiresIn": 86400
  }
}
```

#### 1.2 用户登出
- **接口路径**: `POST /api/auth/logout`
- **描述**: 用户登出，使token失效
- **认证**: 需要有效token
- **使用场景**: 用户主动退出系统

#### 1.3 刷新Token
- **接口路径**: `POST /api/auth/refresh`
- **描述**: 刷新即将过期的token
- **认证**: 需要有效token
- **使用场景**: 延长用户会话时间

### 2. 用户管理 API

#### 2.1 用户注册
- **接口路径**: `POST /api/users`
- **描述**: 普通用户注册
- **使用场景**: 新用户注册账户
- **请求参数**:
```json
{
  "username": "用户名",
  "password": "密码",
  "realName": "真实姓名",
  "phone": "13800138000",
  "email": "<EMAIL>",
  "address": "北京市朝阳区"
}
```

#### 2.2 获取当前用户信息
- **接口路径**: `GET /api/users/me`
- **描述**: 获取当前登录用户的详细信息
- **认证**: 需要用户token
- **使用场景**: 用户查看个人资料页面

#### 2.3 更新当前用户信息
- **接口路径**: `PUT /api/users/me`
- **描述**: 更新当前用户信息
- **认证**: 需要用户token
- **使用场景**: 用户修改个人资料

#### 2.4 获取当前用户车辆列表
- **接口路径**: `GET /api/users/me/vehicles`
- **描述**: 获取当前用户的车辆列表
- **认证**: 需要用户token
- **使用场景**: 用户查看"我的车辆"页面
- **查询参数**:
  - `page` (可选): 页码，默认1
  - `size` (可选): 每页大小，默认20

#### 2.5 获取当前用户工单列表
- **接口路径**: `GET /api/users/me/orders`
- **描述**: 获取当前用户的维修工单列表
- **认证**: 需要用户token
- **使用场景**: 用户查看"我的工单"页面
- **查询参数**:
  - `page` (可选): 页码
  - `size` (可选): 每页大小
  - `status` (可选): 工单状态过滤

#### 2.6 获取当前用户维修历史
- **接口路径**: `GET /api/users/me/repair-history`
- **描述**: 获取当前用户的历史维修记录
- **认证**: 需要用户token
- **使用场景**: 用户查看"维修历史"页面，了解车辆的维修记录
- **查询参数**:
  - `page` (可选): 页码，默认1
  - `size` (可选): 每页大小，默认20
  - `vehicleId` (可选): 按车辆过滤
  - `startDate` (可选): 开始日期
  - `endDate` (可选): 结束日期

#### 2.7 获取当前用户反馈历史
- **接口路径**: `GET /api/users/me/feedbacks`
- **描述**: 获取当前用户的反馈历史
- **认证**: 需要用户token
- **使用场景**: 用户查看历史评价记录

### 3. 技师管理 API

#### 3.1 技师注册
- **接口路径**: `POST /api/technicians`
- **描述**: 技师注册，工种必须从预定义列表中选择
- **使用场景**: 新技师入职注册
- **请求参数**:
```json
{
  "username": "tech001",
  "password": "password123",
  "realName": "技师张三",
  "phone": "13800138001",
  "email": "<EMAIL>",
  "specialty": "engine",
  "hourlyRate": 50.0,
  "hireDate": "2024-01-01"
}
```
- **specialty 可选值**:
  - `engine` - 发动机维修
  - `transmission` - 变速箱维修
  - `brake` - 制动系统维修
  - `electrical` - 电气系统维修
  - `hvac` - 空调系统维修
  - `chassis` - 底盘维修
  - `body` - 车身维修
  - `tire` - 轮胎维修

#### 3.2 获取工种列表
- **接口路径**: `GET /api/technicians/specialties`
- **描述**: 获取所有可用的工种列表
- **使用场景**: 技师注册时选择工种，前端下拉框数据源

#### 3.3 获取当前技师信息
- **接口路径**: `GET /api/technicians/me`
- **描述**: 获取当前登录技师的详细信息
- **认证**: 需要技师token
- **使用场景**: 技师查看个人资料页面

#### 3.4 更新当前技师信息
- **接口路径**: `PUT /api/technicians/me`
- **描述**: 更新当前技师信息（工种不可修改）
- **认证**: 需要技师token
- **使用场景**: 技师修改个人资料

#### 3.5 获取当前技师工单列表
- **接口路径**: `GET /api/technicians/me/orders`
- **描述**: 获取分配给当前技师的工单列表
- **认证**: 需要技师token
- **使用场景**: 技师查看"我的任务"页面

#### 3.6 获取当前技师工时费收入
- **接口路径**: `GET /api/technicians/me/payments`
- **描述**: 获取当前技师的工时费收入记录
- **认证**: 需要技师token
- **使用场景**: 技师查看"收入统计"页面

#### 3.7 获取当前技师历史记录
- **接口路径**: `GET /api/technicians/me/history`
- **描述**: 获取当前技师的历史维修记录
- **认证**: 需要技师token
- **使用场景**: 技师查看工作历史

### 4. 车辆管理 API

#### 4.1 添加车辆
- **接口路径**: `POST /api/vehicles`
- **描述**: 添加新车辆
- **认证**: 需要用户token
- **使用场景**: 用户添加新车辆到账户
- **请求参数**:
```json
{
  "licensePlate": "京A12345",
  "brand": "丰田",
  "model": "凯美瑞",
  "year": 2020,
  "vin": "1HGBH41JXMN109186",
  "color": "白色",
  "engineNumber": "ABC123456",
  "registerDate": "2020-01-01"
}
```

#### 4.2 获取车辆详情
- **接口路径**: `GET /api/vehicles/{id}`
- **描述**: 获取指定车辆的详细信息
- **认证**: 需要用户token，只能访问自己的车辆
- **使用场景**: 查看车辆详细信息

#### 4.3 更新车辆信息
- **接口路径**: `PUT /api/vehicles/{id}`
- **描述**: 更新车辆信息
- **认证**: 需要用户token，只能修改自己的车辆
- **使用场景**: 用户修改车辆信息

#### 4.4 删除车辆
- **接口路径**: `DELETE /api/vehicles/{id}`
- **描述**: 删除车辆
- **认证**: 需要用户token，只能删除自己的车辆
- **使用场景**: 用户不再使用某辆车时删除

### 5. 故障类型管理 API

#### 5.1 获取故障类型列表
- **接口路径**: `GET /api/fault-types`
- **描述**: 获取所有可用的故障类型列表，包含工种对应关系
- **认证**: 需要token
- **使用场景**: 用户提交工单时选择故障类型
- **查询参数**:
  - `status` (可选): 状态过滤，默认只返回启用的
- **响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "faultTypeId": 1,
      "typeName": "发动机故障",
      "description": "发动机系统故障维修，包括启动困难、异响、动力不足等",
      "requiredSpecialties": ["engine"],
      "requiredTechCount": 2,
      "estimatedHours": 4,
      "status": 1
    },
    {
      "faultTypeId": 3,
      "typeName": "综合电气故障",
      "description": "涉及多个电气系统的复杂故障",
      "requiredSpecialties": ["electrical", "engine"],
      "requiredTechCount": 2,
      "estimatedHours": 8,
      "status": 1
    }
  ]
}
```

#### 5.2 获取故障类型详情
- **接口路径**: `GET /api/fault-types/{id}`
- **描述**: 获取指定故障类型的详细信息
- **认证**: 需要token
- **使用场景**: 查看故障类型的详细描述和要求

#### 5.3 创建故障类型
- **接口路径**: `POST /api/fault-types`
- **描述**: 创建新的故障类型
- **认证**: 需要管理员token
- **使用场景**: 管理员添加新的故障类型
- **请求参数**:
```json
{
  "typeName": "综合底盘故障",
  "description": "涉及底盘和制动系统的复杂故障",
  "requiredSpecialties": ["chassis", "brake"],
  "requiredTechCount": 2,
  "estimatedHours": 6
}
```

#### 5.4 更新故障类型
- **接口路径**: `PUT /api/fault-types/{id}`
- **描述**: 更新故障类型信息
- **认证**: 需要管理员token
- **使用场景**: 管理员修改故障类型信息

#### 5.5 删除故障类型
- **接口路径**: `DELETE /api/fault-types/{id}`
- **描述**: 删除故障类型（软删除）
- **认证**: 需要管理员token
- **使用场景**: 管理员停用某个故障类型

### 6. 维修工单 API

#### 6.1 提交维修工单
- **接口路径**: `POST /api/orders`
- **描述**: 提交新的维修工单，系统根据故障类型自动分配技师
- **认证**: 需要用户token
- **使用场景**: 用户报修车辆故障
- **业务流程**:
  1. 用户选择车辆
  2. 用户选择故障类型（从 `/api/fault-types` 获取）
  3. 用户填写故障描述
  4. 系统根据故障类型的 `requiredSpecialties` 自动匹配技师
  5. 系统创建工单并分配给合适的技师
- **请求参数**:
```json
{
  "vehicleId": 1,
  "faultTypeId": 1,
  "description": "发动机启动困难，有异响",
  "urgencyLevel": "normal",
  "preferredTime": "2024-01-15T10:00:00Z",
  "contactPhone": "13800138000"
}
```

#### 6.2 获取工单详情
- **接口路径**: `GET /api/orders/{id}`
- **描述**: 获取指定工单的详细信息
- **认证**: 需要token，用户只能查看自己的工单，技师只能查看分配给自己的工单
- **使用场景**: 查看工单详细信息和进度

#### 6.3 更新工单状态
- **接口路径**: `PATCH /api/orders/{id}/status`
- **描述**: 更新工单状态（管理员专用）
- **认证**: 需要管理员token
- **使用场景**: 管理员手动调整工单状态
- **请求参数**:
```json
{
  "status": "cancelled",
  "reason": "用户取消维修"
}
```

#### 6.4 提交工单反馈
- **接口路径**: `POST /api/orders/{id}/feedback`
- **描述**: 用户对完成的工单提交反馈
- **认证**: 需要用户token，只能对自己的工单反馈
- **使用场景**: 维修完成后用户评价服务质量
- **请求参数**:
```json
{
  "rating": 5,
  "comment": "服务很好，技师专业"
}
```

#### 6.5 获取工单状态历史
- **接口路径**: `GET /api/orders/{id}/history`
- **描述**: 获取指定工单的状态变更历史记录
- **认证**: 需要token
- **使用场景**: 查看工单处理过程和时间线

#### 6.6 获取工单技师分配信息
- **接口路径**: `GET /api/orders/{id}/assignments`
- **描述**: 获取指定工单的技师分配信息
- **认证**: 需要token
- **使用场景**: 查看哪些技师被分配到此工单

### 7. 技师工单操作 API

#### 7.1 接受工单
- **接口路径**: `PATCH /api/orders/{id}/accept`
- **描述**: 技师接受指定工单
- **认证**: 需要技师token
- **使用场景**: 技师在"待接受任务"列表中接受工单

#### 7.2 拒绝工单
- **接口路径**: `PATCH /api/orders/{id}/reject`
- **描述**: 技师拒绝指定工单
- **认证**: 需要技师token
- **使用场景**: 技师因故无法处理某工单时拒绝
- **请求参数**:
```json
{
  "reason": "当前工作负载过重，无法及时处理"
}
```

#### 7.3 开始维修工作
- **接口路径**: `PATCH /api/orders/{id}/start`
- **描述**: 技师开始维修工作
- **认证**: 需要技师token
- **使用场景**: 技师到达现场开始维修时标记

#### 7.4 完成维修工作
- **接口路径**: `PATCH /api/orders/{id}/complete`
- **描述**: 技师完成维修工作
- **认证**: 需要技师token
- **使用场景**: 技师完成维修后提交工作结果
- **请求参数**:
```json
{
  "workingHours": 5.5,
  "workResult": "更换发动机机油，清洗节气门，故障已排除",
  "materialsUsed": [
    {
      "materialId": 1,
      "quantity": 4.0,
      "totalPrice": 200.0
    },
    {
      "materialId": 2,
      "quantity": 1.0,
      "totalPrice": 50.0
    }
  ]
}
```

### 8. 材料管理 API

#### 8.1 获取材料列表
- **接口路径**: `GET /api/materials`
- **描述**: 获取材料列表
- **认证**: 需要技师或管理员token
- **使用场景**: 技师查看可用材料，记录材料使用
- **查询参数**:
  - `page`, `size`: 分页参数
  - `search`: 搜索关键词
  - `category`: 材料分类过滤

#### 8.2 获取材料详情

- **接口路径**: `GET /api/materials/{id}`
- **描述**: 获取指定材料的详细信息
- **认证**: 需要技师或管理员token
- **使用场景**: 查看材料详细信息和库存

#### 8.3 添加材料
- **接口路径**: `POST /api/materials`
- **描述**: 添加新材料
- **认证**: 需要管理员token
- **使用场景**: 管理员添加新的维修材料

#### 8.4 更新材料信息
- **接口路径**: `PUT /api/materials/{id}`
- **描述**: 更新材料信息
- **认证**: 需要管理员token
- **使用场景**: 管理员修改材料信息和价格

#### 8.5 删除材料

- **接口路径**: `DELETE /api/materials/{id}`
- **描述**: 删除材料
- **认证**: 需要管理员token
- **使用场景**: 管理员停用某个材料

#### 8.6 获取材料分类



### 9. 工单材料使用 API

#### 9.1 添加材料使用记录
- **接口路径**: `POST /api/orders/{id}/materials`
- **描述**: 为工单添加材料使用记录
- **认证**: 需要技师token
- **使用场景**: 技师在维修过程中记录使用的材料
- **请求参数**:
```json
{
  "materialId": 1,
  "quantity": 2.0,
  "totalPrice": 100.0,
  "useTime": "2024-01-10T14:30:00Z"
}
```

#### 9.2 获取工单材料使用
- **接口路径**: `GET /api/orders/{id}/materials`
- **描述**: 获取指定工单的材料使用记录
- **认证**: 需要token
- **使用场景**: 查看工单的材料成本明细

### 10. 管理员 API

#### 10.1 创建管理员账户
- **接口路径**: `POST /api/admin/administrators`
- **描述**: 创建新的管理员账户
- **认证**: 需要超级管理员token
- **使用场景**: 现有管理员为新员工创建管理员账户
- **请求参数**:
```json
{
  "username": "admin002",
  "password": "password123",
  "realName": "管理员李四",
  "phone": "13800138002",
  "email": "<EMAIL>"
}
```

#### 10.2 获取所有用户
- **接口路径**: `GET /api/admin/users`
- **描述**: 获取所有用户信息
- **认证**: 需要管理员token
- **使用场景**: 管理员查看"用户管理"页面
- **查询参数**:
  - `page`, `size`: 分页参数
  - `search`: 搜索用户名或姓名
  - `status`: 状态过滤

#### 10.3 获取所有技师
- **接口路径**: `GET /api/admin/technicians`
- **描述**: 获取所有技师信息
- **认证**: 需要管理员token
- **使用场景**: 管理员查看"技师管理"页面
- **查询参数**:
  - `page`, `size`: 分页参数
  - `specialty`: 工种过滤
  - `status`: 状态过滤

#### 10.4 获取所有车辆
- **接口路径**: `GET /api/admin/vehicles`
- **描述**: 获取所有车辆信息
- **认证**: 需要管理员token
- **使用场景**: 管理员查看"车辆管理"页面
- **查询参数**:
  - `page`, `size`: 分页参数
  - `search`: 搜索车牌号或品牌
  - `brand`: 品牌过滤

#### 10.5 获取所有工单
- **接口路径**: `GET /api/admin/orders`
- **描述**: 获取所有维修工单信息
- **认证**: 需要管理员token
- **使用场景**: 管理员查看"工单管理"页面
- **查询参数**:
  - `page`, `size`: 分页参数
  - `status`: 工单状态过滤
  - `startDate`, `endDate`: 时间范围过滤
  - `specialty`: 按工种过滤

#### 10.6 删除用户
- **接口路径**: `DELETE /api/admin/users/{id}`
- **描述**: 删除指定用户
- **认证**: 需要管理员token
- **使用场景**: 管理员删除违规或不活跃用户

#### 10.7 删除技师
- **接口路径**: `DELETE /api/admin/technicians/{id}`
- **描述**: 删除指定技师
- **认证**: 需要管理员token
- **使用场景**: 技师离职时删除账户

### 11. 统计分析 API

#### 11.1 维修统计
- **接口路径**: `GET /api/analytics/repairs`
- **描述**: 获取按车型分组的维修次数和平均费用统计
- **认证**: 需要管理员token
- **使用场景**: 管理员查看"数据统计"页面
- **查询参数**:
  - `startDate`, `endDate`: 统计时间范围
  - `groupBy`: 分组方式（brand, model, faultType）
- **响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "brand": "丰田",
      "model": "凯美瑞",
      "repairCount": 15,
      "avgRepairCost": 1200.50,
      "totalCost": 18007.50
    }
  ]
}
```

#### 11.2 成本分析
- **接口路径**: `GET /api/analytics/costs`
- **描述**: 获取指定时间段的成本分析
- **认证**: 需要管理员token
- **使用场景**: 管理员查看成本构成分析
- **查询参数**:
  - `year` (必需): 年份
  - `quarter` (可选): 季度
- **响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "year": 2024,
      "quarter": 1,
      "totalLaborCost": 50000.0,
      "totalMaterialCost": 30000.0,
      "totalCost": 80000.0,
      "laborPercentage": 62.5,
      "materialPercentage": 37.5
    }
  ]
}
```

#### 11.3 工作负载统计
- **接口路径**: `GET /api/analytics/workload`
- **描述**: 获取不同工种的任务分配和完成情况统计
- **认证**: 需要管理员token
- **使用场景**: 管理员分析人力资源配置
- **查询参数**:
  - `startDate`, `endDate`: 统计时间范围
- **响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "specialty": "engine",
      "specialtyName": "发动机维修",
      "assignedCount": 25,
      "completedCount": 20,
      "assignmentPercentage": 35.5,
      "completionRate": 80.0,
      "avgWorkingHours": 5.2
    }
  ]
}
```

#### 11.4 故障模式统计
- **接口路径**: `GET /api/analytics/patterns`
- **描述**: 获取特定车型的故障模式统计
- **认证**: 需要管理员token
- **使用场景**: 管理员分析故障趋势
- **查询参数**:
  - `brand` (可选): 品牌
  - `model` (可选): 型号
- **响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "brand": "丰田",
      "model": "凯美瑞",
      "faultType": "发动机故障",
      "occurrenceCount": 8,
      "faultPercentage": 25.5
    }
  ]
}
```

#### 11.5 技师绩效统计
- **接口路径**: `GET /api/analytics/technician-performance`
- **描述**: 获取技师绩效统计
- **认证**: 需要管理员token
- **使用场景**: 管理员评估技师工作表现
- **查询参数**:
  - `specialty` (可选): 工种过滤
  - `startDate`, `endDate`: 统计时间范围
- **响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "technicianId": 1,
      "realName": "技师张三",
      "specialty": "engine",
      "completedOrders": 15,
      "totalWorkingHours": 75.5,
      "avgRating": 4.8,
      "totalEarnings": 3775.0
    }
  ]
}
```

## 数据模型

### 用户信息 (UserDTO)
```json
{
  "userId": 1,
  "username": "testuser",
  "realName": "测试用户",
  "phone": "13800138000",
  "email": "<EMAIL>",
  "address": "北京市朝阳区",
  "status": 1,
  "userType": "user",
  "createTime": "2024-01-01T00:00:00Z",
  "updateTime": "2024-01-01T00:00:00Z"
}
```

### 技师信息 (TechnicianDTO)
```json
{
  "technicianId": 1,
  "username": "tech001",
  "realName": "技师张三",
  "phone": "13800138001",
  "email": "<EMAIL>",
  "specialty": "engine",
  "specialtyName": "发动机维修",
  "hourlyRate": 50.0,
  "hireDate": "2024-01-01",
  "status": 1,
  "workload": 3,
  "rating": 4.8,
  "createTime": "2024-01-01T00:00:00Z",
  "updateTime": "2024-01-01T00:00:00Z"
}
```

### 车辆信息 (VehicleDTO)
```json
{
  "vehicleId": 1,
  "userId": 1,
  "licensePlate": "京A12345",
  "brand": "丰田",
  "model": "凯美瑞",
  "year": 2020,
  "vin": "1HGBH41JXMN109186",
  "color": "白色",
  "engineNumber": "ABC123456",
  "registerDate": "2020-01-01",
  "createTime": "2024-01-01T00:00:00Z",
  "updateTime": "2024-01-01T00:00:00Z"
}
```

### 故障类型信息 (FaultTypeDTO)
```json
{
  "faultTypeId": 1,
  "typeName": "发动机故障",
  "description": "发动机相关故障，包括启动困难、异响、动力不足等",
  "requiredSpecialties": ["engine"],
  "requiredTechCount": 2,
  "estimatedHours": 4,
  "status": 1,
  "createTime": "2024-01-01T00:00:00Z",
  "updateTime": "2024-01-01T00:00:00Z"
}
```

### 维修工单 (OrderDTO)
```json
{
  "orderId": 1,
  "userId": 1,
  "vehicleId": 1,
  "faultTypeId": 1,
  "description": "发动机异响",
  "urgencyLevel": "normal",
  "submitTime": "2024-01-10T09:00:00Z",
  "estimatedCompletionTime": "2024-01-15T17:00:00Z",
  "actualCompletionTime": null,
  "status": "assigned",
  "paymentStatus": "unpaid",
  "totalLaborCost": 0.0,
  "totalMaterialCost": 0.0,
  "totalCost": 0.0,
  "user": {
    "userId": 1,
    "username": "testuser",
    "realName": "测试用户",
    "phone": "13800138000"
  },
  "vehicle": {
    "vehicleId": 1,
    "licensePlate": "京A12345",
    "brand": "丰田",
    "model": "凯美瑞"
  },
  "faultType": {
    "faultTypeId": 1,
    "typeName": "发动机故障",
    "requiredSpecialties": ["engine"],
    "estimatedHours": 4
  },
  "assignedTechnicians": [
    {
      "technicianId": 1,
      "realName": "技师张三",
      "specialty": "engine",
      "phone": "13900139000"
    }
  ]
}
```

### 材料信息 (MaterialDTO)
```json
{
  "materialId": 1,
  "materialName": "机油",
  "specification": "5W-30",
  "unit": "升",
  "unitPrice": 50.0,
  "inventory": 100,
  "category": "润滑油",
  "status": 1,
  "createTime": "2024-01-01T00:00:00Z",
  "updateTime": "2024-01-01T00:00:00Z"
}
```

### 工时费记录 (LaborPaymentDTO)
```json
{
  "paymentId": 1,
  "technicianId": 1,
  "year": 2024,
  "month": 1,
  "totalHours": 160.0,
  "hourlyRate": 50.0,
  "totalAmount": 8000.0,
  "paymentStatus": "pending",
  "paymentTime": null,
  "createTime": "2024-01-31T23:59:59Z"
}
```

## 工单状态说明

| 状态 | 说明 | 用户可见 | 技师可见 | 管理员可见 |
|------|------|----------|----------|------------|
| pending | 待处理 | ✓ | ✓ | ✓ |
| assigned | 已分配 | ✓ | ✓ | ✓ |
| accepted | 已接受 | ✓ | ✓ | ✓ |
| in_progress | 进行中 | ✓ | ✓ | ✓ |
| completed | 已完成 | ✓ | ✓ | ✓ |
| cancelled | 已取消 | ✓ | ✓ | ✓ |

## 工种与故障类型对应表

| 故障类型 | 对应工种 | 所需技师数 | 预估工时 | 说明 |
|----------|----------|------------|----------|------|
| 发动机故障 | engine | 2 | 4-8小时 | 单工种，需要2名发动机技师 |
| 变速箱故障 | transmission | 2 | 6-12小时 | 单工种，需要2名变速箱技师 |
| 制动系统故障 | brake | 1 | 2-4小时 | 单工种，需要1名制动技师 |
| 电气系统故障 | electrical | 1 | 3-6小时 | 单工种，需要1名电气技师 |
| 空调系统故障 | hvac | 1 | 2-4小时 | 单工种，需要1名空调技师 |
| 底盘故障 | chassis | 2 | 4-8小时 | 单工种，需要2名底盘技师 |
| 车身损坏 | body | 1 | 4-16小时 | 单工种，需要1名车身技师 |
| 轮胎故障 | tire | 1 | 1-2小时 | 单工种，需要1名轮胎技师 |
| 综合电气故障 | electrical, engine | 2 | 6-10小时 | 多工种，需要电气和发动机技师各1名 |
| 底盘制动故障 | chassis, brake | 2 | 4-6小时 | 多工种，需要底盘和制动技师各1名 |

## 前端界面设计指导

### 用户端界面

**1. 登录页面**
- 使用 `POST /api/auth/login`，userType 选择 "user"
- 提供用户名、密码输入框和用户类型选择

**2. 个人中心**
- 使用 `GET /api/users/me` 获取用户信息
- 显示用户基本信息，提供编辑功能

**3. 我的车辆**
- 使用 `GET /api/users/me/vehicles` 展示车辆列表
- 提供添加、编辑、删除车辆功能
- 支持分页显示

**4. 我的工单**
- 使用 `GET /api/users/me/orders` 展示工单列表
- 支持按状态过滤
- 点击工单查看详情

**5. 提交工单**
- 首先获取用户车辆：`GET /api/users/me/vehicles`
- 获取故障类型：`GET /api/fault-types`
- 显示故障类型时包含：类型名称、描述、所需工种、预估工时
- 提交工单：`POST /api/orders`

**6. 工单详情**
- 使用 `GET /api/orders/{id}` 查看详情
- 显示分配的技师信息
- 显示工单状态历史
- 完成后可提交评价：`POST /api/orders/{id}/feedback`

**7. 维修历史**

- 使用 `GET /api/users/me/repair-history` 获取历史记录
- 支持按车辆、时间范围过滤
- 显示维修详情、费用明细、评价记录

**8. 反馈历史**

### 技师端界面

**1. 登录页面**
- userType 选择 "technician"

**2. 技师注册**
- 获取工种列表：`GET /api/technicians/specialties`
- 工种选择使用下拉框，显示工种名称和描述
- 工种选择后不可修改

**3. 工作台**
- 使用 `GET /api/technicians/me/orders` 获取任务列表
- 按状态分组显示：待接受、进行中、已完成
- 显示任务的故障类型、车辆信息、紧急程度

**4. 任务操作**
- 接受任务：`PATCH /api/orders/{id}/accept`
- 拒绝任务：`PATCH /api/orders/{id}/reject`
- 开始工作：`PATCH /api/orders/{id}/start`
- 完成工作：`PATCH /api/orders/{id}/complete`

**5. 材料记录**
- 查看可用材料：`GET /api/materials`
- 记录材料使用：`POST /api/orders/{id}/materials`

**6. 收入统计**
- 使用 `GET /api/technicians/me/payments` 查看收入
- 支持按年月过滤
- 显示工时统计和收入明细

### 管理员端界面

**1. 登录页面**
- userType 选择 "admin"

**2. 用户管理**
- 使用 `GET /api/admin/users` 获取用户列表
- 支持搜索、分页、状态过滤
- 提供删除用户功能

**3. 技师管理**
- 使用 `GET /api/admin/technicians` 获取技师列表
- 支持按工种过滤
- 显示技师工作负载和评分

**4. 故障类型管理**
- 使用 `GET /api/fault-types` 获取故障类型列表
- 创建故障类型时可选择多个工种
- 设置所需技师数量和预估工时

**5. 数据统计**
- 维修统计：`GET /api/analytics/repairs`
- 成本分析：`GET /api/analytics/costs`
- 工作负载：`GET /api/analytics/workload`
- 故障模式：`GET /api/analytics/patterns`
- 技师绩效：`GET /api/analytics/technician-performance`

**6. 工单管理**
- 使用 `GET /api/admin/orders` 获取所有工单
- 支持多维度过滤
- 可手动调整工单状态

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未认证或认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 422 | 业务逻辑错误 |
| 500 | 服务器内部错误 |

## 注意事项

1. **工种限制**：技师注册时只能选择预定义的8个工种之一，选择后不可修改
2. **故障类型分配**：系统根据故障类型的 `requiredSpecialties` 字段自动匹配技师
3. **多工种协作**：某些复杂故障需要多个不同工种的技师协作完成
4. **权限控制**：用户只能访问自己的数据，技师只能操作分配给自己的工单
5. **管理员权限**：管理员拥有所有数据的访问权限
6. **时间格式**：统一使用ISO 8601格式 (yyyy-MM-ddTHH:mm:ssZ)
7. **金额字段**：使用Double类型，保留两位小数
8. **状态字段**：使用字符串表示，具体值参考状态说明表
9. **分页参数**：page从1开始，size默认20，最大100
15. **数据验证**：
    - 车牌号必须符合中国车牌号格式规范
    - VIN码必须为17位字符，符合国际标准
    - 电话号码格式为11位数字，以1开头
    - 邮箱地址必须符合标准邮箱格式
    - 工种代码必须在预定义列表中
    - 故障类型的工种列表不能为空   
