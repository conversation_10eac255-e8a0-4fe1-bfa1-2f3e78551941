<template>
  <div class="profile-page">
    <div class="page-header">
      <h1>个人资料</h1>
      <p>管理您的个人信息</p>
    </div>

    <div class="profile-content">
      <!-- 基本信息卡片 -->
      <el-card class="profile-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-button
              v-if="!editing"
              type="primary"
              size="small"
              @click="startEdit"
            >
              编辑
            </el-button>
            <div v-else class="edit-buttons">
              <el-button size="small" @click="cancelEdit">取消</el-button>
              <el-button
                type="primary"
                size="small"
                :loading="saving"
                @click="saveProfile"
              >
                保存
              </el-button>
            </div>
          </div>
        </template>

        <div class="profile-info">
          <div class="avatar-section">
            <el-avatar :size="80" class="user-avatar">
              <el-icon size="40"><User /></el-icon>
            </el-avatar>
            <div class="user-basic">
              <h2>{{ userInfo?.realName || userInfo?.username }}</h2>
              <p class="user-type">普通用户</p>
            </div>
          </div>

          <el-form
            ref="profileFormRef"
            :model="profileForm"
            :rules="profileRules"
            label-width="100px"
            class="profile-form"
            :disabled="!editing"
          >
            <div class="form-grid">
              <el-form-item label="用户名" prop="username">
                <el-input
                  v-model="profileForm.username"
                  disabled
                  placeholder="用户名不可修改"
                />
              </el-form-item>

              <el-form-item label="真实姓名" prop="realName">
                <el-input
                  v-model="profileForm.realName"
                  placeholder="请输入真实姓名"
                  maxlength="20"
                />
              </el-form-item>

              <el-form-item label="手机号码" prop="phone">
                <el-input
                  v-model="profileForm.phone"
                  placeholder="请输入手机号码"
                  maxlength="11"
                />
              </el-form-item>

              <el-form-item label="邮箱地址" prop="email">
                <el-input
                  v-model="profileForm.email"
                  placeholder="请输入邮箱地址"
                  maxlength="50"
                />
              </el-form-item>
            </div>

            <el-form-item label="地址" prop="address">
              <el-input
                v-model="profileForm.address"
                type="textarea"
                :rows="3"
                placeholder="请输入详细地址"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-form>
        </div>
      </el-card>

      <!-- 账户统计 -->
      <el-card class="stats-card">
        <template #header>
          <span>账户统计</span>
        </template>

        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-icon vehicle">
              <el-icon size="24"><Van /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.vehicleCount }}</div>
              <div class="stat-label">车辆数量</div>
            </div>
          </div>

          <div class="stat-item">
            <div class="stat-icon order">
              <el-icon size="24"><Document /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.orderCount }}</div>
              <div class="stat-label">工单总数</div>
            </div>
          </div>

          <div class="stat-item">
            <div class="stat-icon completed">
              <el-icon size="24"><Check /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.completedCount }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </div>

          <div class="stat-item">
            <div class="stat-icon cost">
              <el-icon size="24"><Money /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">¥{{ stats.totalCost }}</div>
              <div class="stat-label">总花费</div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 最近活动 -->
      <el-card class="activity-card">
        <template #header>
          <span>最近活动</span>
        </template>

        <el-timeline>
          <el-timeline-item
            v-for="activity in recentActivities"
            :key="activity.id"
            :timestamp="formatDate(activity.time)"
            placement="top"
          >
            <div class="activity-content">
              <div class="activity-title">{{ activity.title }}</div>
              <div class="activity-desc">{{ activity.description }}</div>
            </div>
          </el-timeline-item>

          <el-timeline-item
            v-if="recentActivities.length === 0"
            timestamp="暂无活动记录"
            placement="top"
          >
            <el-empty description="暂无最近活动" :image-size="60" />
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  User, Van, Document, Check, Money
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { userAPI } from '@/api/user'
import dayjs from 'dayjs'

const authStore = useAuthStore()

// 响应式数据
const editing = ref(false)
const saving = ref(false)
const userInfo = ref(null)
const stats = reactive({
  vehicleCount: 0,
  orderCount: 0,
  completedCount: 0,
  totalCost: 0
})
const recentActivities = ref([])

// 表单引用
const profileFormRef = ref()

// 个人资料表单
const profileForm = reactive({
  username: '',
  realName: '',
  phone: '',
  email: '',
  address: ''
})

// 原始数据备份
const originalData = reactive({})

// 表单验证规则
const profileRules = {
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  address: [
    { required: true, message: '请输入地址', trigger: 'blur' },
    { min: 5, max: 200, message: '地址长度在 5 到 200 个字符', trigger: 'blur' }
  ]
}

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    const response = await userAPI.getCurrentUser()
    userInfo.value = response.data.data || response.data

    // 填充表单数据
    const userData = response.data.data || response.data
    Object.assign(profileForm, {
      username: userData.username,
      realName: userData.realName,
      phone: userData.phone,
      email: userData.email,
      address: userData.address
    })

    // 备份原始数据
    Object.assign(originalData, profileForm)
  } catch (error) {
    console.error('Failed to fetch user info:', error)
    ElMessage.error('获取用户信息失败')
  }
}

// 获取统计数据
const fetchStats = async () => {
  try {
    // 获取车辆数量
    const vehiclesResponse = await userAPI.getCurrentUserVehicles({ page: 1, size: 1 })
    stats.vehicleCount = vehiclesResponse.data.data?.page?.totalElements || 0

    // 获取工单统计
    const ordersResponse = await userAPI.getCurrentUserOrders({ page: 1, size: 1 })
    stats.orderCount = ordersResponse.data.data?.page?.totalElements || 0

    // 获取已完成工单数量
    const completedResponse = await userAPI.getCurrentUserOrders({
      page: 1,
      size: 1,
      status: 'completed'
    })
    stats.completedCount = completedResponse.data.data?.page?.totalElements || 0

    // 计算总花费（这里需要根据实际API调整）
    stats.totalCost = 0 // 暂时设为0，需要后端提供相应接口
  } catch (error) {
    console.error('Failed to fetch stats:', error)
  }
}

// 获取最近活动
const fetchRecentActivities = async () => {
  try {
    // 获取最近的工单作为活动记录
    const response = await userAPI.getCurrentUserOrders({ page: 1, size: 5 })
    const orders = response.data?.content || []

    recentActivities.value = orders.map(order => ({
      id: order.orderId,
      title: `工单 #${order.orderId}`,
      description: `${order.vehicle?.licensePlate} - ${order.faultType?.typeName}`,
      time: order.submitTime
    }))
  } catch (error) {
    console.error('Failed to fetch recent activities:', error)
  }
}

// 开始编辑
const startEdit = () => {
  editing.value = true
}

// 取消编辑
const cancelEdit = () => {
  editing.value = false
  // 恢复原始数据
  Object.assign(profileForm, originalData)
}

// 保存个人资料
const saveProfile = async () => {
  if (!profileFormRef.value) return

  try {
    await profileFormRef.value.validate()
    saving.value = true

    const { username, ...updateData } = profileForm
    await userAPI.updateCurrentUser(updateData)

    // 更新store中的用户信息
    authStore.updateUser(updateData)

    // 备份新数据
    Object.assign(originalData, profileForm)

    editing.value = false
    ElMessage.success('个人资料更新成功')

    // 重新获取用户信息
    fetchUserInfo()
  } catch (error) {
    console.error('Failed to update profile:', error)
  } finally {
    saving.value = false
  }
}

// 格式化日期
const formatDate = (dateString) => {
  return dayjs(dateString).format('MM-DD HH:mm')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchUserInfo()
  fetchStats()
  fetchRecentActivities()
})
</script>

<style scoped>
.profile-page {
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.profile-content {
  display: grid;
  gap: 20px;
}

/* 个人资料卡片 */
.profile-card {
  grid-column: 1 / -1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.edit-buttons {
  display: flex;
  gap: 10px;
}

.profile-info {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.avatar-section {
  display: flex;
  align-items: center;
  gap: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.user-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.user-basic h2 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.user-type {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.profile-form {
  flex: 1;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

/* 统计卡片 */
.stats-card {
  grid-column: 1 / -1;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.vehicle {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.order {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.cost {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  line-height: 1;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

/* 活动卡片 */
.activity-card {
  grid-column: 1 / -1;
}

.activity-content {
  margin-bottom: 10px;
}

.activity-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.activity-desc {
  font-size: 14px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-page {
    padding: 0 15px;
  }

  .page-header h1 {
    font-size: 20px;
  }

  .avatar-section {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .stat-item {
    padding: 15px;
  }

  .stat-number {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .edit-buttons {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
