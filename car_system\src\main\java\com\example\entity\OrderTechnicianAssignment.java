package com.example.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 工单技师分配实体类
 * 用于管理工单与技师的分配关系，包括技师同意状态
 */
@Entity
@Table(name = "order_technician_assignments")
public class OrderTechnicianAssignment extends BaseEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "assignment_id")
    private Long assignmentId;
    
    // 多对一关系：分配记录属于一个工单
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id", nullable = false)
    private RepairOrder repairOrder;
    
    // 多对一关系：分配记录对应一个技师
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "technician_id", nullable = false)
    private Technician technician;
    
    // 技师同意状态
    @NotNull(message = "同意状态不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "agreement_status", nullable = false, length = 20)
    private AgreementStatus agreementStatus = AgreementStatus.PENDING;
    
    // 分配时间
    @NotNull(message = "分配时间不能为空")
    @Column(name = "assigned_time", nullable = false)
    private LocalDateTime assignedTime;
    
    // 同意/拒绝时间
    @Column(name = "response_time")
    private LocalDateTime responseTime;
    
    // 拒绝原因
    @Column(name = "reject_reason", length = 500)
    private String rejectReason;
    
    // 枚举：技师同意状态
    public enum AgreementStatus {
        PENDING("待确认"),
        ACCEPTED("已同意"),
        REJECTED("已拒绝");
        
        private final String displayName;
        
        AgreementStatus(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
    
    // 构造函数
    public OrderTechnicianAssignment() {}
    
    public OrderTechnicianAssignment(RepairOrder repairOrder, Technician technician) {
        this.repairOrder = repairOrder;
        this.technician = technician;
        this.assignedTime = LocalDateTime.now();
        this.agreementStatus = AgreementStatus.PENDING;
    }
    
    // Getters and Setters
    public Long getAssignmentId() {
        return assignmentId;
    }
    
    public void setAssignmentId(Long assignmentId) {
        this.assignmentId = assignmentId;
    }
    
    public RepairOrder getRepairOrder() {
        return repairOrder;
    }
    
    public void setRepairOrder(RepairOrder repairOrder) {
        this.repairOrder = repairOrder;
    }
    
    public Technician getTechnician() {
        return technician;
    }
    
    public void setTechnician(Technician technician) {
        this.technician = technician;
    }
    
    public AgreementStatus getAgreementStatus() {
        return agreementStatus;
    }
    
    public void setAgreementStatus(AgreementStatus agreementStatus) {
        this.agreementStatus = agreementStatus;
    }
    
    public LocalDateTime getAssignedTime() {
        return assignedTime;
    }
    
    public void setAssignedTime(LocalDateTime assignedTime) {
        this.assignedTime = assignedTime;
    }
    
    public LocalDateTime getResponseTime() {
        return responseTime;
    }
    
    public void setResponseTime(LocalDateTime responseTime) {
        this.responseTime = responseTime;
    }
    
    public String getRejectReason() {
        return rejectReason;
    }
    
    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }
}
