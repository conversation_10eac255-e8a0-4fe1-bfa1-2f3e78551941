import api from './index'

export const userAPI = {
  // 用户注册
  register(data) {
    return api.post('/users', data)
  },

  // 获取当前用户信息
  getCurrentUser() {
    return api.get('/users/me')
  },

  // 更新当前用户信息
  updateCurrentUser(data) {
    return api.put('/users/me', data)
  },

  // 获取当前用户车辆列表
  getCurrentUserVehicles(params = {}) {
    return api.get('/users/me/vehicles', { params })
  },

  // 获取当前用户工单列表
  getCurrentUserOrders(params = {}) {
    return api.get('/users/me/orders', { params })
  },

  // 获取当前用户维修历史
  getCurrentUserRepairHistory(params = {}) {
    return api.get('/users/me/repair-history', { params })
  },

  // 获取当前用户反馈历史
  getCurrentUserFeedbacks(params = {}) {
    return api.get('/users/me/feedbacks', { params })
  }
}
