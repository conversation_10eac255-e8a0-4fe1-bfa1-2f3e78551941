package com.example.dto.request;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 材料请求DTO
 */
public class MaterialRequest {
    
    @NotBlank(message = "材料名称不能为空")
    @Size(max = 100, message = "材料名称长度不能超过100个字符")
    private String materialName;
    
    @Size(max = 100, message = "规格长度不能超过100个字符")
    private String specification;
    
    @NotBlank(message = "单位不能为空")
    @Size(max = 20, message = "单位长度不能超过20个字符")
    private String unit;
    
    @NotNull(message = "单价不能为空")
    @DecimalMin(value = "0.01", message = "单价必须大于0")
    private BigDecimal unitPrice;
    
    @NotNull(message = "库存数量不能为空")
    @Min(value = 0, message = "库存数量不能为负数")
    private Integer inventory;
    
    @Size(max = 50, message = "分类长度不能超过50个字符")
    private String category;
    
    // 构造函数
    public MaterialRequest() {}
    
    public MaterialRequest(String materialName, String specification, String unit, 
                          BigDecimal unitPrice, Integer inventory, String category) {
        this.materialName = materialName;
        this.specification = specification;
        this.unit = unit;
        this.unitPrice = unitPrice;
        this.inventory = inventory;
        this.category = category;
    }
    
    // Getters and Setters
    public String getMaterialName() {
        return materialName;
    }
    
    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }
    
    public String getSpecification() {
        return specification;
    }
    
    public void setSpecification(String specification) {
        this.specification = specification;
    }
    
    public String getUnit() {
        return unit;
    }
    
    public void setUnit(String unit) {
        this.unit = unit;
    }
    
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }
    
    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }
    
    public Integer getInventory() {
        return inventory;
    }
    
    public void setInventory(Integer inventory) {
        this.inventory = inventory;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
}
