<template>
  <div class="order-detail-page">
    <div class="page-header">
      <div class="header-left">
        <el-button @click="$router.go(-1)">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <h1>工单详情 #{{ orderId }}</h1>
      </div>
      <div class="header-right">
        <el-tag :type="getStatusType(orderDetail?.status)" size="large">
          {{ getStatusText(orderDetail?.status) }}
        </el-tag>
      </div>
    </div>

    <div v-loading="loading" class="detail-content">
      <div v-if="orderDetail" class="detail-grid">
        <!-- 基本信息 -->
        <el-card class="info-card">
          <template #header>
            <span>基本信息</span>
          </template>

          <el-descriptions :column="2" border>
            <el-descriptions-item label="工单号">
              {{ orderDetail.orderId }}
            </el-descriptions-item>
            <el-descriptions-item label="提交时间">
              {{ formatDate(orderDetail.submitTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="紧急程度">
              <el-tag :type="getUrgencyType(orderDetail.urgencyLevel)" size="small">
                {{ getUrgencyText(orderDetail.urgencyLevel) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="期望维修时间">
              {{ formatDate(orderDetail.preferredTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="预估完成时间">
              {{ formatDate(orderDetail.estimatedCompletionTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="联系电话">
              {{ orderDetail.contactPhone }}
            </el-descriptions-item>
            <el-descriptions-item label="当前状态">
              <el-tag :type="getStatusType(orderDetail.status)">
                {{ getStatusText(orderDetail.status) }}
              </el-tag>
            </el-descriptions-item>

            <el-descriptions-item label="故障描述" :span="2">
              {{ orderDetail.description }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 车辆信息 -->
        <el-card class="vehicle-card">
          <template #header>
            <span>车辆信息</span>
          </template>

          <div v-if="orderDetail.vehicle" class="vehicle-info">
            <div class="vehicle-header">
              <div class="license-plate">{{ orderDetail.vehicle.licensePlate }}</div>
              <div class="vehicle-brand">
                {{ orderDetail.vehicle.brand }} {{ orderDetail.vehicle.model }}
              </div>
            </div>

            <el-descriptions :column="2" border>
              <el-descriptions-item label="车辆ID">
                {{ orderDetail.vehicle.vehicleId }}
              </el-descriptions-item>
              <el-descriptions-item label="车牌号">
                {{ orderDetail.vehicle.licensePlate }}
              </el-descriptions-item>
              <el-descriptions-item label="品牌">
                {{ orderDetail.vehicle.brand }}
              </el-descriptions-item>
              <el-descriptions-item label="型号">
                {{ orderDetail.vehicle.model }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>

        <!-- 故障类型信息 -->
        <el-card class="fault-type-card">
          <template #header>
            <span>故障类型</span>
          </template>

          <div v-if="orderDetail.faultType" class="fault-type-info">
            <h3>{{ orderDetail.faultType.typeName }}</h3>

            <el-descriptions :column="2" border>
              <el-descriptions-item label="故障类型ID">
                {{ orderDetail.faultType.faultTypeId }}
              </el-descriptions-item>
              <el-descriptions-item label="预估工时">
                {{ orderDetail.faultType.estimatedHours }} 小时
              </el-descriptions-item>
              <el-descriptions-item label="所需工种" :span="2">
                <el-tag
                  v-for="specialty in orderDetail.faultType.requiredSpecialties"
                  :key="specialty"
                  size="small"
                  style="margin-right: 5px"
                >
                  {{ getSpecialtyName(specialty) }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>

        <!-- 分配技师信息 -->
        <el-card v-if="orderDetail.assignedTechnicians && orderDetail.assignedTechnicians.length > 0" class="technician-card">
          <template #header>
            <span>分配技师</span>
          </template>

          <div class="technician-list">
            <div
              v-for="technician in orderDetail.assignedTechnicians"
              :key="technician.technicianId"
              class="technician-item"
            >
              <div class="technician-info">
                <div class="technician-name">{{ technician.realName }}</div>
                <div class="technician-specialty">{{ getSpecialtyName(technician.specialty) }}</div>
                <div class="technician-phone">联系电话: {{ technician.phone }}</div>
              </div>
              <div class="technician-id">
                <el-tag size="small">ID: {{ technician.technicianId }}</el-tag>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 材料使用信息 (仅技师和管理员可见) -->
        <MaterialUsageCard
          v-if="canViewMaterialUsage && orderDetail.materialUsages"
          :material-usages="orderDetail.materialUsages"
          class="material-usage-section"
        />

        <!-- 费用信息 -->
        <el-card class="cost-card">
          <template #header>
            <span>费用信息</span>
          </template>

          <el-descriptions :column="2" border>
            <el-descriptions-item label="人工费">
              {{ formatCost(orderDetail.totalLaborCost, orderDetail.status) }}
            </el-descriptions-item>
            <el-descriptions-item label="材料费">
              {{ formatCost(orderDetail.totalMaterialCost, orderDetail.status) }}
            </el-descriptions-item>
            <el-descriptions-item label="总费用" :span="2">
              <span class="total-cost">{{ formatCost(orderDetail.totalCost, orderDetail.status) }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 用户反馈 -->
        <el-card v-if="orderDetail.feedback" class="feedback-card">
          <template #header>
            <span>用户反馈</span>
          </template>

          <div class="feedback-content">
            <div class="feedback-rating">
              <span>评分: </span>
              <el-rate
                v-model="orderDetail.feedback.rating"
                disabled
                show-score
                text-color="#ff9900"
              />
            </div>
            <div class="feedback-text">
              <p>{{ orderDetail.feedback.content }}</p>
            </div>
            <div class="feedback-time">
              反馈时间: {{ formatDate(orderDetail.feedback.feedbackTime) }}
            </div>
          </div>
        </el-card>

        <!-- 催单信息 -->
        <el-card v-if="orderDetail.urgentRequests && orderDetail.urgentRequests.length > 0" class="urgent-card">
          <template #header>
            <span>催单记录</span>
          </template>

          <div class="urgent-list">
            <div
              v-for="urgent in orderDetail.urgentRequests"
              :key="urgent.urgentId"
              class="urgent-item"
            >
              <div class="urgent-content">
                <div class="urgent-reason">{{ urgent.reason }}</div>
                <div class="urgent-time">催单时间: {{ formatDate(urgent.urgentTime) }}</div>
              </div>
              <div class="urgent-status">
                <el-tag :type="urgent.status === 'pending' ? 'warning' : 'success'" size="small">
                  {{ urgent.statusDisplayName }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 操作按钮 -->
      <div v-if="orderDetail" class="action-buttons">
        <el-button
          v-if="canUrgent(orderDetail.status)"
          type="danger"
          @click="showUrgentDialog"
        >
          催单
        </el-button>
        <el-button
          v-if="canFeedback(orderDetail.status)"
          type="warning"
          @click="showFeedbackDialog"
        >
          提交评价
        </el-button>
        <div
          v-else-if="orderDetail.status === 'completed' && orderDetail.feedback"
          class="feedback-status"
          style="color: #67c23a; font-size: 14px; text-align: center;"
        >
          <i class="el-icon-check"></i>
          此订单已评价
        </div>
      </div>
    </div>

    <!-- 反馈对话框 -->
    <el-dialog
      v-model="feedbackDialog.visible"
      title="工单评价"
      width="500px"
      @close="resetFeedbackForm"
    >
      <el-form
        ref="feedbackFormRef"
        :model="feedbackForm"
        :rules="feedbackRules"
        label-width="80px"
      >
        <el-form-item label="评分" prop="rating">
          <el-rate
            v-model="feedbackForm.rating"
            :max="5"
            show-text
            :texts="['极差', '失望', '一般', '满意', '惊喜']"
          />
        </el-form-item>

        <el-form-item label="评价内容" prop="content">
          <el-input
            v-model="feedbackForm.content"
            type="textarea"
            :rows="4"
            placeholder="请描述您对本次维修服务的评价"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="feedbackDialog.visible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="feedbackSubmitting"
          @click="submitFeedback"
        >
          提交评价
        </el-button>
      </template>
    </el-dialog>

    <!-- 催单对话框 -->
    <el-dialog
      v-model="urgentDialog.visible"
      title="工单催单"
      width="500px"
      @close="resetUrgentForm"
    >
      <el-form
        ref="urgentFormRef"
        :model="urgentForm"
        :rules="urgentRules"
        label-width="80px"
      >
        <el-form-item label="催单原因" prop="reason">
          <el-input
            v-model="urgentForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请说明催单原因，如：急需用车、工期紧急等"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="urgentDialog.visible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="urgentSubmitting"
          @click="submitUrgent"
        >
          提交催单
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { orderAPI } from '@/api/order'
import { useAuthStore } from '@/stores/auth'
import MaterialUsageCard from '@/components/MaterialUsageCard.vue'
import dayjs from 'dayjs'

const route = useRoute()
const authStore = useAuthStore()

// 获取路由参数
const orderId = route.params.id

// 响应式数据
const loading = ref(false)
const feedbackSubmitting = ref(false)
const urgentSubmitting = ref(false)
const orderDetail = ref(null)

// 表单引用
const feedbackFormRef = ref()
const urgentFormRef = ref()

// 反馈对话框
const feedbackDialog = reactive({
  visible: false
})

// 反馈表单
const feedbackForm = reactive({
  rating: 5,
  content: ''
})

// 反馈表单验证规则
const feedbackRules = {
  rating: [
    { required: true, message: '请选择评分', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入评价内容', trigger: 'blur' },
    { min: 10, message: '评价内容至少10个字符', trigger: 'blur' }
  ]
}

// 催单对话框
const urgentDialog = reactive({
  visible: false
})

// 催单表单
const urgentForm = reactive({
  reason: ''
})

// 催单表单验证规则
const urgentRules = {
  reason: [
    { required: true, message: '请输入催单原因', trigger: 'blur' },
    { min: 5, message: '催单原因至少5个字符', trigger: 'blur' }
  ]
}

// 工种名称映射
const specialtyNames = {
  engine: '发动机维修',
  transmission: '变速箱维修',
  brake: '制动系统维修',
  electrical: '电气系统维修',
  hvac: '空调系统维修',
  chassis: '底盘维修',
  body: '车身维修',
  tire: '轮胎维修'
}

// 计算属性：是否可以查看材料使用信息
const canViewMaterialUsage = computed(() => {
  return authStore.isTechnician || authStore.isAdmin
})



// 获取工单详情
const fetchOrderDetail = async () => {
  try {
    loading.value = true
    const response = await orderAPI.getById(orderId)
    orderDetail.value = response.data
  } catch (error) {
    console.error('Failed to fetch order detail:', error)
    ElMessage.error('获取工单详情失败')
  } finally {
    loading.value = false
  }
}





// 显示反馈对话框
const showFeedbackDialog = () => {
  feedbackDialog.visible = true
}

// 提交反馈
const submitFeedback = async () => {
  if (!feedbackFormRef.value) return

  try {
    await feedbackFormRef.value.validate()
    feedbackSubmitting.value = true

    await orderAPI.submitFeedback(orderId, feedbackForm)

    ElMessage.success('评价提交成功')
    feedbackDialog.visible = false

    // 重新获取工单详情
    fetchOrderDetail()
  } catch (error) {
    console.error('Failed to submit feedback:', error)
  } finally {
    feedbackSubmitting.value = false
  }
}

// 重置反馈表单
const resetFeedbackForm = () => {
  Object.assign(feedbackForm, {
    rating: 5,
    content: ''
  })
  if (feedbackFormRef.value) {
    feedbackFormRef.value.resetFields()
  }
}

// 判断是否可以评价
const canFeedback = (status) => {
  return status === 'completed' && !orderDetail.value?.feedback
}

// 判断是否可以催单
const canUrgent = (status) => {
  return status === 'in_progress'
}

// 显示催单对话框
const showUrgentDialog = () => {
  urgentDialog.visible = true
}

// 提交催单
const submitUrgent = async () => {
  if (!urgentFormRef.value) return

  try {
    await urgentFormRef.value.validate()
    urgentSubmitting.value = true

    await orderAPI.submitUrgent(orderId, urgentForm)

    ElMessage.success('催单提交成功')
    urgentDialog.visible = false

    // 重新获取工单详情
    fetchOrderDetail()
  } catch (error) {
    console.error('Failed to submit urgent request:', error)
    ElMessage.error('催单提交失败')
  } finally {
    urgentSubmitting.value = false
  }
}

// 重置催单表单
const resetUrgentForm = () => {
  Object.assign(urgentForm, {
    reason: ''
  })
  if (urgentFormRef.value) {
    urgentFormRef.value.resetFields()
  }
}

// 获取工种名称
const getSpecialtyName = (specialty) => {
  return specialtyNames[specialty] || specialty
}

// 获取紧急程度类型
const getUrgencyType = (level) => {
  const typeMap = {
    low: 'info',
    normal: 'primary',
    high: 'warning',
    urgent: 'danger'
  }
  return typeMap[level] || 'info'
}

// 获取紧急程度文本
const getUrgencyText = (level) => {
  const textMap = {
    low: '低',
    normal: '中',
    high: '高',
    urgent: '紧急'
  }
  return textMap[level] || level
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    pending: 'warning',
    assigned: 'info',
    accepted: 'primary',
    in_progress: 'warning',
    completed: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    pending: '待处理',
    assigned: '已分配',
    accepted: '已接受',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}



// 格式化费用显示
const formatCost = (cost, status) => {
  // 如果订单未完成，显示"待结算"
  if (status !== 'completed') {
    return '待结算'
  }
  // 如果订单已完成，显示实际费用
  const numericCost = parseFloat(cost) || 0
  return `¥${numericCost.toFixed(2)}`
}





// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未设置'
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchOrderDetail()
})
</script>

<style scoped>
.order-detail-page {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-left h1 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.detail-grid {
  display: grid;
  gap: 20px;
  margin-bottom: 30px;
}

.info-card,
.vehicle-card,
.fault-type-card,
.technician-card,
.material-usage-section,
.cost-card,
.feedback-card,
.urgent-card {
  grid-column: 1 / -1;
}

/* 车辆信息样式 */
.vehicle-header {
  margin-bottom: 20px;
  text-align: center;
}

.license-plate {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 5px;
}

.vehicle-brand {
  font-size: 16px;
  color: #666;
}

/* 故障类型样式 */
.fault-type-info h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 18px;
}

.fault-description {
  margin: 0 0 20px 0;
  color: #666;
  line-height: 1.6;
}

/* 技师列表样式 */
.technician-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.technician-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.technician-info {
  flex: 1;
}

.technician-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.technician-specialty {
  font-size: 14px;
  color: #666;
  margin-bottom: 3px;
}

.technician-phone {
  font-size: 12px;
  color: #999;
}



/* 费用信息样式 */
.total-cost {
  font-size: 18px;
  font-weight: 600;
  color: #e6a23c;
}



/* 反馈样式 */
.feedback-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.feedback-rating {
  display: flex;
  align-items: center;
  gap: 10px;
}

.feedback-text p {
  margin: 0;
  line-height: 1.6;
  color: #333;
}

.feedback-time {
  font-size: 14px;
  color: #999;
}

/* 催单样式 */
.urgent-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.urgent-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #fef0f0;
  border-radius: 8px;
  border-left: 4px solid #f56c6c;
}

.urgent-content {
  flex: 1;
}

.urgent-reason {
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
  line-height: 1.4;
}

.urgent-time {
  font-size: 14px;
  color: #999;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .header-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .header-left h1 {
    font-size: 20px;
  }

  .technician-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }



  .action-buttons {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .vehicle-header {
    text-align: left;
  }

  .license-plate {
    font-size: 20px;
  }


}
</style>
