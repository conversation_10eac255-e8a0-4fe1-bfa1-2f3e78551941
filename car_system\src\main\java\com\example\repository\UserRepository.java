package com.example.repository;

import com.example.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long>, JpaSpecificationExecutor<User> {

    /**
     * 根据用户名查找用户
     */
    Optional<User> findByUsername(String username);

    /**
     * 根据用户名和用户类型查找用户
     */
    Optional<User> findByUsernameAndUserType(String username, User.UserType userType);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查手机号是否存在
     */
    boolean existsByPhone(String phone);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 根据状态查找用户
     */
    Page<User> findByStatus(Integer status, Pageable pageable);

    /**
     * 根据用户类型查找用户
     */
    Page<User> findByUserType(User.UserType userType, Pageable pageable);

    /**
     * 根据用户类型和状态查找用户
     */
    Page<User> findByUserTypeAndStatus(User.UserType userType, Integer status, Pageable pageable);

    /**
     * 搜索用户（根据用户名或真实姓名）
     */
    @Query("SELECT u FROM User u WHERE " +
           "(LOWER(u.username) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(u.realName) LIKE LOWER(CONCAT('%', :search, '%'))) AND " +
           "u.userType = :userType")
    Page<User> searchByUsernameOrRealNameAndUserType(@Param("search") String search,
                                                     @Param("userType") User.UserType userType,
                                                     Pageable pageable);

    /**
     * 搜索用户（根据用户名或真实姓名，包含状态过滤）
     */
    @Query("SELECT u FROM User u WHERE " +
           "(LOWER(u.username) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(u.realName) LIKE LOWER(CONCAT('%', :search, '%'))) AND " +
           "u.userType = :userType AND u.status = :status")
    Page<User> searchByUsernameOrRealNameAndUserTypeAndStatus(@Param("search") String search,
                                                              @Param("userType") User.UserType userType,
                                                              @Param("status") Integer status,
                                                              Pageable pageable);
}
