<template>
  <div class="feedback-history">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>反馈历史</h2>
      <p class="page-description">查看您的历史评价记录</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6">
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon><Star /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.totalFeedbacks }}</div>
              <div class="stat-label">总评价数</div>
            </div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="6">
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.avgRating.toFixed(1) }}</div>
              <div class="stat-label">平均评分</div>
            </div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="6">
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.thisMonthFeedbacks }}</div>
              <div class="stat-label">本月评价</div>
            </div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="6">
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon><Trophy /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.fiveStarCount }}</div>
              <div class="stat-label">五星评价</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-section">
      <el-card>
        <el-form :model="filters" inline>
          <el-form-item label="评分">
            <el-select v-model="filters.rating" placeholder="选择评分" clearable style="width: 120px">
              <el-option label="5星" :value="5" />
              <el-option label="4星" :value="4" />
              <el-option label="3星" :value="3" />
              <el-option label="2星" :value="2" />
              <el-option label="1星" :value="1" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="filters.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 240px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="fetchFeedbacks" :icon="Search">搜索</el-button>
            <el-button @click="resetFilters" :icon="Refresh">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 反馈列表 -->
    <div class="feedback-list">
      <el-card v-loading="loading">
        <div v-if="feedbacks.length === 0" class="empty-state">
          <el-empty description="暂无反馈记录" />
        </div>
        <div v-else>
          <div v-for="feedback in feedbacks" :key="feedback.feedbackId" class="feedback-item">
            <div class="feedback-header">
              <div class="feedback-info">
                <h4>{{ feedback.faultTypeName }}</h4>
                <p class="vehicle-info">{{ feedback.vehicleInfo }}</p>
              </div>
              <div class="feedback-rating">
                <el-rate
                  v-model="feedback.rating"
                  disabled
                  show-score
                  text-color="#ff9900"
                />
              </div>
            </div>

            <div class="feedback-content">
              <p class="feedback-comment">{{ feedback.comment }}</p>
            </div>

            <div class="feedback-details">
              <div class="detail-item">
                <span class="label">工单描述：</span>
                <span class="value">{{ feedback.orderDescription }}</span>
              </div>
              <div class="detail-item">
                <span class="label">维修技师：</span>
                <span class="value">{{ feedback.technicianNames }}</span>
              </div>
              <div class="detail-item">
                <span class="label">提交时间：</span>
                <span class="value">{{ formatDate(feedback.orderSubmitTime) }}</span>
              </div>
              <div class="detail-item">
                <span class="label">完成时间：</span>
                <span class="value">{{ formatDate(feedback.orderCompletionTime) }}</span>
              </div>
              <div class="detail-item">
                <span class="label">评价时间：</span>
                <span class="value">{{ formatDate(feedback.feedbackTime) }}</span>
              </div>
            </div>

            <div class="feedback-actions">
              <el-button type="primary" link @click="viewOrderDetail(feedback.orderId)">
                查看工单详情
              </el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="fetchFeedbacks"
        @current-change="fetchFeedbacks"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Star, TrendCharts, Calendar, Trophy, Search, Refresh
} from '@element-plus/icons-vue'
import { userAPI } from '@/api/user'
import dayjs from 'dayjs'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const feedbacks = ref([])

// 统计数据
const stats = reactive({
  totalFeedbacks: 0,
  avgRating: 0,
  thisMonthFeedbacks: 0,
  fiveStarCount: 0
})

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 筛选条件
const filters = reactive({
  rating: null,
  dateRange: null
})

// 获取反馈历史
const fetchFeedbacks = async () => {
  try {
    loading.value = true

    const params = {
      page: pagination.page,
      size: pagination.size
    }

    // 添加筛选条件
    if (filters.rating) params.rating = filters.rating
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.startDate = filters.dateRange[0]
      params.endDate = filters.dateRange[1]
    }

    const response = await userAPI.getCurrentUserFeedbacks(params)
    console.log('User Feedbacks API Response:', response)
    console.log('Response data structure:', response.data)
    console.log('Feedbacks content:', response.data.data?.content)
    console.log('Page info:', response.data.data?.page)

    // 处理分页响应数据结构
    if (response.data && response.data.data) {
      // PageResponse结构: { success: true, data: { content: [], page: {} } }
      feedbacks.value = response.data.data.content || []
      pagination.total = response.data.data.page?.totalElements || 0
    } else if (response.data && Array.isArray(response.data)) {
      // 直接返回数组的情况（向后兼容）
      feedbacks.value = response.data
      pagination.total = response.data.length
    } else {
      feedbacks.value = []
      pagination.total = 0
    }

    console.log('Final feedbacks array:', feedbacks.value)
    console.log('Final pagination total:', pagination.total)

    // 更新统计数据
    updateStats()
  } catch (error) {
    console.error('Failed to fetch feedbacks:', error)
    ElMessage.error('获取反馈历史失败')
  } finally {
    loading.value = false
  }
}

// 更新统计数据
const updateStats = () => {
  stats.totalFeedbacks = feedbacks.value.length

  if (feedbacks.value.length > 0) {
    const totalRating = feedbacks.value.reduce((sum, feedback) => sum + feedback.rating, 0)
    stats.avgRating = totalRating / feedbacks.value.length

    const thisMonth = dayjs().format('YYYY-MM')
    stats.thisMonthFeedbacks = feedbacks.value.filter(feedback =>
      dayjs(feedback.feedbackTime).format('YYYY-MM') === thisMonth
    ).length

    stats.fiveStarCount = feedbacks.value.filter(feedback => feedback.rating === 5).length
  } else {
    stats.avgRating = 0
    stats.thisMonthFeedbacks = 0
    stats.fiveStarCount = 0
  }
}

// 重置筛选条件
const resetFilters = () => {
  Object.assign(filters, {
    rating: null,
    dateRange: null
  })
  pagination.page = 1
  fetchFeedbacks()
}

// 查看工单详情
const viewOrderDetail = (orderId) => {
  router.push(`/user/orders/${orderId}`)
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-'
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

// 页面加载时获取数据
onMounted(() => {
  fetchFeedbacks()
})
</script>

<style scoped>
.feedback-history {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.stat-icon .el-icon {
  font-size: 24px;
  color: white;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.filter-section {
  margin-bottom: 20px;
}

.feedback-list {
  margin-bottom: 20px;
}

.feedback-item {
  border-bottom: 1px solid #ebeef5;
  padding: 20px 0;
}

.feedback-item:last-child {
  border-bottom: none;
}

.feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.feedback-info h4 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.vehicle-info {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.feedback-content {
  margin-bottom: 16px;
}

.feedback-comment {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
  background: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
}

.feedback-details {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item .label {
  color: #909399;
  width: 80px;
  flex-shrink: 0;
}

.detail-item .value {
  color: #606266;
  flex: 1;
}

.feedback-actions {
  text-align: right;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .feedback-history {
    padding: 16px;
  }

  .feedback-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .feedback-rating {
    margin-top: 8px;
  }

  .detail-item {
    flex-direction: column;
  }

  .detail-item .label {
    width: auto;
    margin-bottom: 4px;
  }
}
</style>
