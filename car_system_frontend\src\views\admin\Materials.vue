<template>
  <div class="admin-materials">
    <div class="page-header">
      <h1>材料管理</h1>
      <p>管理维修材料库存和价格</p>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline class="filter-form">
        <el-form-item label="搜索材料">
          <el-input
            v-model="filters.search"
            placeholder="材料名称、规格、品牌"
            style="width: 200px"
            clearable
            @keyup.enter="fetchMaterials"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="材料分类">
          <el-select
            v-model="filters.category"
            placeholder="全部分类"
            clearable
            style="width: 150px"
            @change="fetchMaterials"
          >
            <el-option label="机油" value="oil" />
            <el-option label="滤清器" value="filter" />
            <el-option label="刹车片" value="brake_pad" />
            <el-option label="轮胎" value="tire" />
            <el-option label="电池" value="battery" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>

        <el-form-item label="库存状态">
          <el-select
            v-model="filters.stockStatus"
            placeholder="全部状态"
            clearable
            style="width: 120px"
            @change="fetchMaterials"
          >
            <el-option label="充足" value="sufficient" />
            <el-option label="不足" value="low" />
            <el-option label="缺货" value="out" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="fetchMaterials">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetFilters">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="showCreateDialog">
            <el-icon><Plus /></el-icon>
            添加材料
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 材料列表 -->
    <el-card>
      <!-- 移动端卡片视图 -->
      <div v-if="isMobile" class="mobile-material-list">
        <div v-for="material in materials" :key="material.materialId" class="mobile-material-card">
          <el-card shadow="hover">
            <div class="material-info">
              <div class="material-header">
                <h3 class="material-name">{{ material.materialName }}</h3>
                <el-tag :type="getStockStatusType(material.inventory)">
                  {{ getStockStatusText(material.inventory) }}
                </el-tag>
              </div>
              <div class="material-details">
                <div class="detail-item">
                  <span class="label">规格:</span>
                  <span class="value">{{ material.specification || '未设置' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">分类:</span>
                  <span class="value">{{ getCategoryText(material.category) }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">单价:</span>
                  <span class="value">¥{{ material.unitPrice }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">库存:</span>
                  <span class="value">{{ material.inventory }} {{ material.unit }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">状态:</span>
                  <span class="value">{{ material.status === 1 ? '正常' : '停用' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">创建时间:</span>
                  <span class="value">{{ formatDate(material.createTime) }}</span>
                </div>
              </div>
              <div class="material-actions">
                <el-button type="primary" size="small" @click="editMaterial(material)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button type="danger" size="small" @click="deleteMaterial(material)">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 桌面端表格视图 -->
      <div v-else class="table-responsive">
        <el-table
          v-loading="loading"
          :data="materials"
          style="width: 100%"
          empty-text="暂无材料数据"
          stripe
          highlight-current-row
        >
          <el-table-column prop="materialName" label="材料名称" min-width="150" />
          <el-table-column label="规格" min-width="120">
            <template #default="{ row }">
              {{ row.specification || '未设置' }}
            </template>
          </el-table-column>

          <el-table-column label="分类" width="100">
            <template #default="{ row }">
              {{ getCategoryText(row.category) }}
            </template>
          </el-table-column>

          <el-table-column prop="unitPrice" label="单价" width="100" align="right">
            <template #default="{ row }">
              ¥{{ row.unitPrice }}
            </template>
          </el-table-column>

          <el-table-column label="库存" width="120" align="center">
            <template #default="{ row }">
              <div class="stock-info">
                <div class="stock-quantity">{{ row.inventory }} {{ row.unit }}</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="库存状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getStockStatusType(row.inventory)" size="small">
                {{ getStockStatusText(row.inventory) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.status === 1 ? 'success' : 'danger'" size="small">
                {{ row.status === 1 ? '正常' : '停用' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="editMaterial(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button
                type="text"
                size="small"
                style="color: #f56c6c"
                @click="deleteMaterial(row)"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchMaterials"
          @current-change="fetchMaterials"
        />
      </div>
    </el-card>

    <!-- 创建/编辑材料对话框 -->
    <el-dialog
      v-model="materialDialog.visible"
      :title="materialDialog.isEdit ? '编辑材料' : '添加材料'"
      width="600px"
      @close="resetMaterialForm"
    >
      <el-form
        ref="materialFormRef"
        :model="materialForm"
        :rules="materialRules"
        label-width="100px"
      >
        <el-form-item label="材料名称" prop="materialName">
          <el-input
            v-model="materialForm.materialName"
            placeholder="请输入材料名称"
          />
        </el-form-item>

        <el-form-item label="规格" prop="specification">
          <el-input
            v-model="materialForm.specification"
            placeholder="请输入规格（可选）"
          />
        </el-form-item>

        <el-form-item label="分类" prop="category">
          <el-select
            v-model="materialForm.category"
            placeholder="请选择分类"
            style="width: 100%"
          >
            <el-option label="机油" value="oil" />
            <el-option label="滤清器" value="filter" />
            <el-option label="刹车片" value="brake_pad" />
            <el-option label="轮胎" value="tire" />
            <el-option label="电池" value="battery" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>

        <el-form-item label="单价" prop="unitPrice">
          <el-input-number
            v-model="materialForm.unitPrice"
            :min="0"
            :precision="2"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="单位" prop="unit">
          <el-input
            v-model="materialForm.unit"
            placeholder="如：个、升、公斤"
          />
        </el-form-item>

        <el-form-item label="库存数量" prop="inventory">
          <el-input-number
            v-model="materialForm.inventory"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="materialDialog.visible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="submitting"
          @click="submitMaterialForm"
        >
          {{ materialDialog.isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>


  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Refresh, Plus, Edit, Delete
} from '@element-plus/icons-vue'
import { adminAPI } from '@/api/admin'
import { useAppStore } from '@/stores/app'

const appStore = useAppStore()

// 计算属性
const isMobile = computed(() => appStore.isMobile)

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const materials = ref([])

// 表单引用
const materialFormRef = ref()

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 筛选条件
const filters = reactive({
  search: '',
  category: '',
  stockStatus: ''
})

// 材料对话框
const materialDialog = reactive({
  visible: false,
  isEdit: false,
  materialId: null
})

// 材料表单
const materialForm = reactive({
  materialName: '',
  specification: '',
  category: '',
  unitPrice: 0,
  unit: '',
  inventory: 0
})

// 材料表单验证规则
const materialRules = {
  materialName: [
    { required: true, message: '请输入材料名称', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  unitPrice: [
    { required: true, message: '请输入单价', trigger: 'blur' }
  ],
  unit: [
    { required: true, message: '请输入单位', trigger: 'blur' }
  ],
  inventory: [
    { required: true, message: '请输入库存数量', trigger: 'blur' }
  ]
}

// 获取材料列表
const fetchMaterials = async () => {
  try {
    loading.value = true

    const params = {
      page: pagination.page,
      size: pagination.size
    }

    // 添加搜索条件
    if (filters.search) params.search = filters.search
    if (filters.category) params.category = filters.category
    if (filters.stockStatus) params.stockStatus = filters.stockStatus

    const response = await adminAPI.getAllMaterials(params)
    console.log('Admin Materials API Response:', response)
    console.log('Response data structure:', response.data)
    console.log('Materials content:', response.data.data?.content)
    console.log('Page info:', response.data.data?.page)

    materials.value = response.data.data?.content || []
    pagination.total = response.data.data?.page?.totalElements || 0

    console.log('Final materials array:', materials.value)
    console.log('Final pagination total:', pagination.total)
  } catch (error) {
    console.error('Failed to fetch materials:', error)
    ElMessage.error('获取材料列表失败')
  } finally {
    loading.value = false
  }
}

// 重置筛选条件
const resetFilters = () => {
  Object.assign(filters, {
    search: '',
    category: '',
    stockStatus: ''
  })
  pagination.page = 1
  fetchMaterials()
}

// 显示创建材料对话框
const showCreateDialog = () => {
  materialDialog.visible = true
  materialDialog.isEdit = false
  materialDialog.materialId = null
  resetMaterialForm()
}

// 编辑材料
const editMaterial = (material) => {
  materialDialog.visible = true
  materialDialog.isEdit = true
  materialDialog.materialId = material.materialId

  // 填充表单数据
  Object.assign(materialForm, {
    materialName: material.materialName,
    specification: material.specification || '',
    category: material.category,
    unitPrice: material.unitPrice,
    unit: material.unit,
    inventory: material.inventory
  })
}

// 删除材料
const deleteMaterial = async (material) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除材料 "${material.materialName}" 吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await adminAPI.deleteMaterial(material.materialId)
    ElMessage.success('材料删除成功')
    fetchMaterials()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete material:', error)
      ElMessage.error('删除材料失败')
    }
  }
}



// 提交材料表单
const submitMaterialForm = async () => {
  try {
    await materialFormRef.value.validate()
    submitting.value = true

    if (materialDialog.isEdit) {
      // 更新材料
      await adminAPI.updateMaterial(materialDialog.materialId, materialForm)
      ElMessage.success('材料更新成功')
    } else {
      // 创建材料
      await adminAPI.createMaterial(materialForm)
      ElMessage.success('材料创建成功')
    }

    materialDialog.visible = false
    fetchMaterials()
  } catch (error) {
    console.error('Failed to submit material form:', error)
    ElMessage.error(materialDialog.isEdit ? '更新材料失败' : '创建材料失败')
  } finally {
    submitting.value = false
  }
}

// 重置材料表单
const resetMaterialForm = () => {
  Object.assign(materialForm, {
    materialName: '',
    specification: '',
    category: '',
    unitPrice: 0,
    unit: '',
    inventory: 0
  })
  materialFormRef.value?.clearValidate()
}



// 获取分类文本
const getCategoryText = (category) => {
  const categoryMap = {
    oil: '机油',
    filter: '滤清器',
    brake_pad: '刹车片',
    tire: '轮胎',
    battery: '电池',
    other: '其他'
  }
  return categoryMap[category] || category
}

// 获取库存状态类型
const getStockStatusType = (inventory) => {
  if (inventory <= 0) return 'danger'
  if (inventory <= 10) return 'warning' // 简单的低库存阈值
  return 'success'
}

// 获取库存状态文本
const getStockStatusText = (inventory) => {
  if (inventory <= 0) return '缺货'
  if (inventory <= 10) return '不足' // 简单的低库存阈值
  return '充足'
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchMaterials()
})
</script>

<style scoped>
.admin-materials {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

/* 过滤器卡片 */
.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: end;
}

/* 移动端材料卡片 */
.mobile-material-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.mobile-material-card {
  width: 100%;
}

.material-info {
  padding: 0;
}

.material-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.material-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.material-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-bottom: 15px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.detail-item .label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.detail-item .value {
  font-size: 14px;
  color: #303133;
  word-break: break-all;
}

.material-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  padding-top: 10px;
  border-top: 1px solid #ebeef5;
}

/* 桌面端表格 */
.stock-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.stock-quantity {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.min-stock {
  font-size: 12px;
  color: #909399;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-materials {
    padding: 15px;
  }

  .page-header h1 {
    font-size: 20px;
  }

  .filter-form {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .filter-form .el-form-item {
    margin-bottom: 0;
  }

  .material-details {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .material-actions {
    flex-direction: column;
    gap: 8px;
  }

  .material-actions .el-button {
    width: 100%;
  }

  .pagination-container {
    margin-top: 15px;
  }

  :deep(.el-pagination) {
    justify-content: center;
    flex-wrap: wrap;
  }

  :deep(.el-pagination .el-pagination__sizes) {
    order: 3;
    margin-top: 10px;
  }

  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto !important;
  }
}

@media (max-width: 480px) {
  .admin-materials {
    padding: 10px;
  }

  .page-header h1 {
    font-size: 18px;
  }

  .material-actions {
    flex-wrap: wrap;
  }

  :deep(.el-pagination .el-pagination__sizes),
  :deep(.el-pagination .el-pagination__jump) {
    display: none;
  }

  :deep(.el-dialog) {
    width: 98% !important;
    margin: 2vh auto !important;
  }

  :deep(.el-form-item__label) {
    font-size: 14px;
    line-height: 1.4;
  }

  :deep(.el-input__inner),
  :deep(.el-input-number__inner) {
    font-size: 14px;
  }
}
</style>