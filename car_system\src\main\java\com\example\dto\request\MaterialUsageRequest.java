package com.example.dto.request;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 材料使用记录请求DTO
 */
public class MaterialUsageRequest {
    
    @NotNull(message = "材料ID不能为空")
    private Long materialId;
    
    @NotNull(message = "使用数量不能为空")
    @DecimalMin(value = "0.01", message = "使用数量必须大于0")
    private BigDecimal quantity;
    
    @NotNull(message = "总价格不能为空")
    @DecimalMin(value = "0.01", message = "总价格必须大于0")
    private BigDecimal totalPrice;
    
    private LocalDateTime useTime;
    
    // 构造函数
    public MaterialUsageRequest() {}
    
    public MaterialUsageRequest(Long materialId, BigDecimal quantity, BigDecimal totalPrice, LocalDateTime useTime) {
        this.materialId = materialId;
        this.quantity = quantity;
        this.totalPrice = totalPrice;
        this.useTime = useTime;
    }
    
    // Getters and Setters
    public Long getMaterialId() {
        return materialId;
    }
    
    public void setMaterialId(Long materialId) {
        this.materialId = materialId;
    }
    
    public BigDecimal getQuantity() {
        return quantity;
    }
    
    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }
    
    public BigDecimal getTotalPrice() {
        return totalPrice;
    }
    
    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }
    
    public LocalDateTime getUseTime() {
        return useTime;
    }
    
    public void setUseTime(LocalDateTime useTime) {
        this.useTime = useTime;
    }
}
