package com.example.dto.response;

import java.math.BigDecimal;

/**
 * 故障模式统计DTO
 */
public class PatternStatisticsDTO {
    
    private String brand;
    private String model;
    private String faultType;
    private Integer occurrenceCount;
    private BigDecimal faultPercentage;
    
    public PatternStatisticsDTO() {}
    
    public PatternStatisticsDTO(String brand, String model, String faultType, 
                               Integer occurrenceCount, BigDecimal faultPercentage) {
        this.brand = brand;
        this.model = model;
        this.faultType = faultType;
        this.occurrenceCount = occurrenceCount;
        this.faultPercentage = faultPercentage;
    }
    
    // Getters and Setters
    public String getBrand() {
        return brand;
    }
    
    public void setBrand(String brand) {
        this.brand = brand;
    }
    
    public String getModel() {
        return model;
    }
    
    public void setModel(String model) {
        this.model = model;
    }
    
    public String getFaultType() {
        return faultType;
    }
    
    public void setFaultType(String faultType) {
        this.faultType = faultType;
    }
    
    public Integer getOccurrenceCount() {
        return occurrenceCount;
    }
    
    public void setOccurrenceCount(Integer occurrenceCount) {
        this.occurrenceCount = occurrenceCount;
    }
    
    public BigDecimal getFaultPercentage() {
        return faultPercentage;
    }
    
    public void setFaultPercentage(BigDecimal faultPercentage) {
        this.faultPercentage = faultPercentage;
    }
}
