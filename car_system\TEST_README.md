# 单元测试说明

本项目为车辆维修管理系统的后端API，包含了完整的Controller层和Service层单元测试。

## 测试覆盖范围

### Service层测试
- **AuthService** - 用户认证服务测试
- **UserService** - 用户管理服务测试
- **VehicleService** - 车辆管理服务测试
- **OrderService** - 工单管理服务测试
- **TechnicianService** - 技师管理服务测试
- **MaterialService** - 材料管理服务测试
- **FaultTypeService** - 故障类型管理服务测试

### Controller层测试
- **AuthController** - 认证控制器测试
- **UserController** - 用户控制器测试
- **VehicleController** - 车辆控制器测试
- **OrderController** - 工单控制器测试（待完成）
- **TechnicianController** - 技师控制器测试（待完成）
- **MaterialController** - 材料控制器测试（待完成）
- **FaultTypeController** - 故障类型控制器测试（待完成）

## 测试技术栈

- **JUnit 5** - 测试框架
- **Mockito** - Mock框架
- **Spring Boot Test** - Spring Boot测试支持
- **Spring Security Test** - Spring Security测试支持
- **MockMvc** - Web层测试
- **H2 Database** - 内存数据库用于测试

## 运行测试

### 方式一：使用PowerShell脚本
```powershell
.\run-tests.ps1
```

### 方式二：使用Maven命令
```bash
# 运行所有测试
mvn clean test

# 运行特定测试类
mvn test -Dtest=AuthServiceTest

# 运行特定测试方法
mvn test -Dtest=AuthServiceTest#testLoginUser_Success

# 生成测试报告
mvn surefire-report:report
```

### 方式三：在IDE中运行
在IntelliJ IDEA或Eclipse中，右键点击测试类或测试方法，选择"Run"即可。

## 测试配置

测试使用独立的配置文件 `application-test.yml`，包含：
- H2内存数据库配置
- 测试专用的JWT密钥
- 日志级别配置
- 应用配置（工种列表等）

## 测试数据

测试使用Mock数据，不依赖外部数据库。每个测试方法都有独立的测试数据设置，确保测试之间的隔离性。

## 测试覆盖的功能点

### 认证服务测试
- 用户登录（普通用户、管理员、技师）
- 登录失败场景（用户不存在、密码错误、账户禁用）
- 用户登出
- Token刷新

### 用户服务测试
- 用户注册
- 获取用户信息
- 更新用户信息
- 获取用户车辆列表
- 获取用户工单列表
- 各种业务异常场景

### 车辆服务测试
- 添加车辆
- 获取车辆信息
- 更新车辆信息
- 删除车辆
- 权限验证
- 业务规则验证（车牌号、VIN码唯一性等）

### 工单服务测试
- 创建工单
- 获取工单详情
- 技师接受工单
- 完成工单
- 权限验证
- 状态流转验证

### 技师服务测试
- 技师注册
- 获取工种列表
- 获取技师信息
- 获取技师工单
- 查找可用技师

### 材料服务测试
- 材料增删改查
- 库存管理
- 分类管理
- 低库存提醒

### 故障类型服务测试
- 故障类型增删改查
- 工种关联
- 状态管理

## 测试最佳实践

1. **测试隔离**：每个测试方法都是独立的，使用@BeforeEach设置测试数据
2. **Mock使用**：对外部依赖进行Mock，确保测试的可控性
3. **异常测试**：覆盖正常流程和异常流程
4. **边界测试**：测试边界条件和特殊情况
5. **权限测试**：验证访问控制和权限检查

## 查看测试报告

运行测试后，可以在以下位置查看详细的测试报告：
- `target/surefire-reports/` - 原始测试报告
- `target/site/surefire-report.html` - HTML格式的测试报告

## 注意事项

1. 确保已安装Java 17和Maven
2. 测试运行前会自动创建H2内存数据库
3. 测试数据不会影响开发或生产数据库
4. 如果测试失败，请检查控制台输出的错误信息

## 持续集成

这些测试可以集成到CI/CD流水线中，确保代码质量：
```yaml
# GitHub Actions示例
- name: Run tests
  run: mvn clean test
  
- name: Generate test report
  run: mvn surefire-report:report
```
