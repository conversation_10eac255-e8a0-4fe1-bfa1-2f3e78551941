# PowerShell脚本用于运行单元测试

Write-Host "开始运行单元测试..." -ForegroundColor Green

# 设置测试环境变量
$env:SPRING_PROFILES_ACTIVE = "test"

# 运行所有测试
Write-Host "运行所有单元测试..." -ForegroundColor Yellow
mvn clean test

# 检查测试结果
if ($LASTEXITCODE -eq 0) {
    Write-Host "所有测试通过!" -ForegroundColor Green
} else {
    Write-Host "测试失败，请检查错误信息" -ForegroundColor Red
    exit 1
}

# 生成测试报告
Write-Host "生成测试报告..." -ForegroundColor Yellow
mvn surefire-report:report

Write-Host "测试完成！测试报告位于 target/site/surefire-report.html" -ForegroundColor Green
