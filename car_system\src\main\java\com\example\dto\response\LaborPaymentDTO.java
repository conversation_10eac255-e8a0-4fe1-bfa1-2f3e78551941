package com.example.dto.response;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 工时费记录DTO
 */
public class LaborPaymentDTO {

    private Long paymentId;
    private Long technicianId;
    private Integer year;
    private Integer month;
    private BigDecimal totalHours;
    private BigDecimal hourlyRate;
    private BigDecimal totalAmount;
    private String paymentStatus;
    private LocalDateTime paymentTime;
    private LocalDateTime createTime;

    public LaborPaymentDTO() {}

    public LaborPaymentDTO(Long paymentId, Long technicianId, Integer year, Integer month,
                          BigDecimal totalHours, BigDecimal hourlyRate, BigDecimal totalAmount,
                          String paymentStatus, LocalDateTime paymentTime, LocalDateTime createTime) {
        this.paymentId = paymentId;
        this.technicianId = technicianId;
        this.year = year;
        this.month = month;
        this.totalHours = totalHours;
        this.hourlyRate = hourlyRate;
        this.totalAmount = totalAmount;
        this.paymentStatus = paymentStatus;
        this.paymentTime = paymentTime;
        this.createTime = createTime;
    }

    public Long getPaymentId() {
        return paymentId;
    }

    public void setPaymentId(Long paymentId) {
        this.paymentId = paymentId;
    }

    public Long getTechnicianId() {
        return technicianId;
    }

    public void setTechnicianId(Long technicianId) {
        this.technicianId = technicianId;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getMonth() {
        return month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public BigDecimal getTotalHours() {
        return totalHours;
    }

    public void setTotalHours(BigDecimal totalHours) {
        this.totalHours = totalHours;
    }

    public BigDecimal getHourlyRate() {
        return hourlyRate;
    }

    public void setHourlyRate(BigDecimal hourlyRate) {
        this.hourlyRate = hourlyRate;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(String paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public LocalDateTime getPaymentTime() {
        return paymentTime;
    }

    public void setPaymentTime(LocalDateTime paymentTime) {
        this.paymentTime = paymentTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
}
