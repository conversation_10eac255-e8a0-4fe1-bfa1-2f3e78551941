@import './base.css';

/* 全局样式重置和基础设置 */
* {
  box-sizing: border-box;
}

#app {
  font-weight: normal;
  min-height: 100vh;
  width: 100%;
}

/* 响应式容器 */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.container-fluid {
  width: 100%;
  padding: 0 15px;
}

/* 响应式网格系统 */
.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px;
}

.col {
  flex: 1;
  padding: 0 15px;
}

.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* 响应式工具类 */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-grid { display: grid !important; }

.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

.w-100 { width: 100% !important; }
.h-100 { height: 100% !important; }

/* 间距工具类 */
.m-0 { margin: 0 !important; }
.m-1 { margin: 0.25rem !important; }
.m-2 { margin: 0.5rem !important; }
.m-3 { margin: 1rem !important; }
.m-4 { margin: 1.5rem !important; }
.m-5 { margin: 3rem !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 0.25rem !important; }
.p-2 { padding: 0.5rem !important; }
.p-3 { padding: 1rem !important; }
.p-4 { padding: 1.5rem !important; }
.p-5 { padding: 3rem !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }
.mb-5 { margin-bottom: 3rem !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }
.mt-5 { margin-top: 3rem !important; }

/* 响应式断点 */
@media (max-width: 575.98px) {
  .d-sm-none { display: none !important; }
  .d-sm-block { display: block !important; }
  .d-sm-flex { display: flex !important; }

  .col-sm-12 { flex: 0 0 100%; max-width: 100%; }
  .col-sm-6 { flex: 0 0 50%; max-width: 50%; }
  .col-sm-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-sm-3 { flex: 0 0 25%; max-width: 25%; }
}

@media (max-width: 767.98px) {
  .d-md-none { display: none !important; }
  .d-md-block { display: block !important; }
  .d-md-flex { display: flex !important; }

  .col-md-12 { flex: 0 0 100%; max-width: 100%; }
  .col-md-6 { flex: 0 0 50%; max-width: 50%; }
  .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-md-3 { flex: 0 0 25%; max-width: 25%; }
}

@media (max-width: 991.98px) {
  .d-lg-none { display: none !important; }
  .d-lg-block { display: block !important; }
  .d-lg-flex { display: flex !important; }

  .col-lg-12 { flex: 0 0 100%; max-width: 100%; }
  .col-lg-6 { flex: 0 0 50%; max-width: 50%; }
  .col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-lg-3 { flex: 0 0 25%; max-width: 25%; }
}

/* 表格响应式 */
.table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.table-responsive table {
  min-width: 600px;
}

/* 卡片响应式 */
.card-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

@media (max-width: 768px) {
  .card-grid {
    grid-template-columns: 1fr;
  }
}

/* 按钮响应式 */
@media (max-width: 768px) {
  .btn-responsive {
    width: 100%;
    margin-bottom: 0.5rem;
  }
}

/* 表单响应式 */
.form-responsive .el-form-item {
  margin-bottom: 1rem;
}

@media (max-width: 768px) {
  .form-responsive .el-form-item {
    margin-bottom: 0.75rem;
  }

  .form-responsive .el-form-item__label {
    line-height: 1.5;
    padding-bottom: 0.25rem;
  }
}

/* 链接样式 */
a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}
