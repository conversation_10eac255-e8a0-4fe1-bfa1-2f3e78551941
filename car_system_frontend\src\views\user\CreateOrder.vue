<template>
  <div class="create-order-page">
    <div class="page-header">
      <h1>提交维修工单</h1>
      <p>请填写车辆故障信息，我们将为您安排专业技师进行维修</p>
    </div>

    <el-card>
      <el-form
        ref="orderFormRef"
        :model="orderForm"
        :rules="orderRules"
        label-width="120px"
        class="order-form"
      >
        <el-form-item label="选择车辆" prop="vehicleId">
          <el-select
            v-model="orderForm.vehicleId"
            placeholder="请选择需要维修的车辆"
            style="width: 100%"
            :loading="vehiclesLoading"
            @change="onVehicleChange"
          >
            <el-option
              v-for="vehicle in vehicles"
              :key="vehicle.vehicleId"
              :label="`${vehicle.licensePlate} - ${vehicle.brand} ${vehicle.model}`"
              :value="vehicle.vehicleId"
            >
              <div class="vehicle-option">
                <div class="vehicle-main">
                  <span class="license-plate">{{ vehicle.licensePlate }}</span>
                  <span class="vehicle-info">{{ vehicle.brand }} {{ vehicle.model }}</span>
                </div>
                <div class="vehicle-detail">
                  {{ vehicle.year }}年 · {{ vehicle.color }}
                </div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="故障类型" prop="faultTypeId">
          <el-select
            v-model="orderForm.faultTypeId"
            placeholder="请选择故障类型"
            style="width: 100%"
            :loading="faultTypesLoading"
            @change="onFaultTypeChange"
          >
            <el-option
              v-for="faultType in faultTypes"
              :key="faultType.faultTypeId"
              :label="faultType.typeName"
              :value="faultType.faultTypeId"
            >
              <div class="fault-type-option">
                <div class="fault-type-name">{{ faultType.typeName }}</div>
                <div class="fault-type-desc">{{ faultType.description }}</div>
                <div class="fault-type-info">
                  <el-tag size="small" type="info">
                    预估工时: {{ faultType.estimatedHours }}小时
                  </el-tag>
                  <el-tag size="small" type="warning">
                    需要技师: {{ faultType.requiredTechCount }}人
                  </el-tag>
                </div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 故障类型详情展示 -->
        <div v-if="selectedFaultType" class="fault-type-detail">
          <h3>故障类型详情</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="故障类型">
              {{ selectedFaultType.typeName }}
            </el-descriptions-item>
            <el-descriptions-item label="预估工时">
              {{ selectedFaultType.estimatedHours }} 小时
            </el-descriptions-item>
            <el-descriptions-item label="需要技师">
              {{ selectedFaultType.requiredTechCount }} 人
            </el-descriptions-item>
            <el-descriptions-item label="所需工种">
              <el-tag
                v-for="specialty in selectedFaultType.requiredSpecialties"
                :key="specialty"
                size="small"
                style="margin-right: 5px"
              >
                {{ getSpecialtyName(specialty) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="故障描述" :span="2">
              {{ selectedFaultType.description }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <el-form-item label="故障描述" prop="description">
          <el-input
            v-model="orderForm.description"
            type="textarea"
            :rows="4"
            placeholder="请详细描述车辆故障现象，如异响、异味、性能异常等"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="紧急程度" prop="urgencyLevel">
          <el-radio-group v-model="orderForm.urgencyLevel">
            <el-radio label="low">
              <div class="urgency-option">
                <div class="urgency-title">低</div>
                <div class="urgency-desc">不影响正常使用</div>
              </div>
            </el-radio>
            <el-radio label="normal">
              <div class="urgency-option">
                <div class="urgency-title">中</div>
                <div class="urgency-desc">影响部分功能</div>
              </div>
            </el-radio>
            <el-radio label="high">
              <div class="urgency-option">
                <div class="urgency-title">高</div>
                <div class="urgency-desc">严重影响使用</div>
              </div>
            </el-radio>
            <el-radio label="urgent">
              <div class="urgency-option">
                <div class="urgency-title">紧急</div>
                <div class="urgency-desc">无法正常使用</div>
              </div>
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="期望维修时间" prop="preferredTime">
          <el-date-picker
            v-model="orderForm.preferredTime"
            type="datetime"
            placeholder="请选择期望的维修时间"
            style="width: 100%"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DDTHH:mm:ss[Z]"
            :disabled-date="disabledDate"
            :disabled-hours="disabledHours"
          />
        </el-form-item>

        <el-form-item label="联系电话" prop="contactPhone">
          <el-input
            v-model="orderForm.contactPhone"
            placeholder="请输入联系电话"
            maxlength="11"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" :loading="submitting" @click="submitOrder">
            提交工单
          </el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button @click="$router.go(-1)">返回</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { userAPI } from '@/api/user'
import { faultTypeAPI } from '@/api/faultType'
import { orderAPI } from '@/api/order'

const router = useRouter()

// 响应式数据
const submitting = ref(false)
const vehiclesLoading = ref(false)
const faultTypesLoading = ref(false)
const vehicles = ref([])
const faultTypes = ref([])

// 表单引用
const orderFormRef = ref()

// 工单表单数据
const orderForm = reactive({
  vehicleId: null,
  faultTypeId: null,
  description: '',
  urgencyLevel: 'normal',
  preferredTime: '',
  contactPhone: ''
})

// 工种名称映射
const specialtyNames = {
  engine: '发动机维修',
  transmission: '变速箱维修',
  brake: '制动系统维修',
  electrical: '电气系统维修',
  hvac: '空调系统维修',
  chassis: '底盘维修',
  body: '车身维修',
  tire: '轮胎维修'
}

// 表单验证规则
const orderRules = {
  vehicleId: [
    { required: true, message: '请选择车辆', trigger: 'change' }
  ],
  faultTypeId: [
    { required: true, message: '请选择故障类型', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请描述故障现象', trigger: 'blur' },
    { min: 10, message: '故障描述至少10个字符', trigger: 'blur' }
  ],
  urgencyLevel: [
    { required: true, message: '请选择紧急程度', trigger: 'change' }
  ],
  preferredTime: [
    { required: true, message: '请选择期望维修时间', trigger: 'change' }
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

// 计算属性
const selectedFaultType = computed(() => {
  return faultTypes.value.find(ft => ft.faultTypeId === orderForm.faultTypeId)
})

// 获取车辆列表
const fetchVehicles = async () => {
  try {
    vehiclesLoading.value = true
    const response = await userAPI.getCurrentUserVehicles({ page: 1, size: 100 })
    vehicles.value = response.data.data?.content || []
  } catch (error) {
    console.error('Failed to fetch vehicles:', error)
    ElMessage.error('获取车辆列表失败')
  } finally {
    vehiclesLoading.value = false
  }
}

// 获取故障类型列表
const fetchFaultTypes = async () => {
  try {
    faultTypesLoading.value = true
    const response = await faultTypeAPI.getList({ status: 1 })
    faultTypes.value = response.data.data?.content || response.data || []
  } catch (error) {
    console.error('Failed to fetch fault types:', error)
    ElMessage.error('获取故障类型失败')
  } finally {
    faultTypesLoading.value = false
  }
}

// 车辆选择变化
const onVehicleChange = (vehicleId) => {
  // 可以在这里添加车辆相关的逻辑
  console.log('Selected vehicle:', vehicleId)
}

// 故障类型选择变化
const onFaultTypeChange = (faultTypeId) => {
  // 可以在这里添加故障类型相关的逻辑
  console.log('Selected fault type:', faultTypeId)
}

// 获取工种名称
const getSpecialtyName = (specialty) => {
  return specialtyNames[specialty] || specialty
}

// 禁用日期（不能选择过去的日期）
const disabledDate = (time) => {
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
}

// 禁用小时（工作时间 8:00-18:00）
const disabledHours = () => {
  const hours = []
  for (let i = 0; i < 8; i++) {
    hours.push(i)
  }
  for (let i = 18; i < 24; i++) {
    hours.push(i)
  }
  return hours
}

// 提交工单
const submitOrder = async () => {
  if (!orderFormRef.value) return

  try {
    await orderFormRef.value.validate()
    submitting.value = true

    await orderAPI.create(orderForm)

    ElMessage.success('工单提交成功，我们将尽快为您安排技师')
    router.push('/user/orders')
  } catch (error) {
    console.error('Failed to submit order:', error)
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  if (orderFormRef.value) {
    orderFormRef.value.resetFields()
  }
  Object.assign(orderForm, {
    vehicleId: null,
    faultTypeId: null,
    description: '',
    urgencyLevel: 'normal',
    preferredTime: '',
    contactPhone: ''
  })
}

// 组件挂载时获取数据
onMounted(() => {
  fetchVehicles()
  fetchFaultTypes()
})
</script>

<style scoped>
.create-order-page {
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 28px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.order-form {
  padding: 20px;
}

/* 车辆选项样式 */
.vehicle-option {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.vehicle-main {
  display: flex;
  align-items: center;
  gap: 10px;
}

.license-plate {
  font-weight: 600;
  color: #409eff;
}

.vehicle-info {
  color: #333;
}

.vehicle-detail {
  font-size: 12px;
  color: #999;
}

/* 故障类型选项样式 */
.fault-type-option {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 5px 0;
}

.fault-type-name {
  font-weight: 600;
  color: #333;
}

.fault-type-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.fault-type-info {
  display: flex;
  gap: 5px;
}

/* 故障类型详情 */
.fault-type-detail {
  margin: 20px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.fault-type-detail h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

/* 紧急程度选项样式 */
.urgency-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.urgency-title {
  font-weight: 600;
  color: #333;
}

.urgency-desc {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

:deep(.el-radio) {
  margin-right: 30px;
  margin-bottom: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .create-order-page {
    padding: 0 15px;
  }

  .page-header h1 {
    font-size: 24px;
  }

  .order-form {
    padding: 15px;
  }

  :deep(.el-form-item__label) {
    width: 100px !important;
  }

  :deep(.el-radio) {
    margin-right: 20px;
  }
}

@media (max-width: 480px) {
  :deep(.el-form-item) {
    flex-direction: column;
  }

  :deep(.el-form-item__label) {
    width: 100% !important;
    text-align: left;
    margin-bottom: 5px;
  }

  :deep(.el-radio) {
    margin-right: 15px;
    margin-bottom: 15px;
    width: 100%;
  }

  :deep(.el-radio-group) {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .urgency-option {
    align-items: flex-start;
    text-align: left;
  }

  .fault-type-info {
    flex-direction: column;
    gap: 3px;
  }

  .fault-type-detail {
    padding: 15px;
    margin: 15px 0;
  }

  :deep(.el-descriptions) {
    font-size: 14px;
  }

  :deep(.el-descriptions__label) {
    font-size: 13px;
  }

  :deep(.el-descriptions__content) {
    font-size: 13px;
  }

  .page-header {
    text-align: left;
    margin-bottom: 20px;
  }

  .page-header h1 {
    font-size: 20px;
  }

  .page-header p {
    font-size: 14px;
  }
}
</style>
