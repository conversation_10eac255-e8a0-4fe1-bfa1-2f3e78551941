package com.example.repository;

import com.example.entity.OrderStatusHistory;
import com.example.entity.RepairOrder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface OrderStatusHistoryRepository extends JpaRepository<OrderStatusHistory, Long> {
    
    /**
     * 根据工单ID查找状态历史
     */
    List<OrderStatusHistory> findByRepairOrderOrderIdOrderByChangeTimeAsc(Long orderId);
    
    /**
     * 根据工单ID查找状态历史（分页）
     */
    Page<OrderStatusHistory> findByRepairOrderOrderId(Long orderId, Pageable pageable);
    
    /**
     * 根据状态变更查找历史记录
     */
    Page<OrderStatusHistory> findByToStatus(RepairOrder.OrderStatus toStatus, Pageable pageable);
    
    /**
     * 根据操作人查找历史记录
     */
    Page<OrderStatusHistory> findByOperator(String operator, Pageable pageable);
    
    /**
     * 根据时间范围查找状态变更记录
     */
    @Query("SELECT osh FROM OrderStatusHistory osh WHERE osh.changeTime BETWEEN :startTime AND :endTime")
    Page<OrderStatusHistory> findByChangeTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                                     @Param("endTime") LocalDateTime endTime, 
                                                     Pageable pageable);
    
    /**
     * 统计状态变更次数
     */
    @Query("SELECT osh.toStatus, COUNT(osh) FROM OrderStatusHistory osh " +
           "WHERE osh.changeTime BETWEEN :startTime AND :endTime " +
           "GROUP BY osh.toStatus")
    List<Object[]> getStatusChangeStats(@Param("startTime") LocalDateTime startTime, 
                                       @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取工单的最新状态变更记录
     */
    @Query("SELECT osh FROM OrderStatusHistory osh WHERE osh.repairOrder.orderId = :orderId " +
           "ORDER BY osh.changeTime DESC")
    List<OrderStatusHistory> findLatestByOrderId(@Param("orderId") Long orderId, Pageable pageable);
}
