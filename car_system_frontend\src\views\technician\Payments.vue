<template>
  <div class="technician-payments">
    <div class="page-header">
      <h1>收入记录</h1>
      <p>查看我的工作收入和支付记录</p>
    </div>

    <!-- 收入统计卡片 -->
    <div class="stats-grid">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon total">
            <el-icon size="24"><Money /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">¥{{ formatCurrency(totalEarnings) }}</div>
            <div class="stat-label">总收入</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon month">
            <el-icon size="24"><Calendar /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">¥{{ formatCurrency(monthlyEarnings) }}</div>
            <div class="stat-label">本月收入</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon orders">
            <el-icon size="24"><Document /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ completedOrders }}</div>
            <div class="stat-label">工时记录</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 筛选器 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline class="filter-form">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
            @change="fetchPayments"
          />
        </el-form-item>



        <el-form-item>
          <el-button type="primary" @click="fetchPayments">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="resetFilters">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>

        </el-form-item>
      </el-form>
    </el-card>

    <!-- 支付记录列表 -->
    <el-card>
      <!-- 移动端卡片视图 -->
      <div v-if="isMobile" class="mobile-payment-list">
        <div v-for="payment in payments" :key="payment.paymentId" class="mobile-payment-card">
          <el-card shadow="hover">
            <div class="payment-info">
              <div class="payment-header">
                <div class="payment-amount">¥{{ formatCurrency(payment.totalAmount) }}</div>
                <el-tag :type="getPaymentStatusType(payment.paymentStatus)">
                  {{ getPaymentStatusText(payment.paymentStatus) }}
                </el-tag>
              </div>
              <div class="payment-details">
                <div class="detail-item">
                  <span class="label">年份:</span>
                  <span class="value">{{ payment.year }}年</span>
                </div>
                <div class="detail-item">
                  <span class="label">月份:</span>
                  <span class="value">{{ payment.month }}月</span>
                </div>
                <div class="detail-item">
                  <span class="label">总工时:</span>
                  <span class="value">{{ payment.totalHours }}小时</span>
                </div>
                <div class="detail-item">
                  <span class="label">时薪:</span>
                  <span class="value">¥{{ formatCurrency(payment.hourlyRate) }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">创建时间:</span>
                  <span class="value">{{ formatDate(payment.createTime) }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">支付时间:</span>
                  <span class="value">{{ payment.paymentTime ? formatDate(payment.paymentTime) : '未设置' }}</span>
                </div>
              </div>

            </div>
          </el-card>
        </div>
      </div>

      <!-- 桌面端表格视图 -->
      <div v-else class="table-responsive">
        <el-table
          v-loading="loading"
          :data="payments"
          style="width: 100%"
          empty-text="暂无支付记录"
          stripe
          highlight-current-row
        >
          <el-table-column prop="year" label="年份" width="80" align="center" />

          <el-table-column prop="month" label="月份" width="80" align="center">
            <template #default="{ row }">
              {{ row.month }}月
            </template>
          </el-table-column>

          <el-table-column prop="totalHours" label="总工时" width="100" align="center">
            <template #default="{ row }">
              {{ row.totalHours }}h
            </template>
          </el-table-column>

          <el-table-column prop="hourlyRate" label="时薪" width="100" align="right">
            <template #default="{ row }">
              ¥{{ formatCurrency(row.hourlyRate) }}
            </template>
          </el-table-column>

          <el-table-column prop="totalAmount" label="收入金额" width="120" align="right">
            <template #default="{ row }">
              <span class="amount-text">¥{{ formatCurrency(row.totalAmount) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="支付状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getPaymentStatusType(row.paymentStatus)" size="small">
                {{ getPaymentStatusText(row.paymentStatus) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.createTime) }}
            </template>
          </el-table-column>

          <el-table-column label="支付时间" width="160">
            <template #default="{ row }">
              {{ row.paymentTime ? formatDate(row.paymentTime) : '未设置' }}
            </template>
          </el-table-column>


        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchPayments"
          @current-change="fetchPayments"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Money, Calendar, Clock, Document, Search, Refresh
} from '@element-plus/icons-vue'
import { technicianAPI } from '@/api/technician'
import { useAppStore } from '@/stores/app'
import dayjs from 'dayjs'

const appStore = useAppStore()

// 计算属性
const isMobile = computed(() => appStore.isMobile)

// 响应式数据
const loading = ref(false)
const payments = ref([])

// 统计数据
const totalEarnings = ref(0)
const monthlyEarnings = ref(0)
const completedOrders = ref(0)

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 筛选条件
const filters = reactive({
  dateRange: null,
  status: ''
})

// 获取支付记录
const fetchPayments = async () => {
  try {
    loading.value = true

    const params = {
      page: pagination.page,
      size: pagination.size
    }

    // 添加筛选条件
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.startDate = filters.dateRange[0]
      params.endDate = filters.dateRange[1]
    }
    if (filters.status) params.status = filters.status

    const response = await technicianAPI.getCurrentTechnicianPayments(params)
    console.log('Technician Payments API Response:', response)
    console.log('Response data structure:', response.data)
    console.log('Payments content:', response.data.data?.content)
    console.log('Page info:', response.data.data?.page)

    // 处理分页响应数据结构
    if (response.data && response.data.data) {
      // PageResponse结构: { success: true, data: { content: [], page: {} } }
      payments.value = response.data.data.content || []
      pagination.total = response.data.data.page?.totalElements || 0
    } else if (response.data && Array.isArray(response.data)) {
      // 直接返回数组的情况（向后兼容）
      payments.value = response.data
      pagination.total = response.data.length
    } else {
      payments.value = []
      pagination.total = 0
    }

    console.log('Final payments array:', payments.value)
    console.log('Final pagination total:', pagination.total)
  } catch (error) {
    console.error('Failed to fetch payments:', error)
    ElMessage.error('获取支付记录失败')
  } finally {
    loading.value = false
  }
}

// 获取统计数据
const fetchStatistics = async () => {
  try {
    // 获取所有支付记录来计算统计信息
    const response = await technicianAPI.getCurrentTechnicianPayments({
      page: 1,
      size: 1000 // 获取足够多的记录来计算统计
    })

    const payments = response.data?.data?.content || []

    // 计算总收入（所有记录都是已支付状态）
    totalEarnings.value = payments
      .reduce((sum, p) => sum + (p.totalAmount || 0), 0)

    // 计算本月收入
    const currentMonth = dayjs().format('YYYY-MM')
    monthlyEarnings.value = payments
      .filter(p => dayjs(p.paymentTime).format('YYYY-MM') === currentMonth)
      .reduce((sum, p) => sum + (p.totalAmount || 0), 0)

    // 计算工时记录数
    completedOrders.value = payments.length

  } catch (error) {
    console.error('Failed to fetch statistics:', error)
  }
}

// 重置筛选条件
const resetFilters = () => {
  Object.assign(filters, {
    dateRange: null,
    status: ''
  })
  pagination.page = 1
  fetchPayments()
}





// 格式化货币
const formatCurrency = (amount) => {
  return (amount || 0).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

// 格式化日期
const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

// 获取支付状态类型（所有记录都是已支付状态）
const getPaymentStatusType = (status) => {
  return 'success'
}

// 获取支付状态文本（所有记录都是已支付状态）
const getPaymentStatusText = (status) => {
  return '已支付'
}

// 组件挂载时获取数据
onMounted(() => {
  fetchPayments()
  fetchStatistics()
})
</script>

<style scoped>
.technician-payments {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  border-radius: 8px;
  overflow: hidden;
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.month {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.pending {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #e6a23c;
}

.stat-icon.orders {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

/* 过滤器卡片 */
.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: end;
}

/* 移动端支付卡片 */
.mobile-payment-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.mobile-payment-card {
  width: 100%;
}

.payment-info {
  padding: 0;
}

.payment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.payment-amount {
  font-size: 20px;
  font-weight: 700;
  color: #67c23a;
}

.payment-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-bottom: 15px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.detail-item .label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.detail-item .value {
  font-size: 14px;
  color: #303133;
  word-break: break-all;
}

.payment-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  padding-top: 10px;
  border-top: 1px solid #ebeef5;
}

/* 桌面端表格 */
.amount-text {
  font-weight: 600;
  color: #67c23a;
}

.no-action {
  color: #c0c4cc;
  font-size: 12px;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .technician-payments {
    padding: 15px;
  }

  .page-header h1 {
    font-size: 20px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .stat-content {
    padding: 15px;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    margin-right: 12px;
  }

  .stat-number {
    font-size: 24px;
  }

  .filter-form {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .filter-form .el-form-item {
    margin-bottom: 0;
  }

  .payment-details {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .payment-actions {
    flex-direction: column;
    gap: 8px;
  }

  .payment-actions .el-button {
    width: 100%;
  }

  .pagination-container {
    margin-top: 15px;
  }

  :deep(.el-pagination) {
    justify-content: center;
    flex-wrap: wrap;
  }

  :deep(.el-pagination .el-pagination__sizes) {
    order: 3;
    margin-top: 10px;
  }
}

@media (max-width: 480px) {
  .technician-payments {
    padding: 10px;
  }

  .page-header h1 {
    font-size: 18px;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
    padding: 12px;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 8px;
    width: 40px;
    height: 40px;
  }

  .stat-number {
    font-size: 20px;
  }

  .stat-label {
    font-size: 12px;
  }

  .payment-amount {
    font-size: 18px;
  }

  :deep(.el-pagination .el-pagination__sizes),
  :deep(.el-pagination .el-pagination__jump) {
    display: none;
  }

  :deep(.el-date-picker) {
    width: 100% !important;
  }

  :deep(.el-select) {
    width: 100% !important;
  }

  :deep(.el-button) {
    width: 100%;
  }
}
</style>
