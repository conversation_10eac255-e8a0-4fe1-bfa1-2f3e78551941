package com.example.dto.response;

import java.time.LocalDateTime;

/**
 * 反馈DTO
 */
public class FeedbackDTO {

    private Long feedbackId;
    private Long orderId;
    private Integer rating;
    private String comment;
    private LocalDateTime feedbackTime;
    
    // 工单相关信息
    private String orderDescription;
    private String faultTypeName;
    private String vehicleInfo;
    private LocalDateTime orderSubmitTime;
    private LocalDateTime orderCompletionTime;
    private String technicianNames;

    public FeedbackDTO() {}

    public FeedbackDTO(Long feedbackId, Long orderId, Integer rating, String comment, 
                      LocalDateTime feedbackTime, String orderDescription, String faultTypeName,
                      String vehicleInfo, LocalDateTime orderSubmitTime, 
                      LocalDateTime orderCompletionTime, String technicianNames) {
        this.feedbackId = feedbackId;
        this.orderId = orderId;
        this.rating = rating;
        this.comment = comment;
        this.feedbackTime = feedbackTime;
        this.orderDescription = orderDescription;
        this.faultTypeName = faultTypeName;
        this.vehicleInfo = vehicleInfo;
        this.orderSubmitTime = orderSubmitTime;
        this.orderCompletionTime = orderCompletionTime;
        this.technicianNames = technicianNames;
    }

    public Long getFeedbackId() {
        return feedbackId;
    }

    public void setFeedbackId(Long feedbackId) {
        this.feedbackId = feedbackId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Integer getRating() {
        return rating;
    }

    public void setRating(Integer rating) {
        this.rating = rating;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public LocalDateTime getFeedbackTime() {
        return feedbackTime;
    }

    public void setFeedbackTime(LocalDateTime feedbackTime) {
        this.feedbackTime = feedbackTime;
    }

    public String getOrderDescription() {
        return orderDescription;
    }

    public void setOrderDescription(String orderDescription) {
        this.orderDescription = orderDescription;
    }

    public String getFaultTypeName() {
        return faultTypeName;
    }

    public void setFaultTypeName(String faultTypeName) {
        this.faultTypeName = faultTypeName;
    }

    public String getVehicleInfo() {
        return vehicleInfo;
    }

    public void setVehicleInfo(String vehicleInfo) {
        this.vehicleInfo = vehicleInfo;
    }

    public LocalDateTime getOrderSubmitTime() {
        return orderSubmitTime;
    }

    public void setOrderSubmitTime(LocalDateTime orderSubmitTime) {
        this.orderSubmitTime = orderSubmitTime;
    }

    public LocalDateTime getOrderCompletionTime() {
        return orderCompletionTime;
    }

    public void setOrderCompletionTime(LocalDateTime orderCompletionTime) {
        this.orderCompletionTime = orderCompletionTime;
    }

    public String getTechnicianNames() {
        return technicianNames;
    }

    public void setTechnicianNames(String technicianNames) {
        this.technicianNames = technicianNames;
    }
}
