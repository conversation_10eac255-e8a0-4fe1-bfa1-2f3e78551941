package com.example.service;

import com.example.dto.request.MaterialRequest;
import com.example.dto.response.MaterialDTO;
import com.example.dto.response.PageResponse;
import com.example.entity.Material;
import com.example.exception.BusinessException;
import com.example.exception.ResourceNotFoundException;
import com.example.repository.MaterialRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 材料服务
 */
@Service
@Transactional
public class MaterialService {

    @Autowired
    private MaterialRepository materialRepository;

    /**
     * 获取材料列表
     */
    @Transactional(readOnly = true)
    public PageResponse<MaterialDTO> getMaterials(String search, String category, String stockStatus, Pageable pageable) {
        Specification<Material> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 只查询启用状态的材料
            predicates.add(criteriaBuilder.equal(root.get("status"), 1));

            // 搜索条件（材料名称）
            if (search != null && !search.trim().isEmpty()) {
                String searchPattern = "%" + search.trim().toLowerCase() + "%";
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("materialName")),
                    searchPattern
                ));
            }

            // 分类筛选
            if (category != null && !category.trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("category")),
                    "%" + category.trim().toLowerCase() + "%"
                ));
            }

            // 库存状态筛选
            if (stockStatus != null && !stockStatus.trim().isEmpty()) {
                switch (stockStatus.toLowerCase()) {
                    case "in_stock":
                        predicates.add(criteriaBuilder.greaterThan(root.get("inventory"), 0));
                        break;
                    case "out_of_stock":
                        predicates.add(criteriaBuilder.equal(root.get("inventory"), 0));
                        break;
                    case "low_stock":
                        // 假设低库存阈值为10
                        predicates.add(criteriaBuilder.and(
                            criteriaBuilder.greaterThan(root.get("inventory"), 0),
                            criteriaBuilder.lessThanOrEqualTo(root.get("inventory"), 10)
                        ));
                        break;
                }
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        Page<Material> materialPage = materialRepository.findAll(spec, pageable);
        List<MaterialDTO> materialDTOs = materialPage.getContent().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        return PageResponse.success(materialDTOs, new PageResponse.PageInfo(
                materialPage.getNumber() + 1,
                materialPage.getSize(),
                materialPage.getTotalElements(),
                materialPage.getTotalPages()
        ));
    }

    /**
     * 获取材料详情
     */
    @Transactional(readOnly = true)
    public MaterialDTO getMaterial(Long id) {
        Material material = materialRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("材料不存在"));
        return convertToDTO(material);
    }

    /**
     * 添加材料
     */
    public MaterialDTO addMaterial(MaterialRequest request) {
        // 检查材料名称是否已存在
        if (materialRepository.existsByMaterialNameAndSpecification(
                request.getMaterialName(), request.getSpecification())) {
            throw new BusinessException("相同名称和规格的材料已存在");
        }

        Material material = new Material();
        material.setMaterialName(request.getMaterialName());
        material.setSpecification(request.getSpecification());
        material.setUnit(request.getUnit());
        material.setUnitPrice(request.getUnitPrice());
        material.setInventory(request.getInventory());
        material.setCategory(request.getCategory());
        material.setStatus(1); // 默认启用

        Material savedMaterial = materialRepository.save(material);
        return convertToDTO(savedMaterial);
    }

    /**
     * 更新材料信息
     */
    public MaterialDTO updateMaterial(Long id, MaterialRequest request) {
        Material material = materialRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("材料不存在"));

        // 检查材料名称和规格是否被其他记录使用
        if (!material.getMaterialName().equals(request.getMaterialName()) ||
            !material.getSpecification().equals(request.getSpecification())) {
            if (materialRepository.existsByMaterialNameAndSpecification(
                    request.getMaterialName(), request.getSpecification())) {
                throw new BusinessException("相同名称和规格的材料已存在");
            }
        }

        material.setMaterialName(request.getMaterialName());
        material.setSpecification(request.getSpecification());
        material.setUnit(request.getUnit());
        material.setUnitPrice(request.getUnitPrice());
        material.setInventory(request.getInventory());
        material.setCategory(request.getCategory());

        Material savedMaterial = materialRepository.save(material);
        return convertToDTO(savedMaterial);
    }

    /**
     * 删除材料
     */
    public void deleteMaterial(Long id) {
        Material material = materialRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("材料不存在"));

        material.setStatus(0); // 软删除
        materialRepository.save(material);
    }

    /**
     * 获取所有材料分类
     */
    @Transactional(readOnly = true)
    public List<String> getCategories() {
        return materialRepository.findDistinctCategories();
    }

    /**
     * 将Material实体转换为MaterialDTO
     */
    private MaterialDTO convertToDTO(Material material) {
        return new MaterialDTO(
                material.getMaterialId(),
                material.getMaterialName(),
                material.getSpecification(),
                material.getUnit(),
                material.getUnitPrice(),
                material.getInventory(),
                material.getCategory(),
                material.getStatus(),
                material.getCreateTime(),
                material.getUpdateTime()
        );
    }
}
