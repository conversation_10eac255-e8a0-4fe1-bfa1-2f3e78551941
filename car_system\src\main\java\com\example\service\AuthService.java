package com.example.service;

import com.example.dto.request.LoginRequest;
import com.example.dto.response.LoginResponse;
import com.example.entity.Technician;
import com.example.entity.User;
import com.example.exception.BusinessException;
import com.example.repository.TechnicianRepository;
import com.example.repository.UserRepository;
import com.example.security.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class AuthService {
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private TechnicianRepository technicianRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    /**
     * 用户登录
     */
    public LoginResponse login(LoginRequest request) {
        String username = request.getUsername();
        String password = request.getPassword();
        String userType = request.getUserType();
        
        // 根据用户类型查找用户
        switch (userType.toLowerCase()) {
            case "user":
            case "admin":
                return loginUser(username, password, userType);
            case "technician":
                return loginTechnician(username, password);
            default:
                throw new BusinessException("不支持的用户类型");
        }
    }
    
    /**
     * 普通用户和管理员登录
     */
    private LoginResponse loginUser(String username, String password, String userType) {
        User.UserType expectedUserType = userType.equals("admin") ? User.UserType.ADMIN : User.UserType.USER;
        
        Optional<User> userOpt = userRepository.findByUsernameAndUserType(username, expectedUserType);
        if (userOpt.isEmpty()) {
            throw new BusinessException("用户名或密码错误");
        }
        
        User user = userOpt.get();
        
        // 检查用户状态
        if (user.getStatus() == 0) {
            throw new BusinessException("账户已被禁用，请联系管理员");
        }
        
        // 验证密码
        if (!passwordEncoder.matches(password, user.getPassword())) {
            throw new BusinessException("用户名或密码错误");
        }
        
        // 生成token
        String token = jwtUtil.generateToken(username, userType.toUpperCase(), user.getUserId());
        
        // 构建响应
        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo(
            user.getUserId(),
            user.getUsername(),
            user.getRealName(),
            userType
        );
        
        return new LoginResponse(token, userInfo, jwtUtil.getExpirationTime());
    }
    
    /**
     * 技师登录
     */
    private LoginResponse loginTechnician(String username, String password) {
        Optional<Technician> technicianOpt = technicianRepository.findByUsername(username);
        if (technicianOpt.isEmpty()) {
            throw new BusinessException("用户名或密码错误");
        }
        
        Technician technician = technicianOpt.get();
        
        // 检查技师状态
        if (technician.getStatus() == 0) {
            throw new BusinessException("账户已被禁用，请联系管理员");
        }
        
        // 验证密码
        if (!passwordEncoder.matches(password, technician.getPassword())) {
            throw new BusinessException("用户名或密码错误");
        }
        
        // 生成token
        String token = jwtUtil.generateToken(username, "TECHNICIAN", technician.getTechnicianId());
        
        // 构建响应
        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo(
            technician.getTechnicianId(),
            technician.getUsername(),
            technician.getRealName(),
            "technician"
        );
        
        return new LoginResponse(token, userInfo, jwtUtil.getExpirationTime());
    }
    
    /**
     * 刷新token
     */
    public LoginResponse refreshToken(String refreshToken) {
        try {
            String newToken = jwtUtil.refreshAccessToken(refreshToken);
            String username = jwtUtil.getUsernameFromToken(newToken);
            String userType = jwtUtil.getUserTypeFromToken(newToken);
            Long userId = jwtUtil.getUserIdFromToken(newToken);
            
            LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo(
                userId,
                username,
                null, // 刷新token时不返回真实姓名
                userType.toLowerCase()
            );
            
            return new LoginResponse(newToken, userInfo, jwtUtil.getExpirationTime());
        } catch (Exception e) {
            throw new BusinessException("刷新token失败：" + e.getMessage());
        }
    }
    
    /**
     * 用户登出（这里只是验证token有效性，实际的token失效需要在客户端处理）
     */
    public void logout(String token) {
        if (!jwtUtil.isTokenValid(token)) {
            throw new BusinessException("无效的token");
        }
        // 在实际应用中，可以将token加入黑名单
        // 这里简单验证token有效性即可
    }
}
