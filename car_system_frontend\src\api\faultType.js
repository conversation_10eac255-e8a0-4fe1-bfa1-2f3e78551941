import api from './index'

export const faultTypeAPI = {
  // 获取故障类型列表
  getList(params = {}) {
    return api.get('/fault-types', { params })
  },

  // 获取故障类型详情
  getById(id) {
    return api.get(`/fault-types/${id}`)
  },

  // 创建故障类型
  create(data) {
    return api.post('/fault-types', data)
  },

  // 更新故障类型
  update(id, data) {
    return api.put(`/fault-types/${id}`, data)
  },

  // 删除故障类型
  delete(id) {
    return api.delete(`/fault-types/${id}`)
  }
}
