<template>
  <div class="orders-page">
    <div class="page-header">
      <h1>我的工单</h1>
      <el-button type="primary" @click="$router.push('/user/orders/create')">
        <el-icon><Plus /></el-icon>
        提交工单
      </el-button>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline class="filter-form">
        <el-form-item label="工单状态">
          <el-select
            v-model="filters.status"
            placeholder="全部状态"
            clearable
            style="width: 150px"
            @change="fetchOrders"
          >
            <el-option label="待处理" value="pending" />
            <el-option label="已分配" value="assigned" />
            <el-option label="已接受" value="accepted" />
            <el-option label="进行中" value="in_progress" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>

        <el-form-item label="车牌号">
          <el-input
            v-model="filters.licensePlate"
            placeholder="请输入车牌号"
            clearable
            style="width: 150px"
            @keyup.enter="fetchOrders"
          />
        </el-form-item>

        <el-form-item label="故障类型">
          <el-select
            v-model="filters.faultTypeId"
            placeholder="全部类型"
            clearable
            style="width: 150px"
            @change="fetchOrders"
          >
            <el-option
              v-for="faultType in faultTypes"
              :key="faultType.faultTypeId"
              :label="faultType.typeName"
              :value="faultType.faultTypeId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="提交时间">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
            @change="fetchOrders"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="fetchOrders">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetFilters">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 工单列表 -->
    <el-card>
      <el-table
        v-loading="loading"
        :data="orders"
        style="width: 100%"
        empty-text="暂无工单记录"
        @row-click="viewOrderDetail"
      >
        <el-table-column prop="orderId" label="工单号" width="100" />

        <el-table-column label="车辆信息" width="180">
          <template #default="{ row }">
            <div class="vehicle-info">
              <div class="license-plate">{{ row.vehicle?.licensePlate }}</div>
              <div class="vehicle-detail">
                {{ row.vehicle?.brand }} {{ row.vehicle?.model }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="faultType.typeName" label="故障类型" width="120" />

        <el-table-column label="故障描述" min-width="200">
          <template #default="{ row }">
            <el-tooltip
              :content="row.description"
              placement="top"
              :disabled="row.description.length <= 50"
            >
              <div class="description-text">
                {{ row.description.length > 50 ? row.description.substring(0, 50) + '...' : row.description }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column label="紧急程度" width="100">
          <template #default="{ row }">
            <el-tag :type="getUrgencyType(row.urgencyLevel)" size="small">
              {{ getUrgencyText(row.urgencyLevel) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="提交时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.submitTime) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button
              type="text"
              size="small"
              @click.stop="viewOrderDetail(row)"
            >
              查看详情
            </el-button>
            <el-button
              v-if="canFeedback(row)"
              type="text"
              size="small"
              style="color: #e6a23c"
              @click.stop="showFeedbackDialog(row)"
            >
              评价
            </el-button>
            <span
              v-else-if="row.status === 'completed' && row.feedback"
              class="feedback-status"
              style="color: #67c23a; font-size: 12px;"
            >
              已评价
            </span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchOrders"
          @current-change="fetchOrders"
        />
      </div>
    </el-card>

    <!-- 反馈对话框 -->
    <el-dialog
      v-model="feedbackDialog.visible"
      title="工单评价"
      width="500px"
      @close="resetFeedbackForm"
    >
      <el-form
        ref="feedbackFormRef"
        :model="feedbackForm"
        :rules="feedbackRules"
        label-width="80px"
      >
        <el-form-item label="评分" prop="rating">
          <el-rate
            v-model="feedbackForm.rating"
            :max="5"
            show-text
            :texts="['极差', '失望', '一般', '满意', '惊喜']"
          />
        </el-form-item>

        <el-form-item label="评价内容" prop="content">
          <el-input
            v-model="feedbackForm.content"
            type="textarea"
            :rows="4"
            placeholder="请描述您对本次维修服务的评价"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="feedbackDialog.visible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="feedbackSubmitting"
          @click="submitFeedback"
        >
          提交评价
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Refresh
} from '@element-plus/icons-vue'
import { userAPI } from '@/api/user'
import { faultTypeAPI } from '@/api/faultType'
import { orderAPI } from '@/api/order'
import dayjs from 'dayjs'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const feedbackSubmitting = ref(false)
const orders = ref([])
const faultTypes = ref([])

// 表单引用
const feedbackFormRef = ref()

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 筛选条件
const filters = reactive({
  status: '',
  licensePlate: '',
  faultTypeId: null,
  dateRange: null
})

// 反馈对话框
const feedbackDialog = reactive({
  visible: false,
  orderId: null
})

// 反馈表单
const feedbackForm = reactive({
  rating: 5,
  content: ''
})

// 反馈表单验证规则
const feedbackRules = {
  rating: [
    { required: true, message: '请选择评分', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入评价内容', trigger: 'blur' },
    { min: 10, message: '评价内容至少10个字符', trigger: 'blur' }
  ]
}

// 获取工单列表
const fetchOrders = async () => {
  try {
    loading.value = true

    const params = {
      page: pagination.page,
      size: pagination.size
    }

    // 添加筛选条件
    if (filters.status) params.status = filters.status
    if (filters.licensePlate) params.licensePlate = filters.licensePlate
    if (filters.faultTypeId) params.faultTypeId = filters.faultTypeId
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.startDate = filters.dateRange[0]
      params.endDate = filters.dateRange[1]
    }

    const response = await userAPI.getCurrentUserOrders(params)
    console.log('User Orders API Response:', response)
    console.log('Response data structure:', response.data)
    console.log('Orders content:', response.data.data?.content)
    console.log('Page info:', response.data.data?.page)

    // 处理分页响应数据结构
    if (response.data && response.data.data) {
      // PageResponse结构: { success: true, data: { content: [], page: {} } }
      orders.value = response.data.data.content || []
      pagination.total = response.data.data.page?.totalElements || 0
    } else if (response.data && Array.isArray(response.data)) {
      // 直接返回数组的情况（向后兼容）
      orders.value = response.data
      pagination.total = response.data.length
    } else {
      orders.value = []
      pagination.total = 0
    }

    console.log('Final orders array:', orders.value)
    console.log('Final pagination total:', pagination.total)
  } catch (error) {
    console.error('Failed to fetch orders:', error)
    ElMessage.error('获取工单列表失败')
  } finally {
    loading.value = false
  }
}

// 获取故障类型列表
const fetchFaultTypes = async () => {
  try {
    const response = await faultTypeAPI.getList()

    // 处理分页响应数据结构
    if (response.data && response.data.data) {
      faultTypes.value = response.data.data.content || []
    } else if (response.data && Array.isArray(response.data)) {
      faultTypes.value = response.data
    } else {
      faultTypes.value = []
    }
  } catch (error) {
    console.error('Failed to fetch fault types:', error)
  }
}

// 重置筛选条件
const resetFilters = () => {
  Object.assign(filters, {
    status: '',
    licensePlate: '',
    faultTypeId: null,
    dateRange: null
  })
  fetchOrders()
}

// 查看工单详情
const viewOrderDetail = (order) => {
  router.push(`/user/orders/${order.orderId}`)
}



// 显示反馈对话框
const showFeedbackDialog = (order) => {
  feedbackDialog.orderId = order.orderId
  feedbackDialog.visible = true
}

// 提交反馈
const submitFeedback = async () => {
  if (!feedbackFormRef.value) return

  try {
    await feedbackFormRef.value.validate()
    feedbackSubmitting.value = true

    await orderAPI.submitFeedback(feedbackDialog.orderId, feedbackForm)

    ElMessage.success('评价提交成功')
    feedbackDialog.visible = false
    fetchOrders()
  } catch (error) {
    console.error('Failed to submit feedback:', error)
  } finally {
    feedbackSubmitting.value = false
  }
}

// 重置反馈表单
const resetFeedbackForm = () => {
  Object.assign(feedbackForm, {
    rating: 5,
    content: ''
  })
  if (feedbackFormRef.value) {
    feedbackFormRef.value.resetFields()
  }
}

// 判断是否可以评价
const canFeedback = (order) => {
  return order.status === 'completed' && !order.feedback
}

// 获取紧急程度类型
const getUrgencyType = (level) => {
  const typeMap = {
    low: 'info',
    normal: 'primary',
    high: 'warning',
    urgent: 'danger'
  }
  return typeMap[level] || 'info'
}

// 获取紧急程度文本
const getUrgencyText = (level) => {
  const textMap = {
    low: '低',
    normal: '中',
    high: '高',
    urgent: '紧急'
  }
  return textMap[level] || level
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    pending: 'warning',
    assigned: 'info',
    accepted: 'primary',
    in_progress: 'warning',
    completed: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    pending: '待处理',
    assigned: '已分配',
    accepted: '已接受',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

// 格式化日期
const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchOrders()
  fetchFaultTypes()
})
</script>

<style scoped>
.orders-page {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  margin: 0;
}

.vehicle-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.license-plate {
  font-weight: 600;
  color: #409eff;
}

.vehicle-detail {
  font-size: 12px;
  color: #999;
}

.description-text {
  line-height: 1.4;
  word-break: break-word;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .page-header h1 {
    font-size: 20px;
  }

  .filter-form {
    :deep(.el-form-item) {
      margin-right: 10px;
      margin-bottom: 10px;
    }
  }

  :deep(.el-table) {
    font-size: 12px;
  }

  :deep(.el-table .el-table__cell) {
    padding: 8px 4px;
  }
}

@media (max-width: 480px) {
  .filter-form {
    :deep(.el-form-item) {
      display: block;
      margin-right: 0;
    }

    :deep(.el-form-item__content) {
      margin-left: 0 !important;
    }
  }

  :deep(.el-pagination) {
    justify-content: center;
  }

  :deep(.el-pagination .el-pagination__sizes),
  :deep(.el-pagination .el-pagination__jump) {
    display: none;
  }
}
</style>
