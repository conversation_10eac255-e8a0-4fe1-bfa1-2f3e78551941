package com.example.dto.response;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 技师信息DTO
 */
public class TechnicianDTO {

    private Long technicianId;
    private String username;
    private String realName;
    private String phone;
    private String email;
    private String specialty;
    private String specialtyName;
    private BigDecimal hourlyRate;
    private LocalDate hireDate;
    private Integer status;
    private Integer workload;
    private BigDecimal rating;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;

    public TechnicianDTO() {}

    public TechnicianDTO(Long technicianId, String username, String realName, String phone,
                        String email, String specialty, String specialtyName, BigDecimal hourlyRate,
                        LocalDate hireDate, Integer status, Integer workload, BigDecimal rating,
                        LocalDateTime createTime, LocalDateTime updateTime) {
        this.technicianId = technicianId;
        this.username = username;
        this.realName = realName;
        this.phone = phone;
        this.email = email;
        this.specialty = specialty;
        this.specialtyName = specialtyName;
        this.hourlyRate = hourlyRate;
        this.hireDate = hireDate;
        this.status = status;
        this.workload = workload;
        this.rating = rating;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }

    public Long getTechnicianId() {
        return technicianId;
    }

    public void setTechnicianId(Long technicianId) {
        this.technicianId = technicianId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getSpecialty() {
        return specialty;
    }

    public void setSpecialty(String specialty) {
        this.specialty = specialty;
    }

    public String getSpecialtyName() {
        return specialtyName;
    }

    public void setSpecialtyName(String specialtyName) {
        this.specialtyName = specialtyName;
    }

    public BigDecimal getHourlyRate() {
        return hourlyRate;
    }

    public void setHourlyRate(BigDecimal hourlyRate) {
        this.hourlyRate = hourlyRate;
    }

    public LocalDate getHireDate() {
        return hireDate;
    }

    public void setHireDate(LocalDate hireDate) {
        this.hireDate = hireDate;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getWorkload() {
        return workload;
    }

    public void setWorkload(Integer workload) {
        this.workload = workload;
    }

    public BigDecimal getRating() {
        return rating;
    }

    public void setRating(BigDecimal rating) {
        this.rating = rating;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}
