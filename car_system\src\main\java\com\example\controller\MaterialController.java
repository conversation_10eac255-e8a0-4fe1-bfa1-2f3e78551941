package com.example.controller;

import com.example.dto.request.MaterialRequest;
import com.example.dto.response.ApiResponse;
import com.example.dto.response.MaterialDTO;
import com.example.dto.response.PageResponse;
import com.example.service.MaterialService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 材料控制器
 */
@RestController
@RequestMapping("/materials")
public class MaterialController {

    @Autowired
    private MaterialService materialService;

    /**
     * 获取材料列表
     */
    @GetMapping
    @PreAuthorize("hasRole('TECHNICIAN') or hasRole('ADMIN')")
    public ApiResponse<PageResponse<MaterialDTO>> getMaterials(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String stockStatus) {

        Pageable pageable = PageRequest.of(page - 1, size, Sort.by("createTime").descending());
        PageResponse<MaterialDTO> materials = materialService.getMaterials(search, category, stockStatus, pageable);
        return ApiResponse.success("获取材料列表成功", materials);
    }

    /**
     * 获取材料详情
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('TECHNICIAN') or hasRole('ADMIN')")
    public ApiResponse<MaterialDTO> getMaterial(@PathVariable Long id) {
        MaterialDTO material = materialService.getMaterial(id);
        return ApiResponse.success("获取材料详情成功", material);
    }

    /**
     * 添加材料
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<MaterialDTO> addMaterial(@Valid @RequestBody MaterialRequest request) {
        MaterialDTO material = materialService.addMaterial(request);
        return ApiResponse.success("添加材料成功", material);
    }

    /**
     * 更新材料信息
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<MaterialDTO> updateMaterial(@PathVariable Long id,
                                                  @Valid @RequestBody MaterialRequest request) {
        MaterialDTO material = materialService.updateMaterial(id, request);
        return ApiResponse.success("更新材料信息成功", material);
    }

    /**
     * 删除材料
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> deleteMaterial(@PathVariable Long id) {
        materialService.deleteMaterial(id);
        return ApiResponse.success("删除材料成功", null);
    }

    /**
     * 获取材料分类列表
     */
    @GetMapping("/categories")
    @PreAuthorize("hasRole('TECHNICIAN') or hasRole('ADMIN')")
    public ApiResponse<List<String>> getCategories() {
        List<String> categories = materialService.getCategories();
        return ApiResponse.success("获取材料分类成功", categories);
    }
}
