package com.example.repository;

import com.example.entity.OrderFeedback;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface OrderFeedbackRepository extends JpaRepository<OrderFeedback, Long>, JpaSpecificationExecutor<OrderFeedback> {

    /**
     * 根据工单ID查找反馈
     */
    Optional<OrderFeedback> findByRepairOrderOrderId(Long orderId);

    /**
     * 根据评分查找反馈
     */
    Page<OrderFeedback> findByRating(Integer rating, Pageable pageable);

    /**
     * 根据评分范围查找反馈
     */
    @Query("SELECT of FROM OrderFeedback of WHERE of.rating BETWEEN :minRating AND :maxRating")
    Page<OrderFeedback> findByRatingBetween(@Param("minRating") Integer minRating,
                                           @Param("maxRating") Integer maxRating,
                                           Pageable pageable);

    /**
     * 根据时间范围查找反馈
     */
    @Query("SELECT of FROM OrderFeedback of WHERE of.feedbackTime BETWEEN :startTime AND :endTime")
    Page<OrderFeedback> findByFeedbackTimeBetween(@Param("startTime") LocalDateTime startTime,
                                                  @Param("endTime") LocalDateTime endTime,
                                                  Pageable pageable);

    /**
     * 根据用户ID查找反馈
     */
    @Query("SELECT of FROM OrderFeedback of WHERE of.repairOrder.user.userId = :userId")
    Page<OrderFeedback> findByUserId(@Param("userId") Long userId, Pageable pageable);

    /**
     * 根据技师ID查找反馈
     */
    @Query("SELECT of FROM OrderFeedback of JOIN of.repairOrder.technicianAssignments ta WHERE ta.technician.technicianId = :technicianId")
    Page<OrderFeedback> findByTechnicianId(@Param("technicianId") Long technicianId, Pageable pageable);

    /**
     * 计算技师的平均评分
     */
    @Query("SELECT AVG(of.rating) FROM OrderFeedback of JOIN of.repairOrder.technicianAssignments ta WHERE ta.technician.technicianId = :technicianId")
    BigDecimal calculateAverageRatingByTechnicianId(@Param("technicianId") Long technicianId);

    /**
     * 统计评分分布
     */
    @Query("SELECT of.rating, COUNT(of) FROM OrderFeedback of " +
           "WHERE of.feedbackTime BETWEEN :startTime AND :endTime " +
           "GROUP BY of.rating ORDER BY of.rating")
    List<Object[]> getRatingDistribution(@Param("startTime") LocalDateTime startTime,
                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 计算整体平均评分
     */
    @Query("SELECT AVG(of.rating) FROM OrderFeedback of WHERE of.feedbackTime BETWEEN :startTime AND :endTime")
    BigDecimal calculateOverallAverageRating(@Param("startTime") LocalDateTime startTime,
                                            @Param("endTime") LocalDateTime endTime);

    /**
     * 统计反馈数量
     */
    long countByFeedbackTimeBetween(LocalDateTime startTime, LocalDateTime endTime);
}
