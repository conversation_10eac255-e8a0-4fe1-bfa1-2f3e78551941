package com.example.dto.response;

import java.time.LocalDateTime;

/**
 * 催单信息DTO
 */
public class UrgentRequestDTO {

    private Long urgentId;
    private String reason;
    private LocalDateTime urgentTime;
    private String status;
    private String statusDisplayName;

    public UrgentRequestDTO() {}

    public UrgentRequestDTO(Long urgentId, String reason, LocalDateTime urgentTime, 
                           String status, String statusDisplayName) {
        this.urgentId = urgentId;
        this.reason = reason;
        this.urgentTime = urgentTime;
        this.status = status;
        this.statusDisplayName = statusDisplayName;
    }

    public Long getUrgentId() {
        return urgentId;
    }

    public void setUrgentId(Long urgentId) {
        this.urgentId = urgentId;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public LocalDateTime getUrgentTime() {
        return urgentTime;
    }

    public void setUrgentTime(LocalDateTime urgentTime) {
        this.urgentTime = urgentTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusDisplayName() {
        return statusDisplayName;
    }

    public void setStatusDisplayName(String statusDisplayName) {
        this.statusDisplayName = statusDisplayName;
    }
}
