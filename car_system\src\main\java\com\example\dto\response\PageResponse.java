package com.example.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.springframework.data.domain.Page;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 分页响应格式
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PageResponse<T> {
    
    private boolean success;
    private String message;
    private PageData<T> data;
    private Integer code;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    private LocalDateTime timestamp;
    
    public PageResponse() {
        this.timestamp = LocalDateTime.now();
    }
    
    public PageResponse(boolean success, String message, PageData<T> data, Integer code) {
        this();
        this.success = success;
        this.message = message;
        this.data = data;
        this.code = code;
    }
    
    // 成功响应
    public static <T> PageResponse<T> success(Page<T> page) {
        PageData<T> pageData = new PageData<>(page);
        return new PageResponse<>(true, "获取成功", pageData, 200);
    }
    
    public static <T> PageResponse<T> success(String message, Page<T> page) {
        PageData<T> pageData = new PageData<>(page);
        return new PageResponse<>(true, message, pageData, 200);
    }
    
    public static <T> PageResponse<T> success(List<T> content, PageInfo pageInfo) {
        PageData<T> pageData = new PageData<>(content, pageInfo);
        return new PageResponse<>(true, "获取成功", pageData, 200);
    }
    
    // 失败响应
    public static <T> PageResponse<T> error(String message) {
        return new PageResponse<>(false, message, null, 400);
    }
    
    public static <T> PageResponse<T> error(String message, Integer code) {
        return new PageResponse<>(false, message, null, code);
    }
    
    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public PageData<T> getData() {
        return data;
    }
    
    public void setData(PageData<T> data) {
        this.data = data;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public void setCode(Integer code) {
        this.code = code;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
    
    /**
     * 分页数据类
     */
    public static class PageData<T> {
        private List<T> content;
        private PageInfo page;
        
        public PageData() {}
        
        public PageData(Page<T> page) {
            this.content = page.getContent();
            this.page = new PageInfo(
                page.getNumber() + 1, // Spring Data JPA的页码从0开始，API文档要求从1开始
                page.getSize(),
                page.getTotalElements(),
                page.getTotalPages()
            );
        }
        
        public PageData(List<T> content, PageInfo pageInfo) {
            this.content = content;
            this.page = pageInfo;
        }
        
        public List<T> getContent() {
            return content;
        }
        
        public void setContent(List<T> content) {
            this.content = content;
        }
        
        public PageInfo getPage() {
            return page;
        }
        
        public void setPage(PageInfo page) {
            this.page = page;
        }
    }
    
    /**
     * 分页信息类
     */
    public static class PageInfo {
        private int number;
        private int size;
        private long totalElements;
        private int totalPages;
        
        public PageInfo() {}
        
        public PageInfo(int number, int size, long totalElements, int totalPages) {
            this.number = number;
            this.size = size;
            this.totalElements = totalElements;
            this.totalPages = totalPages;
        }
        
        public int getNumber() {
            return number;
        }
        
        public void setNumber(int number) {
            this.number = number;
        }
        
        public int getSize() {
            return size;
        }
        
        public void setSize(int size) {
            this.size = size;
        }
        
        public long getTotalElements() {
            return totalElements;
        }
        
        public void setTotalElements(long totalElements) {
            this.totalElements = totalElements;
        }
        
        public int getTotalPages() {
            return totalPages;
        }
        
        public void setTotalPages(int totalPages) {
            this.totalPages = totalPages;
        }
    }
}
