package com.example.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@ConfigurationProperties(prefix = "app")
public class AppProperties {
    
    private DefaultAdmin defaultAdmin;
    private List<Specialty> specialties;
    
    public DefaultAdmin getDefaultAdmin() {
        return defaultAdmin;
    }
    
    public void setDefaultAdmin(DefaultAdmin defaultAdmin) {
        this.defaultAdmin = defaultAdmin;
    }
    
    public List<Specialty> getSpecialties() {
        return specialties;
    }
    
    public void setSpecialties(List<Specialty> specialties) {
        this.specialties = specialties;
    }
    
    public static class DefaultAdmin {
        private String username;
        private String password;
        private String realName;
        private String phone;
        private String email;
        
        public String getUsername() {
            return username;
        }
        
        public void setUsername(String username) {
            this.username = username;
        }
        
        public String getPassword() {
            return password;
        }
        
        public void setPassword(String password) {
            this.password = password;
        }
        
        public String getRealName() {
            return realName;
        }
        
        public void setRealName(String realName) {
            this.realName = realName;
        }
        
        public String getPhone() {
            return phone;
        }
        
        public void setPhone(String phone) {
            this.phone = phone;
        }
        
        public String getEmail() {
            return email;
        }
        
        public void setEmail(String email) {
            this.email = email;
        }
    }
    
    public static class Specialty {
        private String code;
        private String name;
        
        public String getCode() {
            return code;
        }
        
        public void setCode(String code) {
            this.code = code;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
    }
}
