package com.example.repository;

import com.example.entity.FaultType;
import com.example.entity.Technician;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FaultTypeRepository extends JpaRepository<FaultType, Long>, JpaSpecificationExecutor<FaultType> {

    /**
     * 根据状态查找故障类型
     */
    List<FaultType> findByStatus(Integer status);

    /**
     * 根据状态查找故障类型（分页）
     */
    Page<FaultType> findByStatus(Integer status, Pageable pageable);

    /**
     * 根据故障类型名称查找
     */
    List<FaultType> findByTypeName(String typeName);

    /**
     * 检查故障类型名称是否存在
     */
    boolean existsByTypeName(String typeName);

    /**
     * 根据所需工种查找故障类型
     */
    @Query("SELECT ft FROM FaultType ft JOIN ft.requiredSpecialties rs WHERE rs = :specialty AND ft.status = 1")
    List<FaultType> findByRequiredSpecialty(@Param("specialty") Technician.Specialty specialty);

    /**
     * 搜索故障类型（根据名称或描述）
     */
    @Query("SELECT ft FROM FaultType ft WHERE " +
           "(LOWER(ft.typeName) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(ft.description) LIKE LOWER(CONCAT('%', :search, '%'))) AND " +
           "ft.status = :status")
    Page<FaultType> searchByTypeNameOrDescription(@Param("search") String search,
                                                  @Param("status") Integer status,
                                                  Pageable pageable);

    /**
     * 根据预估工时范围查找故障类型
     */
    @Query("SELECT ft FROM FaultType ft WHERE ft.estimatedHours BETWEEN :minHours AND :maxHours AND ft.status = 1")
    List<FaultType> findByEstimatedHoursBetween(@Param("minHours") Integer minHours,
                                               @Param("maxHours") Integer maxHours);

    /**
     * 根据所需技师数量查找故障类型
     */
    List<FaultType> findByRequiredTechCountAndStatus(Integer requiredTechCount, Integer status);
}
