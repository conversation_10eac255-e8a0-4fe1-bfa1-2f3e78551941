package com.example.dto.response;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 材料信息DTO
 */
public class MaterialDTO {

    private Long materialId;
    private String materialName;
    private String specification;
    private String unit;
    private BigDecimal unitPrice;
    private Integer inventory;
    private String category;
    private Integer status;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;

    public MaterialDTO() {}

    public MaterialDTO(Long materialId, String materialName, String specification,
                      String unit, BigDecimal unitPrice, Integer inventory,
                      String category, Integer status,
                      LocalDateTime createTime, LocalDateTime updateTime) {
        this.materialId = materialId;
        this.materialName = materialName;
        this.specification = specification;
        this.unit = unit;
        this.unitPrice = unitPrice;
        this.inventory = inventory;
        this.category = category;
        this.status = status;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public void setMaterialId(Long materialId) {
        this.materialId = materialId;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public Integer getInventory() {
        return inventory;
    }

    public void setInventory(Integer inventory) {
        this.inventory = inventory;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}
