<template>
  <div class="admin-vehicles">
    <div class="page-header">
      <h1>车辆管理</h1>
      <p>管理系统中的所有车辆信息</p>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline class="filter-form">
        <el-form-item label="搜索">
          <el-input
            v-model="filters.search"
            placeholder="车牌号、品牌、型号"
            style="width: 200px"
            clearable
            @keyup.enter="fetchVehicles"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="品牌">
          <el-select
            v-model="filters.brand"
            placeholder="全部品牌"
            clearable
            style="width: 150px"
            @change="fetchVehicles"
          >
            <el-option
              v-for="brand in brands"
              :key="brand"
              :label="brand"
              :value="brand"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="年份">
          <el-select
            v-model="filters.year"
            placeholder="全部年份"
            clearable
            style="width: 120px"
            @change="fetchVehicles"
          >
            <el-option
              v-for="year in years"
              :key="year"
              :label="year"
              :value="year"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="fetchVehicles">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetFilters">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon total">
            <el-icon size="24"><Van /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.total }}</div>
            <div class="stat-label">总车辆数</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon brands">
            <el-icon size="24"><Collection /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.brands }}</div>
            <div class="stat-label">品牌数量</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon recent">
            <el-icon size="24"><Plus /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.recentAdded }}</div>
            <div class="stat-label">本月新增</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon active">
            <el-icon size="24"><Tools /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.activeRepairs }}</div>
            <div class="stat-label">维修中</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 车辆列表 -->
    <el-card>
      <!-- 移动端卡片视图 -->
      <div v-if="isMobile" class="mobile-vehicle-list">
        <div v-for="vehicle in vehicles" :key="vehicle.vehicleId" class="mobile-vehicle-card">
          <el-card shadow="hover" @click="viewVehicleDetail(vehicle)">
            <div class="vehicle-info">
              <div class="vehicle-header">
                <div class="license-plate">{{ vehicle.licensePlate }}</div>
                <el-tag type="primary" size="small">{{ vehicle.brand }}</el-tag>
              </div>
              <div class="vehicle-details">
                <div class="detail-item">
                  <span class="label">车型:</span>
                  <span class="value">{{ vehicle.brand }} {{ vehicle.model }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">年份:</span>
                  <span class="value">{{ vehicle.year }}年</span>
                </div>
                <div class="detail-item">
                  <span class="label">颜色:</span>
                  <span class="value">{{ vehicle.color }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">车主:</span>
                  <span class="value">{{ vehicle.user?.realName }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">VIN码:</span>
                  <span class="value">{{ vehicle.vin }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">注册时间:</span>
                  <span class="value">{{ formatDate(vehicle.registerDate) }}</span>
                </div>
              </div>
              <div class="vehicle-actions">
                <el-button type="primary" size="small" @click.stop="viewVehicleDetail(vehicle)">
                  <el-icon><View /></el-icon>
                  查看详情
                </el-button>

              </div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 桌面端表格视图 -->
      <div v-else class="table-responsive">
        <el-table
          v-loading="loading"
          :data="vehicles"
          style="width: 100%"
          empty-text="暂无车辆数据"
          stripe
          highlight-current-row
          @row-click="viewVehicleDetail"
        >
          <el-table-column label="车牌号" width="120">
            <template #default="{ row }">
              <div class="license-plate-cell">{{ row.licensePlate }}</div>
            </template>
          </el-table-column>

          <el-table-column label="车辆信息" min-width="200">
            <template #default="{ row }">
              <div class="vehicle-cell">
                <div class="vehicle-name">{{ row.brand }} {{ row.model }}</div>
                <div class="vehicle-detail">{{ row.year }}年 · {{ row.color }}</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="车主" width="120">
            <template #default="{ row }">
              {{ row.user?.realName }}
            </template>
          </el-table-column>

          <el-table-column prop="vin" label="VIN码" width="180" show-overflow-tooltip />
          <el-table-column prop="engineNumber" label="发动机号" width="140" show-overflow-tooltip />

          <el-table-column label="注册时间" width="120">
            <template #default="{ row }">
              {{ formatDate(row.registerDate) }}
            </template>
          </el-table-column>

          <el-table-column label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.createTime) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="small" @click.stop="viewVehicleDetail(row)">
                <el-icon><View /></el-icon>
                查看详情
              </el-button>

            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchVehicles"
          @current-change="fetchVehicles"
        />
      </div>
    </el-card>

    <!-- 车辆详情对话框 -->
    <el-dialog
      v-model="detailDialog.visible"
      title="车辆详情"
      width="600px"
    >
      <div v-if="selectedVehicle" class="vehicle-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="车牌号">
            {{ selectedVehicle.licensePlate }}
          </el-descriptions-item>
          <el-descriptions-item label="品牌">
            {{ selectedVehicle.brand }}
          </el-descriptions-item>
          <el-descriptions-item label="型号">
            {{ selectedVehicle.model }}
          </el-descriptions-item>
          <el-descriptions-item label="年份">
            {{ selectedVehicle.year }}年
          </el-descriptions-item>
          <el-descriptions-item label="颜色">
            {{ selectedVehicle.color }}
          </el-descriptions-item>
          <el-descriptions-item label="车主">
            {{ selectedVehicle.user?.realName }}
          </el-descriptions-item>
          <el-descriptions-item label="VIN码" :span="2">
            {{ selectedVehicle.vin }}
          </el-descriptions-item>
          <el-descriptions-item label="发动机号" :span="2">
            {{ selectedVehicle.engineNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="注册时间">
            {{ formatDate(selectedVehicle.registerDate) }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDate(selectedVehicle.createTime) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>


  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Search, Refresh, Van, Collection, Plus, Tools, View
} from '@element-plus/icons-vue'
import { adminAPI } from '@/api/admin'
import { useAppStore } from '@/stores/app'
import dayjs from 'dayjs'

const appStore = useAppStore()

// 计算属性
const isMobile = computed(() => appStore.isMobile)

// 响应式数据
const loading = ref(false)
const vehicles = ref([])
const selectedVehicle = ref(null)
const brands = ref([])
const years = ref([])

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 筛选条件
const filters = reactive({
  search: '',
  brand: '',
  year: null
})

// 统计数据
const stats = reactive({
  total: 0,
  brands: 0,
  recentAdded: 0,
  activeRepairs: 0
})

// 详情对话框
const detailDialog = reactive({
  visible: false
})



// 获取车辆列表
const fetchVehicles = async () => {
  try {
    loading.value = true

    const params = {
      page: pagination.page,
      size: pagination.size
    }

    // 添加搜索条件
    if (filters.search) params.search = filters.search
    if (filters.brand) params.brand = filters.brand
    if (filters.year) params.year = filters.year

    const response = await adminAPI.getAllVehicles(params)
    vehicles.value = response.data.data?.content || []
    pagination.total = response.data.data?.page?.totalElements || 0

    // 更新统计数据
    updateStats()

    // 更新品牌和年份选项
    updateFilterOptions()
  } catch (error) {
    console.error('Failed to fetch vehicles:', error)
    ElMessage.error('获取车辆列表失败')
  } finally {
    loading.value = false
  }
}

// 更新统计数据
const updateStats = () => {
  stats.total = vehicles.value.length

  // 统计品牌数量
  const uniqueBrands = new Set(vehicles.value.map(v => v.brand))
  stats.brands = uniqueBrands.size

  // 统计本月新增（模拟数据）
  const currentMonth = dayjs().format('YYYY-MM')
  stats.recentAdded = vehicles.value.filter(v =>
    dayjs(v.createTime).format('YYYY-MM') === currentMonth
  ).length

  // 统计维修中车辆（这里需要额外的API调用，暂时使用模拟数据）
  stats.activeRepairs = 0
}

// 更新筛选选项
const updateFilterOptions = () => {
  // 更新品牌选项
  const uniqueBrands = [...new Set(vehicles.value.map(v => v.brand))].sort()
  brands.value = uniqueBrands

  // 更新年份选项
  const uniqueYears = [...new Set(vehicles.value.map(v => v.year))].sort((a, b) => b - a)
  years.value = uniqueYears
}

// 重置筛选条件
const resetFilters = () => {
  Object.assign(filters, {
    search: '',
    brand: '',
    year: null
  })
  pagination.page = 1
  fetchVehicles()
}

// 查看车辆详情
const viewVehicleDetail = (vehicle) => {
  selectedVehicle.value = vehicle
  detailDialog.visible = true
}



// 格式化日期
const formatDate = (date) => {
  if (!date) return '-'
  return dayjs(date).format('YYYY-MM-DD')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchVehicles()
})
</script>

<style scoped>
.admin-vehicles {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-form .el-form-item {
  margin-bottom: 0;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.stat-icon.brands {
  background: linear-gradient(135deg, #67c23a, #95d475);
}

.stat-icon.recent {
  background: linear-gradient(135deg, #e6a23c, #f0c78a);
}

.stat-icon.active {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

/* 移动端车辆卡片 */
.mobile-vehicle-list {
  display: grid;
  gap: 16px;
}

.mobile-vehicle-card {
  cursor: pointer;
}

.vehicle-info {
  padding: 0;
}

.vehicle-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.license-plate {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.vehicle-details {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item .label {
  color: #606266;
  font-weight: 500;
  min-width: 80px;
}

.detail-item .value {
  color: #303133;
  text-align: right;
  flex: 1;
}

.vehicle-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 桌面端表格 */
.table-responsive {
  overflow-x: auto;
}

.license-plate-cell {
  font-weight: 600;
  color: #303133;
  font-size: 16px;
}

.vehicle-cell {
  display: flex;
  flex-direction: column;
}

.vehicle-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 2px;
}

.vehicle-detail {
  font-size: 12px;
  color: #909399;
}

.technicians {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.no-technician {
  color: #909399;
  font-style: italic;
}

/* 分页 */
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 车辆详情 */
.vehicle-detail {
  padding: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-vehicles {
    padding: 16px;
  }

  .filter-form {
    flex-direction: column;
  }

  .filter-form .el-form-item {
    width: 100%;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 8px;
  }

  .vehicle-actions {
    justify-content: center;
  }

  .pagination-container {
    overflow-x: auto;
  }
}

@media (max-width: 480px) {
  .admin-vehicles {
    padding: 12px;
  }

  .page-header h1 {
    font-size: 20px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .vehicle-actions .el-button {
    flex: 1;
    min-width: 0;
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .detail-item .value {
    text-align: left;
  }
}
</style>