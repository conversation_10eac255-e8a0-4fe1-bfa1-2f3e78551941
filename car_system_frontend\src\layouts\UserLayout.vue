<template>
  <BaseLayout :menu-items="menuItems" />
</template>

<script setup>
import {
  House, User, Van, Document, Clock, Plus, Star
} from '@element-plus/icons-vue'
import BaseLayout from './BaseLayout.vue'

// 用户端菜单配置
const menuItems = [
  {
    path: '/user/dashboard',
    title: '仪表盘',
    icon: House
  },
  {
    path: '/user/profile',
    title: '个人资料',
    icon: User
  },
  {
    path: '/user/vehicles',
    title: '我的车辆',
    icon: Van
  },
  {
    path: '/user/orders',
    title: '我的工单',
    icon: Document,
    children: [
      {
        path: '/user/orders',
        title: '工单列表',
        icon: Document
      },
      {
        path: '/user/orders/create',
        title: '提交工单',
        icon: Plus
      }
    ]
  },
  {
    path: '/user/repair-history',
    title: '维修历史',
    icon: Clock
  },
  {
    path: '/user/feedback-history',
    title: '反馈历史',
    icon: Star
  }
]
</script>
