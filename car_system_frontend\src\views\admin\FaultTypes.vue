<template>
  <div class="admin-fault-types">
    <div class="page-header">
      <h1>故障类型管理</h1>
      <p>管理系统中的故障类型分类</p>
    </div>

    <!-- 搜索和操作 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline class="filter-form">
        <el-form-item label="搜索">
          <el-input
            v-model="filters.search"
            placeholder="类型名称、描述"
            style="width: 200px"
            clearable
            @keyup.enter="fetchFaultTypes"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="工种">
          <el-select
            v-model="filters.specialty"
            placeholder="全部工种"
            clearable
            style="width: 150px"
            @change="fetchFaultTypes"
          >
            <el-option
              v-for="specialty in specialties"
              :key="specialty.code"
              :label="specialty.name"
              :value="specialty.code"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="fetchFaultTypes">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetFilters">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="showCreateDialog">
            <el-icon><Plus /></el-icon>
            添加故障类型
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 故障类型列表 -->
    <el-card>
      <!-- 移动端卡片视图 -->
      <div v-if="isMobile" class="mobile-fault-type-list">
        <div v-for="faultType in faultTypes" :key="faultType.faultTypeId" class="mobile-fault-type-card">
          <el-card shadow="hover">
            <div class="fault-type-info">
              <div class="fault-type-header">
                <h3 class="fault-type-name">{{ faultType.typeName }}</h3>
                <el-tag :type="getSpecialtyType(faultType.specialty)" size="small">
                  {{ getSpecialtyName(faultType.specialty) }}
                </el-tag>
              </div>
              <div class="fault-type-details">
                <div class="detail-item">
                  <span class="label">描述:</span>
                  <span class="value">{{ faultType.description || '-' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">技师数量:</span>
                  <span class="value">{{ faultType.requiredTechCount || 1 }}人</span>
                </div>
                <div class="detail-item">
                  <span class="label">预计工时:</span>
                  <span class="value">{{ faultType.estimatedHours }}小时</span>
                </div>
                <div class="detail-item">
                  <span class="label">创建时间:</span>
                  <span class="value">{{ formatDate(faultType.createTime) }}</span>
                </div>
              </div>
              <div class="fault-type-actions">
                <el-button type="primary" size="small" @click="editFaultType(faultType)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button type="danger" size="small" @click="deleteFaultType(faultType)">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 桌面端表格视图 -->
      <div v-else class="table-responsive">
        <el-table
          v-loading="loading"
          :data="faultTypes"
          style="width: 100%"
          empty-text="暂无故障类型数据"
          stripe
          highlight-current-row
        >
          <el-table-column prop="typeName" label="类型名称" min-width="150" />

          <el-table-column label="工种" width="120">
            <template #default="{ row }">
              <el-tag :type="getSpecialtyType(row.specialty)" size="small">
                {{ getSpecialtyName(row.specialty) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />

          <el-table-column label="预计工时" width="100" align="center">
            <template #default="{ row }">
              {{ row.estimatedHours }}h
            </template>
          </el-table-column>

          <el-table-column label="技师数量" width="100" align="center">
            <template #default="{ row }">
              {{ row.requiredTechCount || 1 }}人
            </template>
          </el-table-column>

          <el-table-column label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.createTime) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="editFaultType(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button
                type="text"
                size="small"
                style="color: #f56c6c"
                @click="deleteFaultType(row)"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchFaultTypes"
          @current-change="fetchFaultTypes"
        />
      </div>
    </el-card>

    <!-- 创建/编辑故障类型对话框 -->
    <el-dialog
      v-model="faultTypeDialog.visible"
      :title="faultTypeDialog.isEdit ? '编辑故障类型' : '创建故障类型'"
      width="500px"
      @close="resetFaultTypeForm"
    >
      <el-form
        ref="faultTypeFormRef"
        :model="faultTypeForm"
        :rules="faultTypeRules"
        label-width="100px"
      >
        <el-form-item label="类型名称" prop="typeName">
          <el-input
            v-model="faultTypeForm.typeName"
            placeholder="请输入故障类型名称"
          />
        </el-form-item>

        <el-form-item label="工种" prop="specialty">
          <el-select
            v-model="faultTypeForm.specialty"
            placeholder="请选择工种"
            style="width: 100%"
          >
            <el-option
              v-for="specialty in specialties"
              :key="specialty.code"
              :label="specialty.name"
              :value="specialty.code"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="技师数量" prop="requiredTechCount">
          <el-input-number
            v-model="faultTypeForm.requiredTechCount"
            :min="1"
            :max="10"
            placeholder="请输入需要的技师数量"
            style="width: 100%"
          />
          <div class="form-tip">需要分配的技师人数</div>
        </el-form-item>

        <el-form-item label="预计工时" prop="estimatedHours">
          <el-input-number
            v-model="faultTypeForm.estimatedHours"
            :min="0.5"
            :step="0.5"
            :precision="1"
            placeholder="请输入预计工时"
            style="width: 100%"
          />
          <div class="form-tip">单位：小时</div>
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="faultTypeForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入故障类型描述"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="faultTypeDialog.visible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="submitting"
          @click="submitFaultTypeForm"
        >
          {{ faultTypeDialog.isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Refresh, Plus, Edit, Delete
} from '@element-plus/icons-vue'
import { faultTypeAPI } from '@/api/faultType'
import { technicianAPI } from '@/api/technician'
import { useAppStore } from '@/stores/app'
import dayjs from 'dayjs'

const appStore = useAppStore()

// 计算属性
const isMobile = computed(() => appStore.isMobile)

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const faultTypes = ref([])
const specialties = ref([])

// 表单引用
const faultTypeFormRef = ref()

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 筛选条件
const filters = reactive({
  search: '',
  specialty: ''
})

// 故障类型对话框
const faultTypeDialog = reactive({
  visible: false,
  isEdit: false,
  faultTypeId: null
})

// 故障类型表单
const faultTypeForm = reactive({
  typeName: '',
  specialty: '',
  requiredTechCount: 1,
  estimatedHours: null,
  description: ''
})

// 故障类型表单验证规则
const faultTypeRules = {
  typeName: [
    { required: true, message: '请输入故障类型名称', trigger: 'blur' },
    { min: 2, max: 50, message: '名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  specialty: [
    { required: true, message: '请选择工种', trigger: 'change' }
  ],
  requiredTechCount: [
    { required: true, message: '请输入技师数量', trigger: 'blur' },
    { type: 'number', min: 1, max: 10, message: '技师数量必须在1-10人之间', trigger: 'blur' }
  ],
  estimatedHours: [
    { required: true, message: '请输入预计工时', trigger: 'blur' },
    { type: 'number', min: 0.5, message: '预计工时必须大于0.5小时', trigger: 'blur' }
  ]
}

// 工种映射
const specialtyMap = {
  engine: { name: '发动机维修', type: 'primary' },
  transmission: { name: '变速箱维修', type: 'success' },
  brake: { name: '制动系统维修', type: 'warning' },
  electrical: { name: '电气系统维修', type: 'danger' },
  hvac: { name: '空调系统维修', type: 'info' },
  chassis: { name: '底盘维修', type: '' },
  body: { name: '车身维修', type: 'primary' },
  tire: { name: '轮胎维修', type: 'success' }
}

// 获取故障类型列表
const fetchFaultTypes = async () => {
  try {
    loading.value = true

    const params = {
      page: pagination.page,
      size: pagination.size
    }

    // 添加搜索条件
    if (filters.search) params.search = filters.search
    if (filters.specialty) params.specialty = filters.specialty

    console.log('Fetching fault types with params:', params)
    const response = await faultTypeAPI.getList(params)
    console.log('Fault types response:', response)

    // 处理分页响应数据结构
    if (response.data && response.data.data) {
      // PageResponse结构: { success: true, data: { content: [], page: {} } }
      faultTypes.value = response.data.data.content || []
      pagination.total = response.data.data.page?.totalElements || 0
    } else if (response.data && Array.isArray(response.data)) {
      // 直接返回数组的情况（向后兼容）
      faultTypes.value = response.data
      pagination.total = response.data.length
    } else {
      faultTypes.value = []
      pagination.total = 0
    }

    console.log('Processed fault types:', faultTypes.value)
    console.log('Total count:', pagination.total)
  } catch (error) {
    console.error('Failed to fetch fault types:', error)
    ElMessage.error('获取故障类型列表失败: ' + (error.response?.data?.message || error.message))
    faultTypes.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 获取工种列表
const fetchSpecialties = async () => {
  try {
    const response = await technicianAPI.getSpecialties()
    const specialtyList = response.data || []

    // 安全地转换为选项格式，处理可能的对象或字符串数据
    specialties.value = specialtyList.map(item => {
      // 如果 item 是字符串，直接使用
      if (typeof item === 'string') {
        return {
          code: item,
          name: specialtyMap[item]?.name || item
        }
      }
      // 如果 item 是对象，提取 code 属性
      else if (typeof item === 'object' && item !== null) {
        const code = item.code || item
        return {
          code: code,
          name: specialtyMap[code]?.name || code
        }
      }
      // 其他情况，使用默认值
      else {
        return {
          code: item,
          name: String(item)
        }
      }
    })
  } catch (error) {
    console.error('Failed to fetch specialties:', error)
    // 使用默认工种列表
    specialties.value = Object.keys(specialtyMap).map(code => ({
      code,
      name: specialtyMap[code].name
    }))
  }
}

// 重置筛选条件
const resetFilters = () => {
  Object.assign(filters, {
    search: '',
    specialty: ''
  })
  pagination.page = 1
  fetchFaultTypes()
}

// 显示创建故障类型对话框
const showCreateDialog = () => {
  faultTypeDialog.visible = true
  faultTypeDialog.isEdit = false
  faultTypeDialog.faultTypeId = null
  resetFaultTypeForm()
}

// 编辑故障类型
const editFaultType = (faultType) => {
  faultTypeDialog.visible = true
  faultTypeDialog.isEdit = true
  faultTypeDialog.faultTypeId = faultType.faultTypeId

  // 填充表单数据
  Object.assign(faultTypeForm, {
    typeName: faultType.typeName,
    specialty: faultType.specialty,
    requiredTechCount: faultType.requiredTechCount || 1,
    estimatedHours: faultType.estimatedHours,
    description: faultType.description || ''
  })
}

// 删除故障类型
const deleteFaultType = async (faultType) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除故障类型 "${faultType.typeName}" 吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await faultTypeAPI.delete(faultType.faultTypeId)
    ElMessage.success('故障类型删除成功')
    fetchFaultTypes()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete fault type:', error)
      ElMessage.error('删除故障类型失败')
    }
  }
}

// 重置故障类型表单
const resetFaultTypeForm = () => {
  Object.assign(faultTypeForm, {
    typeName: '',
    specialty: '',
    requiredTechCount: 1,
    estimatedHours: null,
    description: ''
  })

  // 清除表单验证
  if (faultTypeFormRef.value) {
    faultTypeFormRef.value.clearValidate()
  }
}

// 提交故障类型表单
const submitFaultTypeForm = async () => {
  try {
    // 表单验证
    if (!faultTypeFormRef.value) return
    await faultTypeFormRef.value.validate()

    submitting.value = true

    if (faultTypeDialog.isEdit) {
      // 更新故障类型
      await faultTypeAPI.update(faultTypeDialog.faultTypeId, faultTypeForm)
      ElMessage.success('故障类型更新成功')
    } else {
      // 创建故障类型
      await faultTypeAPI.create(faultTypeForm)
      ElMessage.success('故障类型创建成功')
    }

    faultTypeDialog.visible = false
    fetchFaultTypes()
  } catch (error) {
    console.error('Failed to submit fault type form:', error)
    ElMessage.error(faultTypeDialog.isEdit ? '更新故障类型失败' : '创建故障类型失败')
  } finally {
    submitting.value = false
  }
}

// 获取工种名称
const getSpecialtyName = (specialty) => {
  return specialtyMap[specialty]?.name || specialty
}

// 获取工种类型
const getSpecialtyType = (specialty) => {
  return specialtyMap[specialty]?.type || ''
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-'
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchFaultTypes()
  fetchSpecialties()
})
</script>

<style scoped>
.admin-fault-types {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-form .el-form-item {
  margin-bottom: 0;
}

/* 移动端故障类型卡片 */
.mobile-fault-type-list {
  display: grid;
  gap: 16px;
}

.fault-type-info {
  padding: 0;
}

.fault-type-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.fault-type-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.fault-type-details {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item .label {
  color: #606266;
  font-weight: 500;
  min-width: 80px;
}

.detail-item .value {
  color: #303133;
  text-align: right;
  flex: 1;
}

.fault-type-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 桌面端表格 */
.table-responsive {
  overflow-x: auto;
}

/* 分页 */
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 表单提示 */
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-fault-types {
    padding: 16px;
  }

  .filter-form {
    flex-direction: column;
  }

  .filter-form .el-form-item {
    width: 100%;
  }

  .fault-type-actions {
    justify-content: center;
  }

  .pagination-container {
    overflow-x: auto;
  }
}

@media (max-width: 480px) {
  .admin-fault-types {
    padding: 12px;
  }

  .page-header h1 {
    font-size: 20px;
  }

  .fault-type-actions .el-button {
    flex: 1;
    min-width: 0;
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .detail-item .value {
    text-align: left;
  }
}
</style>
