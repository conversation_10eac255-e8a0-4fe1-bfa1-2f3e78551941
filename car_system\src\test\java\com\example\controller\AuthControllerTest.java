package com.example.controller;

import com.example.config.TestSecurityConfig;
import com.example.dto.request.LoginRequest;
import com.example.dto.response.LoginResponse;
import com.example.service.AuthService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(AuthController.class)
@Import(TestSecurityConfig.class)
class AuthControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private AuthService authService;

    @Autowired
    private ObjectMapper objectMapper;

    private LoginRequest loginRequest;
    private LoginResponse loginResponse;

    @BeforeEach
    void setUp() {
        // 创建登录请求
        loginRequest = new LoginRequest();
        loginRequest.setUsername("testuser");
        loginRequest.setPassword("password123");
        loginRequest.setUserType("user");

        // 创建登录响应
        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo(
                1L, "testuser", "Test User", "user"
        );
        loginResponse = new LoginResponse(
                "test-jwt-token",
                userInfo,
                3600000L // 1小时的毫秒数
        );
    }

    @Test
    void testLogin_Success() throws Exception {
        // Given
        when(authService.login(any(LoginRequest.class))).thenReturn(loginResponse);

        // When & Then
        mockMvc.perform(post("/auth/login")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("登录成功"))
                .andExpect(jsonPath("$.data.token").value("test-jwt-token"))
                .andExpect(jsonPath("$.data.user.userId").value(1))
                .andExpect(jsonPath("$.data.user.username").value("testuser"))
                .andExpect(jsonPath("$.data.user.realName").value("Test User"))
                .andExpect(jsonPath("$.data.user.userType").value("user"));

        verify(authService).login(any(LoginRequest.class));
    }

    @Test
    void testLogin_InvalidCredentials() throws Exception {
        // Given
        when(authService.login(any(LoginRequest.class)))
                .thenThrow(new RuntimeException("用户名或密码错误"));

        // When & Then
        mockMvc.perform(post("/auth/login")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isInternalServerError());

        verify(authService).login(any(LoginRequest.class));
    }

    @Test
    void testLogin_MissingUsername() throws Exception {
        // Given
        loginRequest.setUsername(null);

        // When & Then
        mockMvc.perform(post("/auth/login")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isBadRequest());

        verify(authService, never()).login(any(LoginRequest.class));
    }

    @Test
    void testLogin_MissingPassword() throws Exception {
        // Given
        loginRequest.setPassword(null);

        // When & Then
        mockMvc.perform(post("/auth/login")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isBadRequest());

        verify(authService, never()).login(any(LoginRequest.class));
    }

    @Test
    void testLogin_MissingUserType() throws Exception {
        // Given
        loginRequest.setUserType(null);

        // When & Then
        mockMvc.perform(post("/auth/login")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isBadRequest());

        verify(authService, never()).login(any(LoginRequest.class));
    }

    @Test
    void testLogin_EmptyRequestBody() throws Exception {
        // When & Then
        mockMvc.perform(post("/auth/login")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{}"))
                .andExpect(status().isBadRequest());

        verify(authService, never()).login(any(LoginRequest.class));
    }

    @Test
    void testLogin_InvalidJson() throws Exception {
        // When & Then
        mockMvc.perform(post("/auth/login")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("invalid json"))
                .andExpect(status().isBadRequest());

        verify(authService, never()).login(any(LoginRequest.class));
    }

    @Test
    void testLogout_WithToken() throws Exception {
        // Given
        doNothing().when(authService).logout(anyString());

        // When & Then
        mockMvc.perform(post("/auth/logout")
                        .header("Authorization", "Bearer test-token"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("登出成功"))
                .andExpect(jsonPath("$.data").isEmpty());

        verify(authService).logout("test-token");
    }

    @Test
    void testLogout_WithoutToken() throws Exception {
        // When & Then
        mockMvc.perform(post("/auth/logout"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("登出成功"))
                .andExpect(jsonPath("$.data").isEmpty());

        verify(authService, never()).logout(anyString());
    }

    @Test
    void testLogout_InvalidTokenFormat() throws Exception {
        // When & Then
        mockMvc.perform(post("/auth/logout")
                        .header("Authorization", "InvalidFormat"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("登出成功"))
                .andExpect(jsonPath("$.data").isEmpty());

        verify(authService, never()).logout(anyString());
    }

    @Test
    void testLogin_AdminUserType() throws Exception {
        // Given
        loginRequest.setUserType("admin");
        LoginResponse.UserInfo adminUserInfo = new LoginResponse.UserInfo(
                1L, "admin", "Admin User", "admin"
        );
        LoginResponse adminResponse = new LoginResponse(
                "admin-jwt-token",
                adminUserInfo,
                3600000L // 1小时的毫秒数
        );
        when(authService.login(any(LoginRequest.class))).thenReturn(adminResponse);

        // When & Then
        mockMvc.perform(post("/auth/login")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.user.userType").value("admin"));

        verify(authService).login(any(LoginRequest.class));
    }

    @Test
    void testLogin_TechnicianUserType() throws Exception {
        // Given
        loginRequest.setUserType("technician");
        LoginResponse.UserInfo technicianUserInfo = new LoginResponse.UserInfo(
                1L, "technician", "Technician User", "technician"
        );
        LoginResponse technicianResponse = new LoginResponse(
                "technician-jwt-token",
                technicianUserInfo,
                3600000L // 1小时的毫秒数
        );
        when(authService.login(any(LoginRequest.class))).thenReturn(technicianResponse);

        // When & Then
        mockMvc.perform(post("/auth/login")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.user.userType").value("technician"));

        verify(authService).login(any(LoginRequest.class));
    }
}
