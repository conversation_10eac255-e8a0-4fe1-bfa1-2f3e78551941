package com.example.service;

import com.example.dto.request.VehicleRequest;
import com.example.dto.response.VehicleDTO;
import com.example.entity.User;
import com.example.entity.Vehicle;
import com.example.entity.RepairOrder;
import com.example.exception.BusinessException;
import com.example.exception.ResourceNotFoundException;
import com.example.repository.UserRepository;
import com.example.repository.VehicleRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class VehicleServiceTest {

    @Mock
    private VehicleRepository vehicleRepository;

    @Mock
    private UserRepository userRepository;

    @InjectMocks
    private VehicleService vehicleService;

    private User testUser;
    private Vehicle testVehicle;
    private VehicleRequest vehicleRequest;
    private RepairOrder activeOrder;
    private RepairOrder completedOrder;

    @BeforeEach
    void setUp() {
        // 创建测试用户
        testUser = new User();
        testUser.setUserId(1L);
        testUser.setUsername("testuser");
        testUser.setRealName("Test User");

        // 创建测试车辆
        testVehicle = new Vehicle();
        testVehicle.setVehicleId(1L);
        testVehicle.setLicensePlate("京A12345");
        testVehicle.setVin("1HGBH41JXMN109186");
        testVehicle.setBrand("Toyota");
        testVehicle.setModel("Camry");
        testVehicle.setYear(2020);
        testVehicle.setColor("白色");
        testVehicle.setEngineNumber("ENG123456");
        testVehicle.setUser(testUser);
        testVehicle.setCreateTime(LocalDateTime.now());
        testVehicle.setUpdateTime(LocalDateTime.now());

        // 创建车辆请求
        vehicleRequest = new VehicleRequest();
        vehicleRequest.setLicensePlate("京B67890");
        vehicleRequest.setVin("2HGBH41JXMN109187");
        vehicleRequest.setBrand("Honda");
        vehicleRequest.setModel("Accord");
        vehicleRequest.setYear(2021);
        vehicleRequest.setColor("黑色");
        vehicleRequest.setEngineNumber("ENG789012");

        // 创建活跃工单
        activeOrder = new RepairOrder();
        activeOrder.setOrderId(1L);
        activeOrder.setStatus(RepairOrder.OrderStatus.IN_PROGRESS);

        // 创建已完成工单
        completedOrder = new RepairOrder();
        completedOrder.setOrderId(2L);
        completedOrder.setStatus(RepairOrder.OrderStatus.COMPLETED);

        testVehicle.setRepairOrders(Arrays.asList(completedOrder));
    }

    @Test
    void testAddVehicle_Success() {
        // Given
        when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
        when(vehicleRepository.existsByLicensePlate(anyString())).thenReturn(false);
        when(vehicleRepository.existsByVin(anyString())).thenReturn(false);
        when(vehicleRepository.save(any(Vehicle.class))).thenReturn(testVehicle);

        // When
        VehicleDTO result = vehicleService.addVehicle(1L, vehicleRequest);

        // Then
        assertNotNull(result);
        assertEquals(testVehicle.getVehicleId(), result.getVehicleId());
        assertEquals(testVehicle.getLicensePlate(), result.getLicensePlate());
        assertEquals(testVehicle.getBrand(), result.getBrand());
        assertEquals(testVehicle.getModel(), result.getModel());

        verify(userRepository).findById(1L);
        verify(vehicleRepository).existsByLicensePlate("京B67890");
        verify(vehicleRepository).existsByVin("2HGBH41JXMN109187");
        verify(vehicleRepository).save(any(Vehicle.class));
    }

    @Test
    void testAddVehicle_UserNotFound() {
        // Given
        when(userRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class,
                () -> vehicleService.addVehicle(1L, vehicleRequest));
        assertEquals("用户不存在", exception.getMessage());

        verify(userRepository).findById(1L);
        verify(vehicleRepository, never()).save(any(Vehicle.class));
    }

    @Test
    void testAddVehicle_LicensePlateExists() {
        // Given
        when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
        when(vehicleRepository.existsByLicensePlate(anyString())).thenReturn(true);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> vehicleService.addVehicle(1L, vehicleRequest));
        assertEquals("车牌号已存在", exception.getMessage());

        verify(vehicleRepository).existsByLicensePlate("京B67890");
        verify(vehicleRepository, never()).save(any(Vehicle.class));
    }

    @Test
    void testAddVehicle_VinExists() {
        // Given
        when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
        when(vehicleRepository.existsByLicensePlate(anyString())).thenReturn(false);
        when(vehicleRepository.existsByVin(anyString())).thenReturn(true);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> vehicleService.addVehicle(1L, vehicleRequest));
        assertEquals("VIN码已存在", exception.getMessage());

        verify(vehicleRepository).existsByVin("2HGBH41JXMN109187");
        verify(vehicleRepository, never()).save(any(Vehicle.class));
    }

    @Test
    void testGetVehicle_Success() {
        // Given
        when(vehicleRepository.findById(1L)).thenReturn(Optional.of(testVehicle));

        // When
        VehicleDTO result = vehicleService.getVehicle(1L, 1L);

        // Then
        assertNotNull(result);
        assertEquals(testVehicle.getVehicleId(), result.getVehicleId());
        assertEquals(testVehicle.getLicensePlate(), result.getLicensePlate());

        verify(vehicleRepository).findById(1L);
    }

    @Test
    void testGetVehicle_VehicleNotFound() {
        // Given
        when(vehicleRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class,
                () -> vehicleService.getVehicle(1L, 1L));
        assertEquals("车辆不存在", exception.getMessage());

        verify(vehicleRepository).findById(1L);
    }

    @Test
    void testGetVehicle_UnauthorizedAccess() {
        // Given
        when(vehicleRepository.findById(1L)).thenReturn(Optional.of(testVehicle));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> vehicleService.getVehicle(1L, 2L)); // 不同的用户ID
        assertEquals("无权查看该车辆信息", exception.getMessage());

        verify(vehicleRepository).findById(1L);
    }

    @Test
    void testUpdateVehicle_Success() {
        // Given
        when(vehicleRepository.findById(1L)).thenReturn(Optional.of(testVehicle));
        when(vehicleRepository.existsByLicensePlate(anyString())).thenReturn(false);
        when(vehicleRepository.existsByVin(anyString())).thenReturn(false);
        when(vehicleRepository.save(any(Vehicle.class))).thenReturn(testVehicle);

        // When
        VehicleDTO result = vehicleService.updateVehicle(1L, 1L, vehicleRequest);

        // Then
        assertNotNull(result);
        verify(vehicleRepository).findById(1L);
        verify(vehicleRepository).save(any(Vehicle.class));
    }

    @Test
    void testUpdateVehicle_UnauthorizedUpdate() {
        // Given
        when(vehicleRepository.findById(1L)).thenReturn(Optional.of(testVehicle));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> vehicleService.updateVehicle(1L, 2L, vehicleRequest)); // 不同的用户ID
        assertEquals("无权修改该车辆信息", exception.getMessage());

        verify(vehicleRepository).findById(1L);
        verify(vehicleRepository, never()).save(any(Vehicle.class));
    }

    @Test
    void testUpdateVehicle_LicensePlateAlreadyUsed() {
        // Given
        when(vehicleRepository.findById(1L)).thenReturn(Optional.of(testVehicle));
        when(vehicleRepository.existsByLicensePlate(anyString())).thenReturn(true);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> vehicleService.updateVehicle(1L, 1L, vehicleRequest));
        assertEquals("车牌号已被其他车辆使用", exception.getMessage());

        verify(vehicleRepository).existsByLicensePlate("京B67890");
        verify(vehicleRepository, never()).save(any(Vehicle.class));
    }

    @Test
    void testDeleteVehicle_Success() {
        // Given
        when(vehicleRepository.findById(1L)).thenReturn(Optional.of(testVehicle));

        // When
        vehicleService.deleteVehicle(1L, 1L);

        // Then
        verify(vehicleRepository).findById(1L);
        verify(vehicleRepository).delete(testVehicle);
    }

    @Test
    void testDeleteVehicle_UnauthorizedDelete() {
        // Given
        when(vehicleRepository.findById(1L)).thenReturn(Optional.of(testVehicle));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> vehicleService.deleteVehicle(1L, 2L)); // 不同的用户ID
        assertEquals("无权删除该车辆", exception.getMessage());

        verify(vehicleRepository).findById(1L);
        verify(vehicleRepository, never()).delete(any(Vehicle.class));
    }

    @Test
    void testDeleteVehicle_HasActiveOrders() {
        // Given
        testVehicle.setRepairOrders(Arrays.asList(activeOrder, completedOrder));
        when(vehicleRepository.findById(1L)).thenReturn(Optional.of(testVehicle));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> vehicleService.deleteVehicle(1L, 1L));
        assertEquals("该车辆有未完成的维修工单，无法删除", exception.getMessage());

        verify(vehicleRepository).findById(1L);
        verify(vehicleRepository, never()).delete(any(Vehicle.class));
    }

    @Test
    void testUpdateVehicle_SameLicensePlate() {
        // Given
        vehicleRequest.setLicensePlate(testVehicle.getLicensePlate()); // 使用相同的车牌号
        when(vehicleRepository.findById(1L)).thenReturn(Optional.of(testVehicle));
        when(vehicleRepository.existsByVin(anyString())).thenReturn(false);
        when(vehicleRepository.save(any(Vehicle.class))).thenReturn(testVehicle);

        // When
        VehicleDTO result = vehicleService.updateVehicle(1L, 1L, vehicleRequest);

        // Then
        assertNotNull(result);
        verify(vehicleRepository).findById(1L);
        verify(vehicleRepository, never()).existsByLicensePlate(anyString()); // 不应该检查相同的车牌号
        verify(vehicleRepository).save(any(Vehicle.class));
    }

    @Test
    void testUpdateVehicle_SameVin() {
        // Given
        vehicleRequest.setVin(testVehicle.getVin()); // 使用相同的VIN码
        when(vehicleRepository.findById(1L)).thenReturn(Optional.of(testVehicle));
        when(vehicleRepository.existsByLicensePlate(anyString())).thenReturn(false);
        when(vehicleRepository.save(any(Vehicle.class))).thenReturn(testVehicle);

        // When
        VehicleDTO result = vehicleService.updateVehicle(1L, 1L, vehicleRequest);

        // Then
        assertNotNull(result);
        verify(vehicleRepository).findById(1L);
        verify(vehicleRepository, never()).existsByVin(anyString()); // 不应该检查相同的VIN码
        verify(vehicleRepository).save(any(Vehicle.class));
    }
}
