package com.example.repository;

import com.example.entity.Material;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MaterialRepository extends JpaRepository<Material, Long>, JpaSpecificationExecutor<Material> {

    /**
     * 根据状态查找材料
     */
    List<Material> findByStatus(Integer status);

    /**
     * 根据状态查找材料（分页）
     */
    Page<Material> findByStatus(Integer status, Pageable pageable);

    /**
     * 根据分类查找材料
     */
    Page<Material> findByCategory(String category, Pageable pageable);

    /**
     * 根据分类和状态查找材料
     */
    Page<Material> findByCategoryAndStatus(String category, Integer status, Pageable pageable);

    /**
     * 根据材料名称查找
     */
    List<Material> findByMaterialName(String materialName);

    /**
     * 检查材料名称是否存在
     */
    boolean existsByMaterialName(String materialName);

    /**
     * 搜索材料（根据名称或规格）
     */
    @Query("SELECT m FROM Material m WHERE " +
           "(LOWER(m.materialName) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(m.specification) LIKE LOWER(CONCAT('%', :search, '%'))) AND " +
           "m.status = :status")
    Page<Material> searchByNameOrSpecification(@Param("search") String search,
                                              @Param("status") Integer status,
                                              Pageable pageable);

    /**
     * 搜索材料（根据名称或规格，包含分类过滤）
     */
    @Query("SELECT m FROM Material m WHERE " +
           "(LOWER(m.materialName) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(m.specification) LIKE LOWER(CONCAT('%', :search, '%'))) AND " +
           "m.category = :category AND m.status = :status")
    Page<Material> searchByNameOrSpecificationAndCategory(@Param("search") String search,
                                                          @Param("category") String category,
                                                          @Param("status") Integer status,
                                                          Pageable pageable);

    /**
     * 查找库存不足的材料
     */
    @Query("SELECT m FROM Material m WHERE m.inventory <= :threshold AND m.status = 1")
    List<Material> findLowInventoryMaterials(@Param("threshold") Integer threshold);

    /**
     * 获取所有分类
     */
    @Query("SELECT DISTINCT m.category FROM Material m WHERE m.category IS NOT NULL AND m.status = 1")
    List<String> findAllCategories();

    /**
     * 根据材料名称和规格检查是否存在
     */
    boolean existsByMaterialNameAndSpecification(String materialName, String specification);

    /**
     * 根据材料名称模糊查询（包含状态过滤）
     */
    Page<Material> findByMaterialNameContainingAndStatus(String materialName, Integer status, Pageable pageable);

    /**
     * 根据材料名称和分类模糊查询（包含状态过滤）
     */
    Page<Material> findByMaterialNameContainingAndCategoryAndStatus(String materialName, String category, Integer status, Pageable pageable);

    /**
     * 获取所有不同的分类（用于MaterialService）
     */
    @Query("SELECT DISTINCT m.category FROM Material m WHERE m.category IS NOT NULL AND m.status = 1")
    List<String> findDistinctCategories();
}
