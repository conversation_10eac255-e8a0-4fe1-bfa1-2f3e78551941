<template>
  <div class="admin-users">
    <div class="page-header">
      <h1>用户管理</h1>
      <p>管理系统中的所有用户账户</p>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline class="filter-form">
        <el-form-item label="搜索用户">
          <el-input
            v-model="filters.search"
            placeholder="用户名、姓名、手机号"
            style="width: 200px"
            clearable
            @keyup.enter="fetchUsers"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="用户状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable style="width: 120px">
            <el-option label="全部" :value="null" />
            <el-option label="有效" :value="1" />
            <el-option label="无效" :value="0" />
          </el-select>
        </el-form-item>

        <el-form-item label="注册时间">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="fetchUsers">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetFilters">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>

        </el-form-item>
      </el-form>
    </el-card>

    <!-- 用户列表 -->
    <el-card>
      <!-- 移动端卡片视图 -->
      <div v-if="isMobile" class="mobile-user-list">
        <div v-for="user in users" :key="user.userId" class="mobile-user-card">
          <el-card shadow="hover">
            <div class="user-info">
              <div class="user-header">
                <div class="user-avatar">
                  <el-avatar :size="50">{{ user.realName?.charAt(0) || 'U' }}</el-avatar>
                </div>
                <div class="user-basic">
                  <h3 class="user-name">{{ user.realName }}</h3>
                  <div class="user-username">@{{ user.username }}</div>
                </div>
              </div>
              <div class="user-details">
                <div class="detail-item">
                  <span class="label">手机号:</span>
                  <span class="value">{{ user.phone }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">邮箱:</span>
                  <span class="value">{{ user.email }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">地址:</span>
                  <span class="value">{{ user.address || '未填写' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">注册时间:</span>
                  <span class="value">{{ formatDate(user.createTime) }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">状态:</span>
                  <el-tag :type="user.status === 1 ? 'success' : 'danger'" size="small">
                    {{ user.status === 1 ? '有效' : '无效' }}
                  </el-tag>
                </div>
              </div>
              <div class="user-actions">


                <el-button
                  v-if="user.status === 1"
                  type="warning"
                  size="small"
                  @click="disableUser(user)"
                >
                  <el-icon><Close /></el-icon>
                  禁用
                </el-button>
                <el-button
                  v-else
                  type="success"
                  size="small"
                  @click="enableUser(user)"
                >
                  <el-icon><Check /></el-icon>
                  启用
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="deleteUser(user)"
                >
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 桌面端表格视图 -->
      <div v-else class="table-responsive">
        <el-table
          v-loading="loading"
          :data="users"
          style="width: 100%"
          empty-text="暂无用户数据"
          stripe
          highlight-current-row
        >
          <el-table-column label="用户信息" min-width="200">
            <template #default="{ row }">
              <div class="user-cell">
                <el-avatar :size="40">{{ row.realName?.charAt(0) || 'U' }}</el-avatar>
                <div class="user-info">
                  <div class="user-name">{{ row.realName }}</div>
                  <div class="user-username">@{{ row.username }}</div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="phone" label="手机号" width="130" />
          <el-table-column prop="email" label="邮箱" min-width="180" show-overflow-tooltip />
          <el-table-column prop="address" label="地址" min-width="200" show-overflow-tooltip />

          <el-table-column label="车辆数量" width="100" align="center">
            <template #default="{ row }">
              <el-tag type="info">{{ row.vehicleCount || 0 }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column label="工单数量" width="100" align="center">
            <template #default="{ row }">
              <el-tag type="primary">{{ row.orderCount || 0 }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.status === 1 ? 'success' : 'danger'" size="small">
                {{ row.status === 1 ? '有效' : '无效' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="注册时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.createTime) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button
                v-if="row.status === 1"
                type="text"
                size="small"
                style="color: #e6a23c"
                @click="disableUser(row)"
              >
                <el-icon><Close /></el-icon>
                禁用
              </el-button>
              <el-button
                v-else
                type="text"
                size="small"
                style="color: #67c23a"
                @click="enableUser(row)"
              >
                <el-icon><Check /></el-icon>
                启用
              </el-button>
              <el-button
                type="text"
                size="small"
                style="color: #f56c6c"
                @click="deleteUser(row)"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchUsers"
          @current-change="fetchUsers"
        />
      </div>
    </el-card>


  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Refresh, Delete, Check, Close
} from '@element-plus/icons-vue'
import { adminAPI } from '@/api/admin'
import { useAppStore } from '@/stores/app'
import dayjs from 'dayjs'

const appStore = useAppStore()

// 计算属性
const isMobile = computed(() => appStore.isMobile)

// 响应式数据
const loading = ref(false)
const users = ref([])

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 筛选条件
const filters = reactive({
  search: '',
  status: null,
  dateRange: null
})



// 获取用户列表
const fetchUsers = async () => {
  try {
    loading.value = true

    const params = {
      page: pagination.page,
      size: pagination.size
    }

    // 添加搜索条件
    if (filters.search) params.search = filters.search
    if (filters.status !== null) params.status = filters.status
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.startDate = filters.dateRange[0]
      params.endDate = filters.dateRange[1]
    }

    const response = await adminAPI.getAllUsers(params)
    console.log('Admin Users API Response:', response)
    console.log('Response data structure:', response.data)
    console.log('Users content:', response.data.data?.content)
    console.log('Page info:', response.data.data?.page)

    users.value = response.data.data?.content || []
    pagination.total = response.data.data?.page?.totalElements || 0

    console.log('Final users array:', users.value)
    console.log('Final pagination total:', pagination.total)
  } catch (error) {
    console.error('Failed to fetch users:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 重置筛选条件
const resetFilters = () => {
  Object.assign(filters, {
    search: '',
    status: null,
    dateRange: null
  })
  pagination.page = 1
  fetchUsers()
}



// 禁用用户
const disableUser = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要禁用用户 "${user.realName}" 吗？禁用后用户将无法登录系统。`,
      '禁用确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await adminAPI.disableUser(user.userId)
    ElMessage.success('用户禁用成功')
    fetchUsers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to disable user:', error)
      ElMessage.error('禁用用户失败')
    }
  }
}

// 删除用户
const deleteUser = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.realName}" 吗？删除后将移除用户的所有关联信息，此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    await adminAPI.deleteUser(user.userId)
    ElMessage.success('用户删除成功')
    fetchUsers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete user:', error)
      ElMessage.error(error.response?.data?.message || '删除用户失败')
    }
  }
}

// 启用用户
const enableUser = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要启用用户 "${user.realName}" 吗？启用后用户可以正常登录系统。`,
      '启用确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    // 这里需要调用启用用户的API
    await adminAPI.enableUser(user.userId)
    ElMessage.success('用户启用成功')
    fetchUsers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to enable user:', error)
      ElMessage.error('启用用户失败')
    }
  }
}

// 格式化日期
const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchUsers()
})
</script>

<style scoped>
.admin-users {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

/* 过滤器卡片 */
.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: end;
}

/* 移动端用户卡片 */
.mobile-user-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.mobile-user-card {
  width: 100%;
}

.user-info {
  padding: 0;
}

.user-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.user-avatar {
  margin-right: 15px;
}

.user-basic {
  flex: 1;
}

.user-name {
  margin: 0 0 5px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.user-username {
  font-size: 12px;
  color: #909399;
}

.user-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-bottom: 15px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.detail-item .label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.detail-item .value {
  font-size: 14px;
  color: #303133;
  word-break: break-all;
}

.user-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  padding-top: 10px;
  border-top: 1px solid #ebeef5;
}

/* 桌面端表格 */
.user-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-cell .user-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-cell .user-name {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.user-cell .user-username {
  font-size: 12px;
  color: #909399;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-users {
    padding: 15px;
  }

  .page-header h1 {
    font-size: 20px;
  }

  .filter-form {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .filter-form .el-form-item {
    margin-bottom: 0;
  }

  .user-details {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .user-actions {
    flex-direction: column;
    gap: 8px;
  }

  .user-actions .el-button {
    width: 100%;
  }

  .pagination-container {
    margin-top: 15px;
  }

  :deep(.el-pagination) {
    justify-content: center;
    flex-wrap: wrap;
  }

  :deep(.el-pagination .el-pagination__sizes) {
    order: 3;
    margin-top: 10px;
  }

  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto !important;
  }
}

@media (max-width: 480px) {
  .admin-users {
    padding: 10px;
  }

  .page-header h1 {
    font-size: 18px;
  }

  .user-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .user-avatar {
    margin-right: 0;
  }

  :deep(.el-pagination .el-pagination__sizes),
  :deep(.el-pagination .el-pagination__jump) {
    display: none;
  }

  :deep(.el-dialog) {
    width: 98% !important;
    margin: 2vh auto !important;
  }

  :deep(.el-form-item__label) {
    font-size: 14px;
    line-height: 1.4;
  }

  :deep(.el-input__inner) {
    font-size: 14px;
  }
}
</style>