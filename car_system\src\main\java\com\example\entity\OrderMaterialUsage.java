package com.example.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "order_material_usage")
public class OrderMaterialUsage extends BaseEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "usage_id")
    private Long usageId;
    
    @NotNull(message = "使用数量不能为空")
    @DecimalMin(value = "0.0", inclusive = false, message = "使用数量必须大于0")
    @Column(name = "quantity", nullable = false, precision = 10, scale = 2)
    private BigDecimal quantity;
    
    @NotNull(message = "总价不能为空")
    @DecimalMin(value = "0.0", inclusive = false, message = "总价必须大于0")
    @Column(name = "total_price", nullable = false, precision = 10, scale = 2)
    private BigDecimal totalPrice;
    
    @Column(name = "use_time")
    private LocalDateTime useTime;
    
    // 多对一关系：使用记录属于一个工单
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id", nullable = false)
    private RepairOrder repairOrder;
    
    // 多对一关系：使用记录对应一个材料
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "material_id", nullable = false)
    private Material material;
    
    // 构造函数
    public OrderMaterialUsage() {}
    
    public OrderMaterialUsage(BigDecimal quantity, BigDecimal totalPrice, RepairOrder repairOrder, Material material) {
        this.quantity = quantity;
        this.totalPrice = totalPrice;
        this.repairOrder = repairOrder;
        this.material = material;
        this.useTime = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getUsageId() {
        return usageId;
    }
    
    public void setUsageId(Long usageId) {
        this.usageId = usageId;
    }
    
    public BigDecimal getQuantity() {
        return quantity;
    }
    
    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }
    
    public BigDecimal getTotalPrice() {
        return totalPrice;
    }
    
    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }
    
    public LocalDateTime getUseTime() {
        return useTime;
    }
    
    public void setUseTime(LocalDateTime useTime) {
        this.useTime = useTime;
    }
    
    public RepairOrder getRepairOrder() {
        return repairOrder;
    }
    
    public void setRepairOrder(RepairOrder repairOrder) {
        this.repairOrder = repairOrder;
    }
    
    public Material getMaterial() {
        return material;
    }
    
    public void setMaterial(Material material) {
        this.material = material;
    }
}
