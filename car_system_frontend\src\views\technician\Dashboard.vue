<template>
  <div class="technician-dashboard">
    <div class="dashboard-header">
      <h1>欢迎回来，{{ technician?.realName }}</h1>
      <p>今天是 {{ currentDate }}，祝您工作愉快！</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon pending">
            <el-icon size="24"><Clock /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.pendingTasks }}</div>
            <div class="stat-label">待处理任务</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon progress">
            <el-icon size="24"><Tools /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.inProgressTasks }}</div>
            <div class="stat-label">进行中任务</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon completed">
            <el-icon size="24"><Check /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.completedToday }}</div>
            <div class="stat-label">今日完成</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon income">
            <el-icon size="24"><Money /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">¥{{ stats.todayIncome }}</div>
            <div class="stat-label">今日收入</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <h2>快速操作</h2>
      <div class="action-grid">
        <el-card class="action-card" @click="$router.push('/technician/tasks')">
          <div class="action-content">
            <el-icon size="32" color="#409eff"><List /></el-icon>
            <div class="action-title">查看任务</div>
            <div class="action-desc">查看分配给我的维修任务</div>
          </div>
        </el-card>

        <el-card class="action-card" @click="$router.push('/technician/payments')">
          <div class="action-content">
            <el-icon size="32" color="#67c23a"><Money /></el-icon>
            <div class="action-title">收入统计</div>
            <div class="action-desc">查看工时费和收入统计</div>
          </div>
        </el-card>

        <el-card class="action-card" @click="$router.push('/technician/profile')">
          <div class="action-content">
            <el-icon size="32" color="#e6a23c"><User /></el-icon>
            <div class="action-title">个人资料</div>
            <div class="action-desc">管理个人信息和技能</div>
          </div>
        </el-card>

        <el-card class="action-card" @click="$router.push('/technician/history')">
          <div class="action-content">
            <el-icon size="32" color="#909399"><Clock /></el-icon>
            <div class="action-title">工作历史</div>
            <div class="action-desc">查看历史工作记录</div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 今日任务 -->
    <div class="today-tasks">
      <h2>今日任务</h2>
      <el-card>
        <el-table
          v-loading="tasksLoading"
          :data="todayTasks"
          style="width: 100%"
          empty-text="今日暂无任务"
        >
          <el-table-column prop="orderId" label="工单号" width="100" />

          <el-table-column label="车辆信息" width="180">
            <template #default="{ row }">
              <div class="vehicle-info">
                <div class="license-plate">{{ row.vehicle?.licensePlate }}</div>
                <div class="vehicle-detail">
                  {{ row.vehicle?.brand }} {{ row.vehicle?.model }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="faultType.typeName" label="故障类型" width="120" />

          <el-table-column label="紧急程度" width="100">
            <template #default="{ row }">
              <el-tag :type="getUrgencyType(row.urgencyLevel)" size="small">
                {{ getUrgencyText(row.urgencyLevel) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="分配时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.assignTime) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button
                v-if="canAcceptTask(row)"
                type="success"
                size="small"
                @click="acceptTask(row)"
              >
                接受任务
              </el-button>
              <el-button
                v-if="canRejectTask(row)"
                type="danger"
                size="small"
                @click="rejectTask(row)"
              >
                拒绝任务
              </el-button>
              <el-tag
                v-if="hasRespondedToTask(row)"
                :type="getResponseType(row)"
                size="small"
                style="margin-right: 8px;"
              >
                {{ getResponseText(row) }}
              </el-tag>
              <el-button
                v-if="row.status === 'accepted'"
                type="primary"
                size="small"
                @click="startTask(row)"
              >
                开始维修
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="viewTaskDetail(row)"
              >
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 工作进度 -->
    <div class="work-progress">
      <h2>本月工作进度</h2>
      <el-card>
        <div class="progress-content">
          <div class="progress-item">
            <div class="progress-label">完成任务数</div>
            <el-progress
              :percentage="(stats.monthlyCompleted / stats.monthlyTarget * 100)"
              :stroke-width="20"
              :text-inside="true"
              status="success"
            />
            <div class="progress-text">
              {{ stats.monthlyCompleted }} / {{ stats.monthlyTarget }}
            </div>
          </div>

          <div class="progress-item">
            <div class="progress-label">工作时长</div>
            <el-progress
              :percentage="(stats.monthlyHours / stats.monthlyHoursTarget * 100)"
              :stroke-width="20"
              :text-inside="true"
              color="#409eff"
            />
            <div class="progress-text">
              {{ stats.monthlyHours }}h / {{ stats.monthlyHoursTarget }}h
            </div>
          </div>

          <div class="progress-item">
            <div class="progress-label">收入目标</div>
            <el-progress
              :percentage="(stats.monthlyIncome / stats.monthlyIncomeTarget * 100)"
              :stroke-width="20"
              :text-inside="true"
              color="#67c23a"
            />
            <div class="progress-text">
              ¥{{ stats.monthlyIncome }} / ¥{{ stats.monthlyIncomeTarget }}
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Clock, Tools, Check, Money, List, User
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { technicianAPI } from '@/api/technician'
import { orderAPI } from '@/api/order'
import dayjs from 'dayjs'

const router = useRouter()
const authStore = useAuthStore()

// 计算属性
const technician = computed(() => authStore.user)
const currentDate = computed(() => dayjs().format('YYYY年MM月DD日'))

// 响应式数据
const tasksLoading = ref(false)
const todayTasks = ref([])

// 统计数据
const stats = reactive({
  pendingTasks: 0,
  inProgressTasks: 0,
  completedToday: 0,
  todayIncome: 0,
  monthlyCompleted: 0,
  monthlyTarget: 30,
  monthlyHours: 0,
  monthlyHoursTarget: 160,
  monthlyIncome: 0,
  monthlyIncomeTarget: 8000
})

// 获取统计数据
const fetchStats = async () => {
  try {
    // 获取今日任务统计
    const today = dayjs().format('YYYY-MM-DD')
    const tasksResponse = await technicianAPI.getCurrentTechnicianOrders({
      page: 1,
      size: 100,
      date: today
    })
    const tasks = tasksResponse.data.data?.content || []

    stats.pendingTasks = tasks.filter(task => task.status === 'assigned').length
    stats.inProgressTasks = tasks.filter(task => task.status === 'in_progress').length
    stats.completedToday = tasks.filter(task => task.status === 'completed').length

    // 计算今日收入（从已完成的任务中计算）
    const todayCompletedTasks = tasks.filter(task =>
      task.status === 'completed' &&
      task.actualCompletionTime &&
      dayjs(task.actualCompletionTime).format('YYYY-MM-DD') === today
    )
    stats.todayIncome = todayCompletedTasks.reduce((sum, task) => {
      return sum + (task.totalLaborCost || 0)
    }, 0)

    // 获取本月统计
    const monthStart = dayjs().startOf('month').format('YYYY-MM-DD')
    const monthEnd = dayjs().endOf('month').format('YYYY-MM-DD')

    const monthlyTasksResponse = await technicianAPI.getCurrentTechnicianOrders({
      page: 1,
      size: 1000,
      startDate: monthStart,
      endDate: monthEnd,
      status: 'completed'
    })
    stats.monthlyCompleted = monthlyTasksResponse.data?.page?.totalElements || 0

    // 计算月度收入（从工时费记录中获取）
    const monthlyPaymentsResponse = await technicianAPI.getCurrentTechnicianPayments({
      page: 1,
      size: 100
    })
    const payments = monthlyPaymentsResponse.data.data?.content || []
    const currentMonth = dayjs().month() + 1 // dayjs月份从0开始
    const currentYear = dayjs().year()

    stats.monthlyIncome = payments
      .filter(p => p.year === currentYear && p.month === currentMonth)
      .reduce((sum, p) => sum + (p.totalAmount || 0), 0)
    stats.monthlyHours = monthlyPaymentsResponse.data?.totalHours || 0
  } catch (error) {
    console.error('Failed to fetch stats:', error)
  }
}

// 获取今日任务
const fetchTodayTasks = async () => {
  try {
    tasksLoading.value = true
    const today = dayjs().format('YYYY-MM-DD')
    const response = await technicianAPI.getCurrentTechnicianOrders({
      page: 1,
      size: 10,
      date: today
    })
    todayTasks.value = response.data?.content || []
  } catch (error) {
    console.error('Failed to fetch today tasks:', error)
    ElMessage.error('获取今日任务失败')
  } finally {
    tasksLoading.value = false
  }
}

// 接受任务
const acceptTask = async (task) => {
  try {
    await orderAPI.accept(task.orderId)
    ElMessage.success('任务已接受')
    fetchTodayTasks()
    fetchStats()
  } catch (error) {
    console.error('Failed to accept task:', error)
    ElMessage.error('接受任务失败')
  }
}

// 拒绝任务
const rejectTask = async (task) => {
  try {
    const { value: reason } = await ElMessageBox.prompt(
      '请输入拒绝原因',
      '拒绝任务',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '请输入拒绝原因'
      }
    )

    await orderAPI.reject(task.orderId, { reason })
    ElMessage.success('任务已拒绝')
    fetchTodayTasks()
    fetchStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to reject task:', error)

      // 美化错误提示信息
      let errorMessage = '拒绝任务失败'
      if (error.response?.data?.message) {
        const message = error.response.data.message
        if (message.includes('没有可用的') && message.includes('技师进行重新分配')) {
          errorMessage = '拒绝失败：当前没有其他可用技师可以接替此任务，请联系管理员处理'
        } else {
          errorMessage = message
        }
      }

      ElMessage({
        message: errorMessage,
        type: 'error',
        duration: 5000,
        showClose: true
      })
    }
  }
}

// 开始任务
const startTask = async (task) => {
  try {
    await orderAPI.start(task.orderId)
    ElMessage.success('任务已开始')
    fetchTodayTasks()
    fetchStats()
  } catch (error) {
    console.error('Failed to start task:', error)
    ElMessage.error('开始任务失败')
  }
}

// 查看任务详情
const viewTaskDetail = (task) => {
  router.push(`/technician/tasks/${task.orderId}`)
}

// 获取紧急程度类型
const getUrgencyType = (level) => {
  const typeMap = {
    low: 'info',
    normal: 'primary',
    high: 'warning',
    urgent: 'danger'
  }
  return typeMap[level] || 'info'
}

// 获取紧急程度文本
const getUrgencyText = (level) => {
  const textMap = {
    low: '低',
    normal: '中',
    high: '高',
    urgent: '紧急'
  }
  return textMap[level] || level
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    assigned: 'info',
    accepted: 'primary',
    in_progress: 'warning',
    completed: 'success'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    assigned: '已分配',
    accepted: '已接受',
    in_progress: '进行中',
    completed: '已完成'
  }
  return statusMap[status] || status
}

// 判断是否可以接受任务
const canAcceptTask = (task) => {
  if (task.status !== 'assigned') return false

  // 检查当前技师的分配状态
  const currentTechnicianAssignment = getCurrentTechnicianAssignment(task)
  if (!currentTechnicianAssignment) return false

  return currentTechnicianAssignment.agreementStatus === 'pending'
}

// 判断是否可以拒绝任务
const canRejectTask = (task) => {
  if (task.status !== 'assigned') return false

  // 检查当前技师的分配状态
  const currentTechnicianAssignment = getCurrentTechnicianAssignment(task)
  if (!currentTechnicianAssignment) return false

  return currentTechnicianAssignment.agreementStatus === 'pending'
}

// 判断当前技师是否已经响应任务
const hasRespondedToTask = (task) => {
  if (task.status !== 'assigned') return false

  const currentTechnicianAssignment = getCurrentTechnicianAssignment(task)
  if (!currentTechnicianAssignment) return false

  return currentTechnicianAssignment.agreementStatus !== 'pending'
}

// 获取当前技师的分配记录
const getCurrentTechnicianAssignment = (task) => {
  if (!task.technicianAssignments || !Array.isArray(task.technicianAssignments)) {
    return null
  }

  // 这里需要获取当前登录技师的ID，暂时使用一个假设的方法
  const currentTechnicianId = getCurrentTechnicianId()

  return task.technicianAssignments.find(assignment =>
    assignment.technicianId === currentTechnicianId
  )
}

// 获取响应状态的类型
const getResponseType = (task) => {
  const assignment = getCurrentTechnicianAssignment(task)
  if (!assignment) return 'info'

  const typeMap = {
    accepted: 'success',
    rejected: 'danger',
    pending: 'warning'
  }
  return typeMap[assignment.agreementStatus] || 'info'
}

// 获取响应状态的文本
const getResponseText = (task) => {
  const assignment = getCurrentTechnicianAssignment(task)
  if (!assignment) return ''

  const textMap = {
    accepted: '已同意',
    rejected: '已拒绝',
    pending: '待确认'
  }
  return textMap[assignment.agreementStatus] || ''
}

// 获取当前技师ID
const getCurrentTechnicianId = () => {
  return authStore.user?.userId || null
}

// 格式化日期
const formatDate = (dateString) => {
  return dayjs(dateString).format('MM-DD HH:mm')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchStats()
  fetchTodayTasks()
})
</script>

<style scoped>
.technician-dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 30px;
}

.dashboard-header h1 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 28px;
  font-weight: 600;
}

.dashboard-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  cursor: default;
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: white;
}

.stat-icon.pending {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.stat-icon.progress {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.stat-icon.income {
  background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  line-height: 1;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

/* 快速操作 */
.quick-actions {
  margin-bottom: 30px;
}

.quick-actions h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.action-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.action-content {
  text-align: center;
  padding: 20px;
}

.action-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 15px 0 5px 0;
}

.action-desc {
  font-size: 14px;
  color: #666;
}

/* 今日任务 */
.today-tasks {
  margin-bottom: 30px;
}

.today-tasks h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.vehicle-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.license-plate {
  font-weight: 600;
  color: #409eff;
}

.vehicle-detail {
  font-size: 12px;
  color: #999;
}

/* 工作进度 */
.work-progress h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.progress-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.progress-item {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.progress-label {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.progress-text {
  text-align: center;
  color: #666;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .action-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .dashboard-header h1 {
    font-size: 24px;
  }

  :deep(.el-table) {
    font-size: 12px;
  }

  :deep(.el-table .el-table__cell) {
    padding: 8px 4px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .action-grid {
    grid-template-columns: 1fr;
  }

  .action-content {
    padding: 15px;
  }
}
</style>
