<template>
  <div class="repair-history-page">
    <div class="page-header">
      <h1>维修历史</h1>
      <p>查看您的车辆维修记录和统计信息</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon total">
            <el-icon size="24"><Document /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.totalRepairs }}</div>
            <div class="stat-label">总维修次数</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon cost">
            <el-icon size="24"><Money /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">¥{{ stats.totalCost }}</div>
            <div class="stat-label">总维修费用</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon time">
            <el-icon size="24"><Clock /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.totalHours }}</div>
            <div class="stat-label">总维修工时</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon rating">
            <el-icon size="24"><Star /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.avgRating }}</div>
            <div class="stat-label">平均评分</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline class="filter-form">
        <el-form-item label="车辆">
          <el-select
            v-model="filters.vehicleId"
            placeholder="全部车辆"
            clearable
            style="width: 200px"
            @change="fetchRepairHistory"
          >
            <el-option
              v-for="vehicle in vehicles"
              :key="vehicle.vehicleId"
              :label="`${vehicle.licensePlate} - ${vehicle.brand} ${vehicle.model}`"
              :value="vehicle.vehicleId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="故障类型">
          <el-select
            v-model="filters.faultTypeId"
            placeholder="全部类型"
            clearable
            style="width: 150px"
            @change="fetchRepairHistory"
          >
            <el-option
              v-for="faultType in faultTypes"
              :key="faultType.faultTypeId"
              :label="faultType.typeName"
              :value="faultType.faultTypeId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="维修时间">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
            @change="fetchRepairHistory"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="fetchRepairHistory">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetFilters">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 维修历史列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>维修记录</span>
          <el-button type="text" @click="exportHistory">
            <el-icon><Download /></el-icon>
            导出记录
          </el-button>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="repairHistory"
        style="width: 100%"
        empty-text="暂无维修记录"
      >
        <el-table-column prop="orderId" label="工单号" width="100" />

        <el-table-column label="车辆信息" width="180">
          <template #default="{ row }">
            <div class="vehicle-info">
              <div class="license-plate">{{ row.vehicle?.licensePlate }}</div>
              <div class="vehicle-detail">
                {{ row.vehicle?.brand }} {{ row.vehicle?.model }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="faultType.typeName" label="故障类型" width="120" />

        <el-table-column label="维修技师" width="120">
          <template #default="{ row }">
            <div v-if="row.assignedTechnicians && row.assignedTechnicians.length > 0">
              <div
                v-for="tech in row.assignedTechnicians"
                :key="tech.technicianId"
                class="technician-name"
              >
                {{ tech.realName }}
              </div>
            </div>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>

        <el-table-column label="维修时间" width="180">
          <template #default="{ row }">
            <div class="repair-time">
              <div>提交: {{ formatDate(row.submitTime) }}</div>
              <div v-if="row.actualCompletionTime">完成: {{ formatDate(row.actualCompletionTime) }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="工时" width="80">
          <template #default="{ row }">
            {{ row.workingHours || '-' }}h
          </template>
        </el-table-column>

        <el-table-column label="费用" width="100">
          <template #default="{ row }">
            <div class="cost-info">
              <div>工时: ¥{{ (parseFloat(row.totalLaborCost) || 0).toFixed(2) }}</div>
              <div>材料: ¥{{ (parseFloat(row.totalMaterialCost) || 0).toFixed(2) }}</div>
              <div class="total-cost">总计: ¥{{ (parseFloat(row.totalCost) || 0).toFixed(2) }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="评分" width="100">
          <template #default="{ row }">
            <div v-if="row.feedback">
              <el-rate
                v-model="row.feedback.rating"
                disabled
                size="small"
                show-score
                text-color="#ff9900"
              />
            </div>
            <span v-else class="no-data">未评价</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button
              type="text"
              size="small"
              @click="viewDetail(row)"
            >
              查看详情
            </el-button>
            <el-button
              v-if="!row.feedback"
              type="text"
              size="small"
              style="color: #e6a23c"
              @click="showFeedbackDialog(row)"
            >
              评价
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchRepairHistory"
          @current-change="fetchRepairHistory"
        />
      </div>
    </el-card>

    <!-- 反馈对话框 -->
    <el-dialog
      v-model="feedbackDialog.visible"
      title="维修评价"
      width="500px"
      @close="resetFeedbackForm"
    >
      <el-form
        ref="feedbackFormRef"
        :model="feedbackForm"
        :rules="feedbackRules"
        label-width="80px"
      >
        <el-form-item label="评分" prop="rating">
          <el-rate
            v-model="feedbackForm.rating"
            :max="5"
            show-text
            :texts="['极差', '失望', '一般', '满意', '惊喜']"
          />
        </el-form-item>

        <el-form-item label="评价内容" prop="comment">
          <el-input
            v-model="feedbackForm.comment"
            type="textarea"
            :rows="4"
            placeholder="请描述您对本次维修服务的评价"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="feedbackDialog.visible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="feedbackSubmitting"
          @click="submitFeedback"
        >
          提交评价
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Document, Money, Clock, Star, Search, Refresh, Download
} from '@element-plus/icons-vue'
import { userAPI } from '@/api/user'
import { faultTypeAPI } from '@/api/faultType'
import { orderAPI } from '@/api/order'
import dayjs from 'dayjs'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const feedbackSubmitting = ref(false)
const repairHistory = ref([])
const vehicles = ref([])
const faultTypes = ref([])

// 表单引用
const feedbackFormRef = ref()

// 统计数据
const stats = reactive({
  totalRepairs: 0,
  totalCost: 0,
  totalHours: 0,
  avgRating: 0
})

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 筛选条件
const filters = reactive({
  vehicleId: null,
  faultTypeId: null,
  dateRange: null
})

// 反馈对话框
const feedbackDialog = reactive({
  visible: false,
  orderId: null
})

// 反馈表单
const feedbackForm = reactive({
  rating: 5,
  comment: ''
})

// 反馈表单验证规则
const feedbackRules = {
  rating: [
    { required: true, message: '请选择评分', trigger: 'change' }
  ],
  comment: [
    { required: true, message: '请输入评价内容', trigger: 'blur' },
    { min: 10, message: '评价内容至少10个字符', trigger: 'blur' }
  ]
}

// 获取维修历史
const fetchRepairHistory = async () => {
  try {
    loading.value = true

    const params = {
      page: pagination.page,
      size: pagination.size
    }

    // 添加筛选条件
    if (filters.vehicleId) params.vehicleId = filters.vehicleId
    if (filters.faultTypeId) params.faultTypeId = filters.faultTypeId
    if (filters.dateRange && filters.dateRange.length === 2) {
      // 转换为LocalDateTime格式
      params.startDate = filters.dateRange[0] + 'T00:00:00'
      params.endDate = filters.dateRange[1] + 'T23:59:59'
    }

    console.log('Fetching repair history with params:', params)

    const response = await userAPI.getCurrentUserRepairHistory(params)
    console.log('User Repair History API Response:', response)
    console.log('Response data structure:', response.data)
    console.log('Repair history content:', response.data.data?.content)
    console.log('Page info:', response.data.data?.page)

    // 处理分页响应数据结构
    if (response.data && response.data.data) {
      // PageResponse结构: { success: true, data: { content: [], page: {} } }
      repairHistory.value = response.data.data.content || []
      pagination.total = response.data.data.page?.totalElements || 0
    } else if (response.data && Array.isArray(response.data)) {
      // 直接返回数组的情况（向后兼容）
      repairHistory.value = response.data
      pagination.total = response.data.length
    } else {
      repairHistory.value = []
      pagination.total = 0
    }

    console.log('Final repair history array:', repairHistory.value)
    console.log('Final pagination total:', pagination.total)
  } catch (error) {
    console.error('Failed to fetch repair history:', error)
    ElMessage.error('获取维修历史失败')
  } finally {
    loading.value = false
  }
}

// 获取统计数据
const fetchStats = async () => {
  try {
    // 这里应该调用专门的统计API，暂时用维修历史数据计算
    const response = await userAPI.getCurrentUserRepairHistory({ page: 1, size: 1000 })

    let allRepairs = []
    if (response.data && response.data.data) {
      allRepairs = response.data.data.content || []
    } else if (response.data && Array.isArray(response.data)) {
      allRepairs = response.data
    }

    stats.totalRepairs = allRepairs.length
    stats.totalCost = allRepairs.reduce((sum, repair) => {
      return sum + (repair.totalCost || 0)
    }, 0)
    stats.totalHours = allRepairs.reduce((sum, repair) => {
      return sum + (repair.workingHours || 0)
    }, 0)

    const ratedRepairs = allRepairs.filter(repair => repair.feedback?.rating)
    stats.avgRating = ratedRepairs.length > 0
      ? (ratedRepairs.reduce((sum, repair) => sum + repair.feedback.rating, 0) / ratedRepairs.length).toFixed(1)
      : 0
  } catch (error) {
    console.error('Failed to fetch stats:', error)
  }
}

// 获取车辆列表
const fetchVehicles = async () => {
  try {
    const response = await userAPI.getCurrentUserVehicles({ page: 1, size: 100 })

    // 处理分页响应数据结构
    if (response.data && response.data.data) {
      vehicles.value = response.data.data.content || []
    } else if (response.data && Array.isArray(response.data)) {
      vehicles.value = response.data
    } else {
      vehicles.value = []
    }
  } catch (error) {
    console.error('Failed to fetch vehicles:', error)
  }
}

// 获取故障类型列表
const fetchFaultTypes = async () => {
  try {
    const response = await faultTypeAPI.getList()
    faultTypes.value = response.data.data?.content || response.data || []
  } catch (error) {
    console.error('Failed to fetch fault types:', error)
  }
}

// 重置筛选条件
const resetFilters = () => {
  Object.assign(filters, {
    vehicleId: null,
    faultTypeId: null,
    dateRange: null
  })
  fetchRepairHistory()
}

// 查看详情
const viewDetail = (repair) => {
  router.push(`/user/orders/${repair.orderId}`)
}

// 显示反馈对话框
const showFeedbackDialog = (repair) => {
  feedbackDialog.orderId = repair.orderId
  feedbackDialog.visible = true
}

// 提交反馈
const submitFeedback = async () => {
  if (!feedbackFormRef.value) return

  try {
    await feedbackFormRef.value.validate()
    feedbackSubmitting.value = true

    await orderAPI.submitFeedback(feedbackDialog.orderId, feedbackForm)

    ElMessage.success('评价提交成功')
    feedbackDialog.visible = false

    // 重新获取数据
    fetchRepairHistory()
    fetchStats()
  } catch (error) {
    console.error('Failed to submit feedback:', error)
  } finally {
    feedbackSubmitting.value = false
  }
}

// 重置反馈表单
const resetFeedbackForm = () => {
  Object.assign(feedbackForm, {
    rating: 5,
    comment: ''
  })
  if (feedbackFormRef.value) {
    feedbackFormRef.value.resetFields()
  }
}

// 导出历史记录
const exportHistory = () => {
  // 这里可以实现导出功能
  ElMessage.info('导出功能开发中...')
}

// 格式化日期
const formatDate = (dateString) => {
  return dayjs(dateString).format('MM-DD HH:mm')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchRepairHistory()
  fetchStats()
  fetchVehicles()
  fetchFaultTypes()
})
</script>

<style scoped>
.repair-history-page {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  cursor: default;
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.cost {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.time {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.rating {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  line-height: 1;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  margin: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.vehicle-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.license-plate {
  font-weight: 600;
  color: #409eff;
}

.vehicle-detail {
  font-size: 12px;
  color: #999;
}

.technician-name {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.repair-time {
  font-size: 12px;
  line-height: 1.4;
}

.cost-info {
  font-size: 12px;
  line-height: 1.4;
}

.total-cost {
  font-weight: 600;
  color: #333;
}

.no-data {
  color: #999;
  font-size: 12px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .page-header h1 {
    font-size: 20px;
  }

  .filter-form {
    :deep(.el-form-item) {
      margin-right: 10px;
      margin-bottom: 10px;
    }
  }

  :deep(.el-table) {
    font-size: 12px;
  }

  :deep(.el-table .el-table__cell) {
    padding: 8px 4px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .filter-form {
    :deep(.el-form-item) {
      display: block;
      margin-right: 0;
    }

    :deep(.el-form-item__content) {
      margin-left: 0 !important;
    }
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style>
