package com.example.controller;

import com.example.dto.request.TechnicianRegistrationRequest;
import com.example.dto.request.TechnicianUpdateRequest;
import com.example.dto.response.ApiResponse;
import com.example.dto.response.PageResponse;
import com.example.dto.response.SpecialtyDTO;
import com.example.dto.response.TechnicianDTO;
import com.example.dto.response.OrderDTO;
import com.example.dto.response.LaborPaymentDTO;
import com.example.security.JwtAuthenticationFilter;
import com.example.service.TechnicianService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 技师控制器
 */
@RestController
@RequestMapping("/technicians")
public class TechnicianController {

    @Autowired
    private TechnicianService technicianService;

    /**
     * 技师注册
     */
    @PostMapping
    public ApiResponse<TechnicianDTO> registerTechnician(@Valid @RequestBody TechnicianRegistrationRequest request) {
        TechnicianDTO technicianDTO = technicianService.registerTechnician(request);
        return ApiResponse.success("注册成功", technicianDTO);
    }

    /**
     * 获取工种列表
     */
    @GetMapping("/specialties")
    public ApiResponse<List<SpecialtyDTO>> getSpecialties() {
        List<SpecialtyDTO> specialties = technicianService.getSpecialties();
        return ApiResponse.success("获取工种列表成功", specialties);
    }

    /**
     * 获取当前技师信息
     */
    @GetMapping("/me")
    @PreAuthorize("hasRole('TECHNICIAN')")
    public ApiResponse<TechnicianDTO> getCurrentTechnician(HttpServletRequest request) {
        Long technicianId = getCurrentTechnicianId(request);
        TechnicianDTO technicianDTO = technicianService.getCurrentTechnician(technicianId);
        return ApiResponse.success("获取技师信息成功", technicianDTO);
    }

    /**
     * 更新当前技师信息
     */
    @PutMapping("/me")
    @PreAuthorize("hasRole('TECHNICIAN')")
    public ApiResponse<TechnicianDTO> updateCurrentTechnician(@Valid @RequestBody TechnicianUpdateRequest request,
                                                             HttpServletRequest httpRequest) {
        Long technicianId = getCurrentTechnicianId(httpRequest);
        TechnicianDTO technicianDTO = technicianService.updateCurrentTechnician(technicianId, request);
        return ApiResponse.success("更新技师信息成功", technicianDTO);
    }

    /**
     * 获取当前技师工单列表
     */
    @GetMapping("/me/orders")
    @PreAuthorize("hasRole('TECHNICIAN')")
    public ApiResponse<PageResponse<OrderDTO>> getCurrentTechnicianOrders(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String urgencyLevel,
            @RequestParam(required = false) Long faultTypeId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            HttpServletRequest request) {

        Long technicianId = getCurrentTechnicianId(request);
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by("submitTime").descending());

        // 转换日期为LocalDateTime
        LocalDateTime startDateTime = startDate != null ? startDate.atStartOfDay() : null;
        LocalDateTime endDateTime = endDate != null ? endDate.atTime(23, 59, 59) : null;

        PageResponse<OrderDTO> orders = technicianService.getCurrentTechnicianOrders(
            technicianId, status, urgencyLevel, faultTypeId, startDateTime, endDateTime, pageable);
        return ApiResponse.success("获取工单列表成功", orders);
    }

    /**
     * 获取当前技师工时费收入
     */
    @GetMapping("/me/payments")
    @PreAuthorize("hasRole('TECHNICIAN')")
    public ApiResponse<PageResponse<LaborPaymentDTO>> getCurrentTechnicianPayments(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            HttpServletRequest request) {

        Long technicianId = getCurrentTechnicianId(request);
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by("createTime").descending());

        // 转换日期为LocalDateTime
        LocalDateTime startDateTime = startDate != null ? startDate.atStartOfDay() : null;
        LocalDateTime endDateTime = endDate != null ? endDate.atTime(23, 59, 59) : null;

        PageResponse<LaborPaymentDTO> payments = technicianService.getCurrentTechnicianPayments(
            technicianId, startDateTime, endDateTime, pageable);
        return ApiResponse.success("获取工时费收入成功", payments);
    }

    /**
     * 获取当前技师历史记录
     */
    @GetMapping("/me/history")
    @PreAuthorize("hasRole('TECHNICIAN')")
    public ApiResponse<PageResponse<OrderDTO>> getCurrentTechnicianHistory(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Long faultTypeId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            HttpServletRequest request) {

        Long technicianId = getCurrentTechnicianId(request);
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by("actualCompletionTime").descending());

        // 转换日期为LocalDateTime
        LocalDateTime startDateTime = startDate != null ? startDate.atStartOfDay() : null;
        LocalDateTime endDateTime = endDate != null ? endDate.atTime(23, 59, 59) : null;

        PageResponse<OrderDTO> history = technicianService.getCurrentTechnicianHistory(
            technicianId, status, faultTypeId, startDateTime, endDateTime, pageable);
        return ApiResponse.success("获取历史记录成功", history);
    }



    /**
     * 从SecurityContext中获取当前技师ID
     */
    private Long getCurrentTechnicianId(HttpServletRequest request) {
        JwtAuthenticationFilter.UserPrincipal principal =
            (JwtAuthenticationFilter.UserPrincipal) SecurityContextHolder.getContext()
                .getAuthentication().getPrincipal();
        return principal.getUserId();
    }
}
