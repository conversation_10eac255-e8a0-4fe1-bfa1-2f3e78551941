import api from './index'

export const analyticsAPI = {
  // 维修统计
  getRepairStats(params = {}) {
    return api.get('/analytics/repairs', { params })
  },

  // 成本分析
  getCostAnalysis(params = {}) {
    return api.get('/analytics/costs', { params })
  },

  // 工作负载统计
  getWorkloadStats(params = {}) {
    return api.get('/analytics/workload', { params })
  },

  // 故障模式统计
  getPatternStats(params = {}) {
    return api.get('/analytics/patterns', { params })
  },

  // 技师绩效统计
  getTechnicianPerformance(params = {}) {
    return api.get('/analytics/technician-performance', { params })
  },

  // 获取品牌和型号数据（用于筛选器）
  // 注意：由于API文档中没有专门的brands-models端点，
  // 这里通过车辆管理API获取品牌和型号数据
  async getBrandsAndModels() {
    try {
      // 获取所有车辆数据来提取品牌和型号
      const response = await api.get('/admin/vehicles', {
        params: { page: 1, size: 1000 }
      })
      const vehicles = response.data.data?.content || []

      // 提取唯一的品牌和型号
      const brands = [...new Set(vehicles.map(v => v.brand))].filter(Boolean).sort()
      const models = [...new Set(vehicles.map(v => v.model))].filter(Boolean).sort()

      return {
        data: {
          brands,
          models
        }
      }
    } catch (error) {
      console.error('Failed to fetch brands and models:', error)
      return {
        data: {
          brands: [],
          models: []
        }
      }
    }
  },

}
