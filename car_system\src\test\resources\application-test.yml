spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: false
  
  h2:
    console:
      enabled: false

# JWT配置
jwt:
  secret: test-secret-key-for-unit-tests-only
  expiration: 3600000 # 1小时
  refresh-expiration: 604800000 # 7天

# 应用配置
app:
  specialties:
    - code: engine
      name: 发动机维修
    - code: transmission
      name: 变速箱维修
    - code: brake
      name: 制动系统维修
    - code: electrical
      name: 电气系统维修
    - code: suspension
      name: 悬挂系统维修
    - code: air_conditioning
      name: 空调系统维修
    - code: body
      name: 车身维修
    - code: tire
      name: 轮胎维修

# 日志配置
logging:
  level:
    com.example: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
