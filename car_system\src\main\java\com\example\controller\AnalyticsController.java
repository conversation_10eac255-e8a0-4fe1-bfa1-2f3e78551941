package com.example.controller;

import com.example.dto.response.*;
import com.example.service.AnalyticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 数据统计分析控制器
 */
@RestController
@RequestMapping("/analytics")
@PreAuthorize("hasRole('ADMIN')")
public class AnalyticsController {

    @Autowired
    private AnalyticsService analyticsService;

    /**
     * 获取维修统计
     */
    @GetMapping("/repairs")
    public ApiResponse<List<RepairStatisticsDTO>> getRepairStatistics(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "brand") String groupBy) {
        
        List<RepairStatisticsDTO> statistics = analyticsService.getRepairStatistics(startDate, endDate, groupBy);
        return ApiResponse.success("获取维修统计成功", statistics);
    }

    /**
     * 获取成本分析
     */
    @GetMapping("/costs")
    public ApiResponse<List<CostAnalysisDTO>> getCostAnalysis(
            @RequestParam(required = false) Integer year,
            @RequestParam(required = false) Integer quarter) {
        
        List<CostAnalysisDTO> analysis = analyticsService.getCostAnalysis(year, quarter);
        return ApiResponse.success("获取成本分析成功", analysis);
    }

    /**
     * 获取工作负载统计
     */
    @GetMapping("/workload")
    public ApiResponse<List<WorkloadStatisticsDTO>> getWorkloadStatistics(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        List<WorkloadStatisticsDTO> statistics = analyticsService.getWorkloadStatistics(startDate, endDate);
        return ApiResponse.success("获取工作负载统计成功", statistics);
    }

    /**
     * 获取故障模式统计
     */
    @GetMapping("/patterns")
    public ApiResponse<List<PatternStatisticsDTO>> getPatternStatistics(
            @RequestParam(required = false) String brand,
            @RequestParam(required = false) String model) {
        
        List<PatternStatisticsDTO> statistics = analyticsService.getPatternStatistics(brand, model);
        return ApiResponse.success("获取故障模式统计成功", statistics);
    }

    /**
     * 获取技师绩效统计
     */
    @GetMapping("/technician-performance")
    public ApiResponse<List<TechnicianPerformanceDTO>> getTechnicianPerformance(
            @RequestParam(required = false) String specialty,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        List<TechnicianPerformanceDTO> performance = analyticsService.getTechnicianPerformance(specialty, startDate, endDate);
        return ApiResponse.success("获取技师绩效统计成功", performance);
    }
}
