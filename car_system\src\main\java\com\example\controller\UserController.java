package com.example.controller;

import com.example.dto.request.UserRegistrationRequest;
import com.example.dto.request.UserUpdateRequest;
import com.example.dto.response.ApiResponse;
import com.example.dto.response.PageResponse;
import com.example.dto.response.UserDTO;
import com.example.dto.response.VehicleDTO;
import com.example.dto.response.OrderDTO;
import com.example.dto.response.FeedbackDTO;
import com.example.security.JwtAuthenticationFilter;
import com.example.service.UserService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/users")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 用户注册
     */
    @PostMapping
    public ApiResponse<UserDTO> registerUser(@Valid @RequestBody UserRegistrationRequest request) {
        UserDTO userDTO = userService.registerUser(request);
        return ApiResponse.success("注册成功", userDTO);
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<UserDTO> getCurrentUser(HttpServletRequest request) {
        Long userId = getCurrentUserId(request);
        UserDTO userDTO = userService.getCurrentUser(userId);
        return ApiResponse.success("获取用户信息成功", userDTO);
    }

    /**
     * 更新当前用户信息
     */
    @PutMapping("/me")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<UserDTO> updateCurrentUser(@Valid @RequestBody UserUpdateRequest request,
                                                  HttpServletRequest httpRequest) {
        Long userId = getCurrentUserId(httpRequest);
        UserDTO userDTO = userService.updateCurrentUser(userId, request);
        return ApiResponse.success("更新用户信息成功", userDTO);
    }

    /**
     * 获取当前用户车辆列表
     */
    @GetMapping("/me/vehicles")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<PageResponse<VehicleDTO>> getCurrentUserVehicles(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            HttpServletRequest request) {

        Long userId = getCurrentUserId(request);
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by("createTime").descending());
        PageResponse<VehicleDTO> vehicles = userService.getCurrentUserVehicles(userId, pageable);
        return ApiResponse.success("获取车辆列表成功", vehicles);
    }

    /**
     * 获取当前用户工单列表
     */
    @GetMapping("/me/orders")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<PageResponse<OrderDTO>> getCurrentUserOrders(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String licensePlate,
            @RequestParam(required = false) Long faultTypeId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            HttpServletRequest request) {

        Long userId = getCurrentUserId(request);
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by("submitTime").descending());

        // 转换日期为LocalDateTime
        LocalDateTime startDateTime = startDate != null ? startDate.atStartOfDay() : null;
        LocalDateTime endDateTime = endDate != null ? endDate.atTime(23, 59, 59) : null;

        PageResponse<OrderDTO> orders = userService.getCurrentUserOrders(
            userId, status, licensePlate, faultTypeId, startDateTime, endDateTime, pageable);
        return ApiResponse.success("获取工单列表成功", orders);
    }

    /**
     * 获取当前用户维修历史
     */
    @GetMapping("/me/repair-history")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<PageResponse<OrderDTO>> getCurrentUserRepairHistory(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) Long vehicleId,
            @RequestParam(required = false) Long faultTypeId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            HttpServletRequest request) {

        Long userId = getCurrentUserId(request);
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by("submitTime").descending());
        PageResponse<OrderDTO> history = userService.getCurrentUserRepairHistory(
            userId, vehicleId, faultTypeId, startDate, endDate, pageable);
        return ApiResponse.success("获取维修历史成功", history);
    }

    /**
     * 获取当前用户反馈历史
     */
    @GetMapping("/me/feedbacks")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<PageResponse<FeedbackDTO>> getCurrentUserFeedbacks(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) Integer rating,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            HttpServletRequest request) {

        Long userId = getCurrentUserId(request);
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by("feedbackTime").descending());

        PageResponse<FeedbackDTO> feedbacks = userService.getCurrentUserFeedbacks(
                userId, rating, startDate, endDate, pageable);

        return ApiResponse.success("获取反馈历史成功", feedbacks);
    }

    /**
     * 从SecurityContext中获取当前用户ID
     */
    private Long getCurrentUserId(HttpServletRequest request) {
        JwtAuthenticationFilter.UserPrincipal principal =
            (JwtAuthenticationFilter.UserPrincipal) SecurityContextHolder.getContext()
                .getAuthentication().getPrincipal();
        return principal.getUserId();
    }
}
