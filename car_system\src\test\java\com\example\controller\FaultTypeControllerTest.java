package com.example.controller;

import com.example.config.TestSecurityConfig;
import com.example.dto.request.FaultTypeRequest;
import com.example.dto.response.FaultTypeDTO;
import com.example.service.FaultTypeService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(FaultTypeController.class)
@Import(TestSecurityConfig.class)
class FaultTypeControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private FaultTypeService faultTypeService;

    @Autowired
    private ObjectMapper objectMapper;

    private FaultTypeRequest faultTypeRequest;
    private FaultTypeDTO faultTypeDTO;

    @BeforeEach
    void setUp() {
        // 创建故障类型请求
        faultTypeRequest = new FaultTypeRequest();
        faultTypeRequest.setTypeName("发动机故障");
        faultTypeRequest.setDescription("发动机相关故障");
        faultTypeRequest.setRequiredSpecialties(Arrays.asList("engine"));
        faultTypeRequest.setRequiredTechCount(2);
        faultTypeRequest.setEstimatedHours(4.0);

        // 创建故障类型DTO
        faultTypeDTO = new FaultTypeDTO(
                1L, "发动机故障", "发动机相关故障",
                "engine", Arrays.asList("engine"), 2, 4, 1,
                LocalDateTime.now(), LocalDateTime.now()
        );
    }

    @Test
    void testGetFaultTypes_Success() throws Exception {
        // Given
        List<FaultTypeDTO> faultTypes = Arrays.asList(faultTypeDTO);
        when(faultTypeService.getFaultTypes(isNull())).thenReturn(faultTypes);

        // When & Then
        mockMvc.perform(get("/fault-types"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取故障类型列表成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].faultTypeId").value(1))
                .andExpect(jsonPath("$.data[0].typeName").value("发动机故障"));

        verify(faultTypeService).getFaultTypes(isNull());
    }

    @Test
    void testGetFaultTypes_WithStatus() throws Exception {
        // Given
        List<FaultTypeDTO> faultTypes = Arrays.asList(faultTypeDTO);
        when(faultTypeService.getFaultTypes(eq(1))).thenReturn(faultTypes);

        // When & Then
        mockMvc.perform(get("/fault-types")
                        .param("status", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].status").value(1));

        verify(faultTypeService).getFaultTypes(eq(1));
    }

    @Test
    void testGetFaultType_Success() throws Exception {
        // Given
        when(faultTypeService.getFaultType(1L)).thenReturn(faultTypeDTO);

        // When & Then
        mockMvc.perform(get("/fault-types/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取故障类型详情成功"))
                .andExpect(jsonPath("$.data.faultTypeId").value(1))
                .andExpect(jsonPath("$.data.typeName").value("发动机故障"))
                .andExpect(jsonPath("$.data.description").value("发动机相关故障"));

        verify(faultTypeService).getFaultType(1L);
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testCreateFaultType_Success() throws Exception {
        // Given
        when(faultTypeService.createFaultType(any(FaultTypeRequest.class))).thenReturn(faultTypeDTO);

        // When & Then
        mockMvc.perform(post("/fault-types")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(faultTypeRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("创建故障类型成功"))
                .andExpect(jsonPath("$.data.faultTypeId").value(1))
                .andExpect(jsonPath("$.data.typeName").value("发动机故障"));

        verify(faultTypeService).createFaultType(any(FaultTypeRequest.class));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testUpdateFaultType_Success() throws Exception {
        // Given
        when(faultTypeService.updateFaultType(eq(1L), any(FaultTypeRequest.class))).thenReturn(faultTypeDTO);

        // When & Then
        mockMvc.perform(put("/fault-types/1")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(faultTypeRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("更新故障类型成功"))
                .andExpect(jsonPath("$.data.faultTypeId").value(1));

        verify(faultTypeService).updateFaultType(eq(1L), any(FaultTypeRequest.class));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testDeleteFaultType_Success() throws Exception {
        // Given
        doNothing().when(faultTypeService).deleteFaultType(1L);

        // When & Then
        mockMvc.perform(delete("/fault-types/1")
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("删除故障类型成功"));

        verify(faultTypeService).deleteFaultType(1L);
    }

    @Test
    @WithMockUser(roles = "USER") // 错误的角色
    void testCreateFaultType_Forbidden() throws Exception {
        // When & Then
        mockMvc.perform(post("/fault-types")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(faultTypeRequest)))
                .andExpect(status().isForbidden());

        verify(faultTypeService, never()).createFaultType(any(FaultTypeRequest.class));
    }

    @Test
    @WithMockUser(roles = "USER") // 错误的角色
    void testUpdateFaultType_Forbidden() throws Exception {
        // When & Then
        mockMvc.perform(put("/fault-types/1")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(faultTypeRequest)))
                .andExpect(status().isForbidden());

        verify(faultTypeService, never()).updateFaultType(anyLong(), any(FaultTypeRequest.class));
    }

    @Test
    @WithMockUser(roles = "USER") // 错误的角色
    void testDeleteFaultType_Forbidden() throws Exception {
        // When & Then
        mockMvc.perform(delete("/fault-types/1")
                        .with(csrf()))
                .andExpect(status().isForbidden());

        verify(faultTypeService, never()).deleteFaultType(anyLong());
    }

    @Test
    void testCreateFaultType_Unauthorized() throws Exception {
        // When & Then
        mockMvc.perform(post("/fault-types")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(faultTypeRequest)))
                .andExpect(status().isUnauthorized());

        verify(faultTypeService, never()).createFaultType(any(FaultTypeRequest.class));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testCreateFaultType_InvalidInput() throws Exception {
        // Given
        faultTypeRequest.setTypeName(null); // 缺少必填字段

        // When & Then
        mockMvc.perform(post("/fault-types")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(faultTypeRequest)))
                .andExpect(status().isBadRequest());

        verify(faultTypeService, never()).createFaultType(any(FaultTypeRequest.class));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testCreateFaultType_MissingDescription() throws Exception {
        // Given
        faultTypeRequest.setDescription(null);

        // When & Then
        mockMvc.perform(post("/fault-types")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(faultTypeRequest)))
                .andExpect(status().isBadRequest());

        verify(faultTypeService, never()).createFaultType(any(FaultTypeRequest.class));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testCreateFaultType_EmptySpecialties() throws Exception {
        // Given
        faultTypeRequest.setRequiredSpecialties(Arrays.asList());

        // When & Then
        mockMvc.perform(post("/fault-types")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(faultTypeRequest)))
                .andExpect(status().isBadRequest());

        verify(faultTypeService, never()).createFaultType(any(FaultTypeRequest.class));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testUpdateFaultType_InvalidInput() throws Exception {
        // Given
        faultTypeRequest.setRequiredTechCount(null); // 缺少必填字段

        // When & Then
        mockMvc.perform(put("/fault-types/1")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(faultTypeRequest)))
                .andExpect(status().isBadRequest());

        verify(faultTypeService, never()).updateFaultType(anyLong(), any(FaultTypeRequest.class));
    }

    @Test
    void testGetFaultType_InvalidId() throws Exception {
        // When & Then
        mockMvc.perform(get("/fault-types/invalid"))
                .andExpect(status().isBadRequest());

        verify(faultTypeService, never()).getFaultType(anyLong());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testDeleteFaultType_InvalidId() throws Exception {
        // When & Then
        mockMvc.perform(delete("/fault-types/invalid")
                        .with(csrf()))
                .andExpect(status().isBadRequest());

        verify(faultTypeService, never()).deleteFaultType(anyLong());
    }
}
