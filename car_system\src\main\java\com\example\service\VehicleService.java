package com.example.service;

import com.example.dto.request.VehicleRequest;
import com.example.dto.response.VehicleDTO;
import com.example.entity.User;
import com.example.entity.Vehicle;
import com.example.exception.BusinessException;
import com.example.exception.ResourceNotFoundException;
import com.example.repository.UserRepository;
import com.example.repository.VehicleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 车辆服务类
 */
@Service
@Transactional
public class VehicleService {

    @Autowired
    private VehicleRepository vehicleRepository;

    @Autowired
    private UserRepository userRepository;

    /**
     * 添加车辆
     */
    public VehicleDTO addVehicle(Long userId, VehicleRequest request) {
        // 检查用户是否存在
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));

        // 检查车牌号是否已存在
        if (vehicleRepository.existsByLicensePlate(request.getLicensePlate())) {
            throw new BusinessException("车牌号已存在");
        }

        // 检查VIN码是否已存在
        if (vehicleRepository.existsByVin(request.getVin())) {
            throw new BusinessException("VIN码已存在");
        }

        // 创建新车辆
        Vehicle vehicle = new Vehicle();
        vehicle.setLicensePlate(request.getLicensePlate());
        vehicle.setBrand(request.getBrand());
        vehicle.setModel(request.getModel());
        vehicle.setYear(request.getYear());
        vehicle.setVin(request.getVin());
        vehicle.setColor(request.getColor());
        vehicle.setEngineNumber(request.getEngineNumber());
        vehicle.setRegisterDate(request.getRegisterDate());
        vehicle.setUser(user);

        Vehicle savedVehicle = vehicleRepository.save(vehicle);
        return convertToDTO(savedVehicle);
    }

    /**
     * 获取车辆详情
     */
    @Transactional(readOnly = true)
    public VehicleDTO getVehicle(Long vehicleId, Long userId) {
        Vehicle vehicle = vehicleRepository.findById(vehicleId)
            .orElseThrow(() -> new ResourceNotFoundException("车辆不存在"));

        // 检查车辆是否属于当前用户
        if (!vehicle.getUser().getUserId().equals(userId)) {
            throw new BusinessException("无权访问该车辆信息");
        }

        return convertToDTO(vehicle);
    }

    /**
     * 更新车辆信息
     */
    public VehicleDTO updateVehicle(Long vehicleId, Long userId, VehicleRequest request) {
        Vehicle vehicle = vehicleRepository.findById(vehicleId)
            .orElseThrow(() -> new ResourceNotFoundException("车辆不存在"));

        // 检查车辆是否属于当前用户
        if (!vehicle.getUser().getUserId().equals(userId)) {
            throw new BusinessException("无权修改该车辆信息");
        }

        // 检查车牌号是否被其他车辆使用
        if (!vehicle.getLicensePlate().equals(request.getLicensePlate()) 
            && vehicleRepository.existsByLicensePlate(request.getLicensePlate())) {
            throw new BusinessException("车牌号已被其他车辆使用");
        }

        // 检查VIN码是否被其他车辆使用
        if (!vehicle.getVin().equals(request.getVin()) 
            && vehicleRepository.existsByVin(request.getVin())) {
            throw new BusinessException("VIN码已被其他车辆使用");
        }

        // 更新车辆信息
        vehicle.setLicensePlate(request.getLicensePlate());
        vehicle.setBrand(request.getBrand());
        vehicle.setModel(request.getModel());
        vehicle.setYear(request.getYear());
        vehicle.setVin(request.getVin());
        vehicle.setColor(request.getColor());
        vehicle.setEngineNumber(request.getEngineNumber());
        vehicle.setRegisterDate(request.getRegisterDate());

        Vehicle updatedVehicle = vehicleRepository.save(vehicle);
        return convertToDTO(updatedVehicle);
    }

    /**
     * 删除车辆
     */
    public void deleteVehicle(Long vehicleId, Long userId) {
        Vehicle vehicle = vehicleRepository.findById(vehicleId)
            .orElseThrow(() -> new ResourceNotFoundException("车辆不存在"));

        // 检查车辆是否属于当前用户
        if (!vehicle.getUser().getUserId().equals(userId)) {
            throw new BusinessException("无权删除该车辆");
        }

        // 检查车辆是否有未完成的维修工单
        boolean hasActiveOrders = vehicle.getRepairOrders().stream()
            .anyMatch(order -> order.getStatus() != com.example.entity.RepairOrder.OrderStatus.COMPLETED 
                            && order.getStatus() != com.example.entity.RepairOrder.OrderStatus.CANCELLED);

        if (hasActiveOrders) {
            throw new BusinessException("该车辆有未完成的维修工单，无法删除");
        }

        vehicleRepository.delete(vehicle);
    }

    /**
     * 将Vehicle实体转换为VehicleDTO
     */
    private VehicleDTO convertToDTO(Vehicle vehicle) {
        return new VehicleDTO(
            vehicle.getVehicleId(),
            vehicle.getUser().getUserId(),
            vehicle.getLicensePlate(),
            vehicle.getBrand(),
            vehicle.getModel(),
            vehicle.getYear(),
            vehicle.getVin(),
            vehicle.getColor(),
            vehicle.getEngineNumber(),
            vehicle.getRegisterDate(),
            vehicle.getCreateTime(),
            vehicle.getUpdateTime()
        );
    }
}
