package com.example.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "labor_payments")
public class LaborPayment extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "payment_id")
    private Long paymentId;

    @NotNull(message = "年份不能为空")
    @Min(value = 2020, message = "年份不能早于2020年")
    @Column(name = "payment_year", nullable = false)
    private Integer year;

    @NotNull(message = "月份不能为空")
    @Min(value = 1, message = "月份最小为1")
    @Max(value = 12, message = "月份最大为12")
    @Column(name = "payment_month", nullable = false)
    private Integer month;

    @NotNull(message = "总工时不能为空")
    @DecimalMin(value = "0.0", message = "总工时不能为负数")
    @Column(name = "total_hours", nullable = false, precision = 8, scale = 2)
    private BigDecimal totalHours;

    @NotNull(message = "时薪不能为空")
    @DecimalMin(value = "0.0", inclusive = false, message = "时薪必须大于0")
    @Column(name = "hourly_rate", nullable = false, precision = 10, scale = 2)
    private BigDecimal hourlyRate;

    @NotNull(message = "总金额不能为空")
    @DecimalMin(value = "0.0", message = "总金额不能为负数")
    @Column(name = "total_amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal totalAmount;

    @NotNull(message = "支付状态不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "payment_status", nullable = false, length = 20)
    private PaymentStatus paymentStatus = PaymentStatus.PAID;

    @Column(name = "payment_time")
    private LocalDateTime paymentTime;

    // 多对一关系：工时费记录属于一个技师
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "technician_id", nullable = false)
    private Technician technician;

    // 枚举：支付状态
    public enum PaymentStatus {
        PENDING("待支付"),
        PAID("已支付"),
        CANCELLED("已取消");

        private final String displayName;

        PaymentStatus(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    // 构造函数
    public LaborPayment() {}

    public LaborPayment(Integer year, Integer month, BigDecimal totalHours,
                       BigDecimal hourlyRate, Technician technician) {
        this.year = year;
        this.month = month;
        this.totalHours = totalHours;
        this.hourlyRate = hourlyRate;
        this.technician = technician;
        this.totalAmount = totalHours.multiply(hourlyRate);
        // 默认已支付状态，设置支付时间为当前时间
        this.paymentStatus = PaymentStatus.PAID;
        this.paymentTime = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getPaymentId() {
        return paymentId;
    }

    public void setPaymentId(Long paymentId) {
        this.paymentId = paymentId;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getMonth() {
        return month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public BigDecimal getTotalHours() {
        return totalHours;
    }

    public void setTotalHours(BigDecimal totalHours) {
        this.totalHours = totalHours;
    }

    public BigDecimal getHourlyRate() {
        return hourlyRate;
    }

    public void setHourlyRate(BigDecimal hourlyRate) {
        this.hourlyRate = hourlyRate;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public PaymentStatus getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(PaymentStatus paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public LocalDateTime getPaymentTime() {
        return paymentTime;
    }

    public void setPaymentTime(LocalDateTime paymentTime) {
        this.paymentTime = paymentTime;
    }

    public Technician getTechnician() {
        return technician;
    }

    public void setTechnician(Technician technician) {
        this.technician = technician;
    }
}
