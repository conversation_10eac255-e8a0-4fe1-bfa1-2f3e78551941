package com.example.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import com.example.entity.Technician.Specialty;

@Entity
@Table(name = "fault_types")
public class FaultType extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "fault_type_id")
    private Long faultTypeId;

    @NotBlank(message = "故障类型名称不能为空")
    @Size(max = 100, message = "故障类型名称长度不能超过100个字符")
    @Column(name = "type_name", nullable = false, length = 100)
    private String typeName;

    @Size(max = 500, message = "描述长度不能超过500个字符")
    @Column(name = "description", length = 500)
    private String description;

    @NotEmpty(message = "所需工种不能为空")
    @ElementCollection(targetClass = Technician.Specialty.class, fetch = FetchType.EAGER)
    @Enumerated(EnumType.STRING)
    @CollectionTable(name = "fault_type_specialties",
                    joinColumns = @JoinColumn(name = "fault_type_id"))
    @Column(name = "specialty")
    private Set<Technician.Specialty> requiredSpecialties;

    @NotNull(message = "所需技师数量不能为空")
    @Min(value = 1, message = "所需技师数量至少为1")
    @Column(name = "required_tech_count", nullable = false)
    private Integer requiredTechCount;

    @NotNull(message = "预估工时不能为空")
    @Min(value = 1, message = "预估工时至少为1小时")
    @Column(name = "estimated_hours", nullable = false)
    private Integer estimatedHours;

    @Column(name = "status", nullable = false)
    private Integer status = 1; // 1: 启用, 0: 禁用

    // 一对多关系：故障类型对应多个维修工单
    @OneToMany(mappedBy = "faultType", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<RepairOrder> repairOrders = new ArrayList<>();

    // 构造函数
    public FaultType() {}

    public FaultType(String typeName, String description, Set<Technician.Specialty> requiredSpecialties,
                    Integer requiredTechCount, Integer estimatedHours) {
        this.typeName = typeName;
        this.description = description;
        this.requiredSpecialties = requiredSpecialties;
        this.requiredTechCount = requiredTechCount;
        this.estimatedHours = estimatedHours;
    }

    // Getters and Setters
    public Long getFaultTypeId() {
        return faultTypeId;
    }

    public void setFaultTypeId(Long faultTypeId) {
        this.faultTypeId = faultTypeId;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Set<Technician.Specialty> getRequiredSpecialties() {
        return requiredSpecialties;
    }

    public void setRequiredSpecialties(Set<Specialty> requiredSpecialties) {
        this.requiredSpecialties = requiredSpecialties;
    }

    public Integer getRequiredTechCount() {
        return requiredTechCount;
    }

    public void setRequiredTechCount(Integer requiredTechCount) {
        this.requiredTechCount = requiredTechCount;
    }

    public Integer getEstimatedHours() {
        return estimatedHours;
    }

    public void setEstimatedHours(Integer estimatedHours) {
        this.estimatedHours = estimatedHours;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<RepairOrder> getRepairOrders() {
        return repairOrders;
    }

    public void setRepairOrders(List<RepairOrder> repairOrders) {
        this.repairOrders = repairOrders;
    }
}
