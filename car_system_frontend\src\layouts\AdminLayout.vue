<template>
  <BaseLayout :menu-items="menuItems" />
</template>

<script setup>
import {
  House, User, UserFilled, Van, Document, Setting,
  Box, TrendCharts
} from '@element-plus/icons-vue'
import BaseLayout from './BaseLayout.vue'

// 管理员端菜单配置
const menuItems = [
  {
    path: '/admin/dashboard',
    title: '仪表盘',
    icon: House
  },
  {
    path: '/admin/users',
    title: '用户管理',
    icon: User
  },
  {
    path: '/admin/technicians',
    title: '技师管理',
    icon: UserFilled
  },
  {
    path: '/admin/vehicles',
    title: '车辆管理',
    icon: Van
  },
  {
    path: '/admin/orders',
    title: '工单管理',
    icon: Document
  },
  {
    path: '/admin/fault-types',
    title: '故障类型管理',
    icon: Setting
  },
  {
    path: '/admin/materials',
    title: '材料管理',
    icon: Box
  },
  {
    path: '/admin/analytics',
    title: '数据统计',
    icon: Trend<PERSON>harts
  }
]
</script>
