<template>
  <div class="vehicles-page">
    <div class="page-header">
      <h1>我的车辆</h1>
      <el-button type="primary" @click="showAddDialog = true">
        <el-icon><Plus /></el-icon>
        添加车辆
      </el-button>
    </div>

    <!-- 车辆列表 -->
    <el-card>
      <!-- 移动端卡片视图 -->
      <div v-if="isMobile" class="mobile-vehicle-list">
        <div v-for="vehicle in vehicles" :key="vehicle.vehicleId" class="mobile-vehicle-card">
          <el-card shadow="hover">
            <div class="vehicle-info">
              <div class="vehicle-header">
                <h3 class="license-plate">{{ vehicle.licensePlate }}</h3>
                <el-tag type="primary">{{ vehicle.brand }} {{ vehicle.model }}</el-tag>
              </div>
              <div class="vehicle-details">
                <div class="detail-item">
                  <span class="label">年份:</span>
                  <span class="value">{{ vehicle.year }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">颜色:</span>
                  <span class="value">{{ vehicle.color }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">车架号:</span>
                  <span class="value">{{ vehicle.vin }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">注册日期:</span>
                  <span class="value">{{ formatDate(vehicle.registerDate) }}</span>
                </div>
              </div>
              <div class="vehicle-actions">
                <el-button type="primary" size="small" @click="editVehicle(vehicle)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button type="danger" size="small" @click="deleteVehicle(vehicle)">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </el-card>
        </div>

        <div v-if="!loading && vehicles.length === 0" class="empty-state">
          <el-empty description="暂无车辆信息">
            <el-button type="primary" @click="showAddDialog = true">
              <el-icon><Plus /></el-icon>
              添加第一辆车
            </el-button>
          </el-empty>
        </div>
      </div>

      <!-- 桌面端表格视图 -->
      <div v-else class="table-responsive">
        <el-table
          v-loading="loading"
          :data="vehicles"
          style="width: 100%"
          empty-text="暂无车辆信息"
          stripe
          highlight-current-row
        >
          <el-table-column prop="licensePlate" label="车牌号" width="120" sortable />
          <el-table-column prop="brand" label="品牌" width="100" sortable />
          <el-table-column prop="model" label="型号" width="120" sortable />
          <el-table-column prop="year" label="年份" width="80" sortable />
          <el-table-column prop="color" label="颜色" width="80" />
          <el-table-column prop="vin" label="车架号" min-width="150" show-overflow-tooltip />
          <el-table-column prop="registerDate" label="注册日期" width="120" sortable>
            <template #default="{ row }">
              {{ formatDate(row.registerDate) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="editVehicle(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button
                type="text"
                size="small"
                style="color: #f56c6c"
                @click="deleteVehicle(row)"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchVehicles"
          @current-change="fetchVehicles"
        />
      </div>
    </el-card>

    <!-- 添加/编辑车辆对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingVehicle ? '编辑车辆' : '添加车辆'"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="vehicleFormRef"
        :model="vehicleForm"
        :rules="vehicleRules"
        label-width="100px"
      >
        <el-form-item label="车牌号" prop="licensePlate">
          <el-input
            v-model="vehicleForm.licensePlate"
            placeholder="请输入车牌号"
            maxlength="10"
          />
        </el-form-item>

        <el-form-item label="品牌" prop="brand">
          <el-input
            v-model="vehicleForm.brand"
            placeholder="请输入品牌"
            maxlength="50"
          />
        </el-form-item>

        <el-form-item label="型号" prop="model">
          <el-input
            v-model="vehicleForm.model"
            placeholder="请输入型号"
            maxlength="50"
          />
        </el-form-item>

        <el-form-item label="年份" prop="year">
          <el-input-number
            v-model="vehicleForm.year"
            :min="1900"
            :max="new Date().getFullYear() + 1"
            placeholder="请输入年份"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="颜色" prop="color">
          <el-input
            v-model="vehicleForm.color"
            placeholder="请输入颜色"
            maxlength="20"
          />
        </el-form-item>

        <el-form-item label="车架号" prop="vin">
          <el-input
            v-model="vehicleForm.vin"
            placeholder="请输入17位车架号"
            maxlength="17"
          />
        </el-form-item>

        <el-form-item label="发动机号" prop="engineNumber">
          <el-input
            v-model="vehicleForm.engineNumber"
            placeholder="请输入发动机号"
            maxlength="50"
          />
        </el-form-item>

        <el-form-item label="注册日期" prop="registerDate">
          <el-date-picker
            v-model="vehicleForm.registerDate"
            type="date"
            placeholder="请选择注册日期"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="submitVehicle">
          {{ editingVehicle ? '更新' : '添加' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'
import { userAPI } from '@/api/user'
import { vehicleAPI } from '@/api/vehicle'
import { useAppStore } from '@/stores/app'
import dayjs from 'dayjs'

const appStore = useAppStore()

// 计算属性
const isMobile = computed(() => appStore.isMobile)

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const showAddDialog = ref(false)
const editingVehicle = ref(null)
const vehicles = ref([])

// 表单引用
const vehicleFormRef = ref()

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 车辆表单数据
const vehicleForm = reactive({
  licensePlate: '',
  brand: '',
  model: '',
  year: null,
  vin: '',
  color: '',
  engineNumber: '',
  registerDate: ''
})

// 表单验证规则
const vehicleRules = {
  licensePlate: [
    { required: true, message: '请输入车牌号', trigger: 'blur' },
    { pattern: /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4,5}[A-Z0-9挂学警港澳]$/, message: '请输入正确的车牌号', trigger: 'blur' }
  ],
  brand: [
    { required: true, message: '请输入品牌', trigger: 'blur' }
  ],
  model: [
    { required: true, message: '请输入型号', trigger: 'blur' }
  ],
  year: [
    { required: true, message: '请输入年份', trigger: 'blur' },
    { type: 'number', min: 1900, max: new Date().getFullYear() + 1, message: '请输入正确的年份', trigger: 'blur' }
  ],
  color: [
    { required: true, message: '请输入颜色', trigger: 'blur' }
  ],
  vin: [
    { required: true, message: '请输入车架号', trigger: 'blur' },
    { len: 17, message: '车架号必须为17位', trigger: 'blur' }
  ],
  engineNumber: [
    { required: true, message: '请输入发动机号', trigger: 'blur' }
  ],
  registerDate: [
    { required: true, message: '请选择注册日期', trigger: 'change' }
  ]
}

// 获取车辆列表
const fetchVehicles = async () => {
  try {
    loading.value = true
    const response = await userAPI.getCurrentUserVehicles({
      page: pagination.page,
      size: pagination.size
    })

    vehicles.value = response.data.data?.content || []
    pagination.total = response.data.data?.page?.totalElements || 0
  } catch (error) {
    console.error('Failed to fetch vehicles:', error)
    ElMessage.error('获取车辆列表失败')
  } finally {
    loading.value = false
  }
}

// 编辑车辆
const editVehicle = (vehicle) => {
  editingVehicle.value = vehicle
  Object.assign(vehicleForm, vehicle)
  showAddDialog.value = true
}

// 删除车辆
const deleteVehicle = async (vehicle) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除车辆 ${vehicle.licensePlate} 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await vehicleAPI.delete(vehicle.vehicleId)
    ElMessage.success('删除成功')
    fetchVehicles()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete vehicle:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 提交车辆表单
const submitVehicle = async () => {
  if (!vehicleFormRef.value) return

  try {
    await vehicleFormRef.value.validate()
    submitting.value = true

    if (editingVehicle.value) {
      // 更新车辆
      await vehicleAPI.update(editingVehicle.value.vehicleId, vehicleForm)
      ElMessage.success('更新成功')
    } else {
      // 添加车辆
      await vehicleAPI.create(vehicleForm)
      ElMessage.success('添加成功')
    }

    showAddDialog.value = false
    fetchVehicles()
  } catch (error) {
    console.error('Failed to submit vehicle:', error)
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  editingVehicle.value = null
  Object.assign(vehicleForm, {
    licensePlate: '',
    brand: '',
    model: '',
    year: null,
    vin: '',
    color: '',
    engineNumber: '',
    registerDate: ''
  })
  if (vehicleFormRef.value) {
    vehicleFormRef.value.resetFields()
  }
}

// 格式化日期
const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchVehicles()
})
</script>

<style scoped>
.vehicles-page {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 移动端车辆卡片样式 */
.mobile-vehicle-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.mobile-vehicle-card {
  width: 100%;
}

.vehicle-info {
  padding: 0;
}

.vehicle-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.license-plate {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.vehicle-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-bottom: 15px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.detail-item .label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.detail-item .value {
  font-size: 14px;
  color: #303133;
  word-break: break-all;
}

.vehicle-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  padding-top: 10px;
  border-top: 1px solid #ebeef5;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .page-header h1 {
    font-size: 20px;
  }

  :deep(.el-table) {
    font-size: 12px;
  }

  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto !important;
  }

  .pagination-container {
    margin-top: 15px;
  }

  :deep(.el-pagination) {
    justify-content: center;
    flex-wrap: wrap;
  }

  :deep(.el-pagination .el-pagination__sizes) {
    order: 3;
    margin-top: 10px;
  }
}

@media (max-width: 480px) {
  .vehicles-page {
    padding: 0 10px;
  }

  .page-header {
    gap: 10px;
  }

  .page-header h1 {
    font-size: 18px;
  }

  .vehicle-details {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .vehicle-actions {
    flex-direction: column;
    gap: 8px;
  }

  .vehicle-actions .el-button {
    width: 100%;
  }

  :deep(.el-table .el-table__cell) {
    padding: 8px 4px;
  }

  :deep(.el-pagination .el-pagination__sizes),
  :deep(.el-pagination .el-pagination__jump) {
    display: none;
  }

  :deep(.el-dialog) {
    width: 98% !important;
    margin: 2vh auto !important;
  }

  :deep(.el-form-item__label) {
    font-size: 14px;
    line-height: 1.4;
  }

  :deep(.el-input__inner),
  :deep(.el-input-number__inner) {
    font-size: 14px;
  }
}
</style>
