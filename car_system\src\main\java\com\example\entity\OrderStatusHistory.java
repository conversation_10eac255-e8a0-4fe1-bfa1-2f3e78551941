package com.example.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;

import java.time.LocalDateTime;

@Entity
@Table(name = "order_status_history")
public class OrderStatusHistory extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "history_id")
    private Long historyId;

    @Enumerated(EnumType.STRING)
    @Column(name = "from_status", length = 20)
    private RepairOrder.OrderStatus fromStatus;

    @NotNull(message = "新状态不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "to_status", nullable = false, length = 20)
    private RepairOrder.OrderStatus toStatus;

    @NotNull(message = "状态变更时间不能为空")
    @Column(name = "change_time", nullable = false)
    private LocalDateTime changeTime;

    @Size(max = 500, message = "备注长度不能超过500个字符")
    @Column(name = "remark", length = 500)
    private String remark;

    @Size(max = 100, message = "操作人长度不能超过100个字符")
    @Column(name = "operator", length = 100)
    private String operator;

    // 多对一关系：状态历史属于一个工单
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id", nullable = false)
    private RepairOrder repairOrder;

    // 构造函数
    public OrderStatusHistory() {}

    public OrderStatusHistory(RepairOrder.OrderStatus fromStatus, RepairOrder.OrderStatus toStatus,
                             RepairOrder repairOrder, String operator) {
        this.fromStatus = fromStatus;
        this.toStatus = toStatus;
        this.repairOrder = repairOrder;
        this.operator = operator;
        this.changeTime = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getHistoryId() {
        return historyId;
    }

    public void setHistoryId(Long historyId) {
        this.historyId = historyId;
    }

    public RepairOrder.OrderStatus getFromStatus() {
        return fromStatus;
    }

    public void setFromStatus(RepairOrder.OrderStatus fromStatus) {
        this.fromStatus = fromStatus;
    }

    public RepairOrder.OrderStatus getToStatus() {
        return toStatus;
    }

    public void setToStatus(RepairOrder.OrderStatus toStatus) {
        this.toStatus = toStatus;
    }

    public LocalDateTime getChangeTime() {
        return changeTime;
    }

    public void setChangeTime(LocalDateTime changeTime) {
        this.changeTime = changeTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public RepairOrder getRepairOrder() {
        return repairOrder;
    }

    public void setRepairOrder(RepairOrder repairOrder) {
        this.repairOrder = repairOrder;
    }
}
