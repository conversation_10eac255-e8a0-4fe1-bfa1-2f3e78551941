package com.example.repository;

import com.example.entity.OrderMaterialUsage;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface OrderMaterialUsageRepository extends JpaRepository<OrderMaterialUsage, Long> {

    /**
     * 根据工单ID查找材料使用记录
     */
    List<OrderMaterialUsage> findByRepairOrderOrderId(Long orderId);

    /**
     * 根据工单ID查找材料使用记录（分页）
     */
    Page<OrderMaterialUsage> findByRepairOrderOrderId(Long orderId, Pageable pageable);

    /**
     * 根据材料ID查找使用记录
     */
    Page<OrderMaterialUsage> findByMaterialMaterialId(Long materialId, Pageable pageable);

    /**
     * 根据时间范围查找材料使用记录
     */
    @Query("SELECT omu FROM OrderMaterialUsage omu WHERE omu.useTime BETWEEN :startTime AND :endTime")
    Page<OrderMaterialUsage> findByUseTimeBetween(@Param("startTime") LocalDateTime startTime,
                                                  @Param("endTime") LocalDateTime endTime,
                                                  Pageable pageable);

    /**
     * 计算工单的总材料成本
     */
    @Query("SELECT COALESCE(SUM(omu.totalPrice), 0) FROM OrderMaterialUsage omu WHERE omu.repairOrder.orderId = :orderId")
    BigDecimal calculateTotalMaterialCostByOrderId(@Param("orderId") Long orderId);

    /**
     * 统计材料的使用次数
     */
    @Query("SELECT COUNT(omu) FROM OrderMaterialUsage omu WHERE omu.material.materialId = :materialId")
    long countUsageByMaterialId(@Param("materialId") Long materialId);

    /**
     * 统计材料的总使用量
     */
    @Query("SELECT COALESCE(SUM(omu.quantity), 0) FROM OrderMaterialUsage omu WHERE omu.material.materialId = :materialId")
    BigDecimal sumQuantityByMaterialId(@Param("materialId") Long materialId);

    /**
     * 根据材料分类统计使用情况
     */
    @Query("SELECT m.category, COUNT(omu), COALESCE(SUM(omu.totalPrice), 0) " +
           "FROM OrderMaterialUsage omu JOIN omu.material m " +
           "WHERE omu.useTime BETWEEN :startTime AND :endTime " +
           "GROUP BY m.category")
    List<Object[]> getMaterialUsageStatsByCategory(@Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 计算年度材料成本
     */
    @Query("SELECT COALESCE(SUM(omu.totalPrice), 0) FROM OrderMaterialUsage omu " +
           "WHERE YEAR(omu.useTime) = :year")
    BigDecimal calculateYearlyMaterialCost(@Param("year") Integer year);

    /**
     * 计算季度材料成本
     */
    @Query("SELECT COALESCE(SUM(omu.totalPrice), 0) FROM OrderMaterialUsage omu " +
           "WHERE YEAR(omu.useTime) = :year AND MONTH(omu.useTime) BETWEEN :startMonth AND :endMonth")
    BigDecimal calculateQuarterlyMaterialCost(@Param("year") Integer year,
                                             @Param("startMonth") Integer startMonth,
                                             @Param("endMonth") Integer endMonth);
}
