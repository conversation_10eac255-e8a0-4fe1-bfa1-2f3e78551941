package com.example.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;

import java.time.LocalDateTime;

@Entity
@Table(name = "order_feedback")
public class OrderFeedback extends BaseEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "feedback_id")
    private Long feedbackId;
    
    @NotNull(message = "评分不能为空")
    @Min(value = 1, message = "评分最低为1分")
    @Max(value = 5, message = "评分最高为5分")
    @Column(name = "rating", nullable = false)
    private Integer rating;
    
    @Size(max = 1000, message = "评价内容长度不能超过1000个字符")
    @Column(name = "comment", length = 1000)
    private String comment;
    
    @NotNull(message = "反馈时间不能为空")
    @Column(name = "feedback_time", nullable = false)
    private LocalDateTime feedbackTime;
    
    // 一对一关系：反馈属于一个工单
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id", nullable = false)
    private RepairOrder repairOrder;
    
    // 构造函数
    public OrderFeedback() {}
    
    public OrderFeedback(Integer rating, String comment, RepairOrder repairOrder) {
        this.rating = rating;
        this.comment = comment;
        this.repairOrder = repairOrder;
        this.feedbackTime = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getFeedbackId() {
        return feedbackId;
    }
    
    public void setFeedbackId(Long feedbackId) {
        this.feedbackId = feedbackId;
    }
    
    public Integer getRating() {
        return rating;
    }
    
    public void setRating(Integer rating) {
        this.rating = rating;
    }
    
    public String getComment() {
        return comment;
    }
    
    public void setComment(String comment) {
        this.comment = comment;
    }
    
    public LocalDateTime getFeedbackTime() {
        return feedbackTime;
    }
    
    public void setFeedbackTime(LocalDateTime feedbackTime) {
        this.feedbackTime = feedbackTime;
    }
    
    public RepairOrder getRepairOrder() {
        return repairOrder;
    }
    
    public void setRepairOrder(RepairOrder repairOrder) {
        this.repairOrder = repairOrder;
    }
}
