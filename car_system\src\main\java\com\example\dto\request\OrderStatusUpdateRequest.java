package com.example.dto.request;

import jakarta.validation.constraints.*;

/**
 * 工单状态更新请求DTO
 */
public class OrderStatusUpdateRequest {

    @NotBlank(message = "状态不能为空")
    @Pattern(regexp = "^(pending|assigned|accepted|in_progress|completed|cancelled)$", 
             message = "状态必须是pending、assigned、accepted、in_progress、completed或cancelled")
    private String status;

    @Size(max = 500, message = "原因长度不能超过500个字符")
    private String reason;

    public OrderStatusUpdateRequest() {}

    public OrderStatusUpdateRequest(String status, String reason) {
        this.status = status;
        this.reason = reason;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}
