<template>
  <el-card class="material-usage-card">
    <template #header>
      <div class="card-header">
        <span>材料使用记录</span>
        <el-tag v-if="materialUsages.length > 0" type="info" size="small">
          共 {{ materialUsages.length }} 项
        </el-tag>
      </div>
    </template>

    <div v-if="materialUsages.length === 0" class="empty-state">
      <el-empty description="暂无材料使用记录" :image-size="80" />
    </div>

    <div v-else class="material-usage-content">
      <!-- 材料使用列表 -->
      <div class="material-usage-list">
        <div
          v-for="usage in materialUsages"
          :key="usage.usageId"
          class="material-usage-item"
        >
          <div class="material-info">
            <div class="material-name">{{ usage.materialName }}</div>
            <div class="material-spec">
              {{ usage.specification || '无规格' }}
            </div>
            <div class="material-category" v-if="usage.category">
              <el-tag size="small" type="info">{{ usage.category }}</el-tag>
            </div>
          </div>
          
          <div class="usage-details">
            <div class="usage-row">
              <span class="label">数量:</span>
              <span class="value">{{ usage.quantity }} {{ usage.unit }}</span>
            </div>
            <div class="usage-row">
              <span class="label">单价:</span>
              <span class="value">¥{{ usage.unitPrice }}</span>
            </div>
            <div class="usage-row">
              <span class="label">小计:</span>
              <span class="value total-price">¥{{ usage.totalPrice }}</span>
            </div>
            <div class="usage-row" v-if="usage.useTime">
              <span class="label">使用时间:</span>
              <span class="value time">{{ formatDate(usage.useTime) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 材料费用汇总 -->
      <div class="material-summary">
        <el-divider />
        <div class="summary-row">
          <span class="summary-label">材料总费用:</span>
          <span class="summary-value">¥{{ calculateTotal() }}</span>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { computed } from 'vue'
import dayjs from 'dayjs'

// Props
const props = defineProps({
  materialUsages: {
    type: Array,
    default: () => []
  },
  showSummary: {
    type: Boolean,
    default: true
  }
})

// 计算材料总费用
const calculateTotal = () => {
  const total = props.materialUsages.reduce((total, usage) => {
    return total + (parseFloat(usage.totalPrice) || 0)
  }, 0)
  return total.toFixed(2)
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未设置'
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}
</script>

<style scoped>
.material-usage-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-state {
  padding: 20px;
  text-align: center;
}

.material-usage-content {
  width: 100%;
}

.material-usage-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.material-usage-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
  transition: all 0.3s ease;
}

.material-usage-item:hover {
  background: #f0f2f5;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.material-info {
  flex: 1;
  margin-right: 20px;
}

.material-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 6px;
  font-size: 16px;
}

.material-spec {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.material-category {
  margin-top: 8px;
}

.usage-details {
  text-align: right;
  min-width: 200px;
}

.usage-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  font-size: 14px;
}

.usage-row:last-child {
  margin-bottom: 0;
}

.label {
  color: #666;
  margin-right: 12px;
}

.value {
  color: #333;
  font-weight: 500;
}

.total-price {
  color: #e6a23c;
  font-weight: 600;
  font-size: 15px;
}

.time {
  color: #999;
  font-size: 12px;
}

.material-summary {
  margin-top: 20px;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f0f2f5;
  border-radius: 8px;
}

.summary-label {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.summary-value {
  font-size: 18px;
  font-weight: 700;
  color: #e6a23c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .material-usage-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .material-info {
    margin-right: 0;
    width: 100%;
  }

  .usage-details {
    text-align: left;
    width: 100%;
    min-width: auto;
  }

  .usage-row {
    justify-content: flex-start;
  }

  .label {
    min-width: 80px;
  }
}

@media (max-width: 480px) {
  .material-usage-item {
    padding: 12px;
  }

  .material-name {
    font-size: 15px;
  }

  .usage-row {
    font-size: 13px;
  }

  .summary-row {
    padding: 10px 12px;
  }

  .summary-label {
    font-size: 15px;
  }

  .summary-value {
    font-size: 16px;
  }
}
</style>
