package com.example.service;

import com.example.entity.*;
import com.example.repository.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TestDataInitializer {

    private static final Logger logger = LoggerFactory.getLogger(TestDataInitializer.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private TechnicianRepository technicianRepository;

    @Autowired
    private VehicleRepository vehicleRepository;

    @Autowired
    private RepairOrderRepository repairOrderRepository;

    @Autowired
    private FaultTypeRepository faultTypeRepository;

    @Autowired
    private MaterialRepository materialRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private TestDataHelper testDataHelper;

    @EventListener(ApplicationReadyEvent.class)
    public void initializeTestData() {
        logger.info("Application ready, starting test data initialization...");

        try {
            // 初始化测试用户
            List<User> testUsers = initializeTestUsers();

            // 初始化测试技师
            List<Technician> testTechnicians = initializeTestTechnicians();

            // 初始化测试车辆
            List<Vehicle> testVehicles = initializeTestVehicles(testUsers);

            // 初始化测试工单
            initializeTestOrders(testUsers, testVehicles, testTechnicians);

            logger.info("Test data initialization completed successfully.");
        } catch (Exception e) {
            logger.error("Error during test data initialization: {}", e.getMessage(), e);
        }
    }

    /**
     * 初始化测试用户
     */
    private List<User> initializeTestUsers() {
        if (userRepository.count() > 1) { // 除了默认管理员
            logger.info("Test users already exist, skipping initialization");
            return userRepository.findAll().stream()
                    .filter(user -> user.getUserType() == User.UserType.USER)
                    .collect(Collectors.toList());
        }

        List<User> users = new ArrayList<>();
        String[][] userData = {
            {"user001", "张三", "13800001001", "<EMAIL>", "北京市朝阳区建国路1号"},
            {"user002", "李四", "13800001002", "<EMAIL>", "上海市浦东新区陆家嘴路2号"},
            {"user003", "王五", "13800001003", "<EMAIL>", "广州市天河区珠江路3号"},
            {"user004", "赵六", "13800001004", "<EMAIL>", "深圳市南山区科技路4号"},
            {"user005", "钱七", "13800001005", "<EMAIL>", "杭州市西湖区文三路5号"},
            {"user006", "孙八", "13800001006", "<EMAIL>", "南京市鼓楼区中山路6号"},
            {"user007", "周九", "13800001007", "<EMAIL>", "武汉市武昌区解放路7号"},
            {"user008", "吴十", "13800001008", "<EMAIL>", "成都市锦江区春熙路8号"},
            {"user009", "郑一", "13800001009", "<EMAIL>", "西安市雁塔区小寨路9号"},
            {"user010", "王二", "13800001010", "<EMAIL>", "重庆市渝中区解放碑10号"}
        };

        for (String[] data : userData) {
            User user = new User();
            user.setUsername(data[0]);
            user.setPassword(passwordEncoder.encode("password123"));
            user.setRealName(data[1]);
            user.setPhone(data[2]);
            user.setEmail(data[3]);
            user.setAddress(data[4]);
            user.setUserType(User.UserType.USER);
            user.setStatus(1);

            users.add(userRepository.save(user));
        }

        logger.info("Created {} test users", users.size());
        return users;
    }

    /**
     * 初始化测试技师
     */
    private List<Technician> initializeTestTechnicians() {
        if (technicianRepository.count() > 0) {
            logger.info("Test technicians already exist, skipping initialization");
            return technicianRepository.findAll();
        }

        List<Technician> technicians = new ArrayList<>();
        Object[][] techData = {
            // 发动机技师
            {"tech001", "张师傅", "13900001001", "<EMAIL>", Technician.Specialty.ENGINE, 60.0, "2020-01-15"},
            {"tech002", "李师傅", "13900001002", "<EMAIL>", Technician.Specialty.ENGINE, 55.0, "2021-03-20"},
            {"tech003", "王师傅", "13900001003", "<EMAIL>", Technician.Specialty.ENGINE, 65.0, "2019-08-10"},

            // 变速箱技师
            {"tech004", "赵师傅", "13900001004", "<EMAIL>", Technician.Specialty.TRANSMISSION, 70.0, "2018-12-05"},
            {"tech005", "钱师傅", "13900001005", "<EMAIL>", Technician.Specialty.TRANSMISSION, 68.0, "2020-06-15"},

            // 制动系统技师
            {"tech006", "孙师傅", "13900001006", "<EMAIL>", Technician.Specialty.BRAKE, 50.0, "2021-09-01"},
            {"tech007", "周师傅", "13900001007", "<EMAIL>", Technician.Specialty.BRAKE, 52.0, "2020-11-20"},

            // 电气系统技师
            {"tech008", "吴师傅", "13900001008", "<EMAIL>", Technician.Specialty.ELECTRICAL, 58.0, "2019-04-12"},
            {"tech009", "郑师傅", "13900001009", "<EMAIL>", Technician.Specialty.ELECTRICAL, 62.0, "2020-02-28"},

            // 空调系统技师
            {"tech010", "冯师傅", "13900001010", "<EMAIL>", Technician.Specialty.HVAC, 48.0, "2021-07-10"},
            {"tech011", "陈师傅", "13900001011", "<EMAIL>", Technician.Specialty.HVAC, 50.0, "2020-12-03"},

            // 底盘技师
            {"tech012", "褚师傅", "13900001012", "<EMAIL>", Technician.Specialty.CHASSIS, 55.0, "2019-10-18"},
            {"tech013", "卫师傅", "13900001013", "<EMAIL>", Technician.Specialty.CHASSIS, 57.0, "2020-05-25"},

            // 车身技师
            {"tech014", "蒋师傅", "13900001014", "<EMAIL>", Technician.Specialty.BODY, 45.0, "2021-01-08"},
            {"tech015", "沈师傅", "13900001015", "<EMAIL>", Technician.Specialty.BODY, 47.0, "2020-08-14"},

            // 轮胎技师
            {"tech016", "韩师傅", "13900001016", "<EMAIL>", Technician.Specialty.TIRE, 40.0, "2021-04-22"},
            {"tech017", "杨师傅", "13900001017", "<EMAIL>", Technician.Specialty.TIRE, 42.0, "2020-10-30"}
        };

        for (Object[] data : techData) {
            Technician technician = new Technician();
            technician.setUsername((String) data[0]);
            technician.setPassword(passwordEncoder.encode("password123"));
            technician.setRealName((String) data[1]);
            technician.setPhone((String) data[2]);
            technician.setEmail((String) data[3]);
            technician.setSpecialty((Technician.Specialty) data[4]);
            technician.setHourlyRate(new BigDecimal((Double) data[5]));
            technician.setHireDate(LocalDate.parse((String) data[6]));
            technician.setStatus(1);
            technician.setWorkload(0);
            technician.setRating(new BigDecimal("4.5")); // 默认评分

            technicians.add(technicianRepository.save(technician));
        }

        logger.info("Created {} test technicians", technicians.size());
        return technicians;
    }

    /**
     * 初始化测试车辆
     */
    private List<Vehicle> initializeTestVehicles(List<User> users) {
        if (vehicleRepository.count() > 0) {
            logger.info("Test vehicles already exist, skipping initialization");
            return vehicleRepository.findAll();
        }

        List<Vehicle> vehicles = new ArrayList<>();
        String[][] vehicleData = {
            // 用户1的车辆
            {"京A123A5", "丰田", "凯美瑞", "2020", "1HGBH41JXMN109001", "白色", "ABC123456", "2020-01-15"},
            {"京B234B6", "本田", "雅阁", "2019", "2HGBH41JXMN109002", "黑色", "DEF234567", "2019-06-20"},

            // 用户2的车辆
            {"沪C345C7", "大众", "帕萨特", "2021", "3HGBH41JXMN109003", "银色", "GHI345678", "2021-03-10"},
            {"沪D456D8", "奥迪", "A4L", "2020", "4HGBH41JXMN109004", "蓝色", "JKL456789", "2020-08-25"},

            // 用户3的车辆
            {"粤E567E9", "宝马", "3系", "2019", "5HGBH41JXMN109005", "红色", "MNO567890", "2019-12-05"},
            {"粤F678F0", "奔驰", "C级", "2021", "6HGBH41JXMN109006", "白色", "PQR678901", "2021-05-18"},

            // 用户4的车辆
            {"粤G789G1", "日产", "天籁", "2020", "7HGBH41JXMN109007", "灰色", "TUV789012", "2020-02-28"},
            {"粤H890H2", "马自达", "阿特兹", "2019", "8HGBH41JXMN109008", "白色", "WXY890123", "2019-09-15"},

            // 用户5的车辆
            {"浙I901I3", "福特", "蒙迪欧", "2021", "9HGBH41JXMN109009", "黑色", "YZA901234", "2021-01-08"},
            {"浙J012J4", "雪佛兰", "迈锐宝", "2020", "AHGBH41JXMN109010", "银色", "BCD012345", "2020-07-12"},

            // 用户6的车辆
            {"苏K123K5", "起亚", "K5", "2019", "BHGBH41JXMN109011", "蓝色", "EFG123456", "2019-11-22"},
            {"苏L234L6", "现代", "索纳塔", "2021", "CHGBH41JXMN109012", "红色", "HIJ234567", "2021-04-30"},

            // 用户7的车辆
            {"鄂M345M7", "别克", "君威", "2020", "DHGBH41JXMN109013", "白色", "KLM345678", "2020-10-14"},
            {"鄂N456N8", "雪铁龙", "C5", "2019", "EHGBH41JXMN109014", "灰色", "NOP456789", "2019-05-07"},

            // 用户8的车辆
            {"川P567P9", "标致", "508", "2021", "FHGBH41JXMN109015", "黑色", "PRT567890", "2021-02-16"},
            {"川R678R0", "斯柯达", "速派", "2020", "GHGBH41JXMN109016", "银色", "TUV678901", "2020-09-03"},

            // 用户9的车辆
            {"陕T789T1", "吉利", "博瑞", "2019", "HHGBH41JXMN109017", "蓝色", "WXY789012", "2019-08-19"},
            {"陕U890U2", "长安", "睿骋", "2021", "JHGBH41JXMN109018", "红色", "ZAB890123", "2021-06-11"},

            // 用户10的车辆
            {"渝V901V3", "奇瑞", "瑞虎", "2020", "KHGBH41JXMN109019", "白色", "CDE901234", "2020-12-24"},
            {"渝W012W4", "比亚迪", "秦", "2019", "LHGBH41JXMN109020", "绿色", "FGH012345", "2019-03-17"}
        };

        for (int i = 0; i < vehicleData.length; i++) {
            String[] data = vehicleData[i];
            User owner = users.get(i / 2); // 每个用户2辆车

            Vehicle vehicle = new Vehicle();
            vehicle.setLicensePlate(data[0]);
            vehicle.setBrand(data[1]);
            vehicle.setModel(data[2]);
            vehicle.setYear(Integer.parseInt(data[3]));
            vehicle.setVin(data[4]);
            vehicle.setColor(data[5]);
            vehicle.setEngineNumber(data[6]);
            vehicle.setRegisterDate(LocalDate.parse(data[7]));
            vehicle.setUser(owner);

            vehicles.add(vehicleRepository.save(vehicle));
        }

        logger.info("Created {} test vehicles", vehicles.size());
        return vehicles;
    }

    /**
     * 初始化测试工单
     */
    @Transactional
    private void initializeTestOrders(List<User> users, List<Vehicle> vehicles, List<Technician> technicians) {
        if (repairOrderRepository.count() > 0) {
            logger.info("Test orders already exist, skipping initialization");
            return;
        }

        List<FaultType> faultTypes = faultTypeRepository.findAll();
        List<Material> materials = materialRepository.findAll();
        Random random = new Random();

        // 创建不同状态的工单
        createCompletedOrders(users, vehicles, technicians, faultTypes, materials, random, 15);
        testDataHelper.createInProgressOrders(users, vehicles, technicians, faultTypes, random, 10);
        // 删除创建待处理订单的逻辑，根据用户要求不允许测试数据创建待处理订单

        logger.info("Test orders initialization completed");
    }

    /**
     * 创建已完成的工单
     */
    private void createCompletedOrders(List<User> users, List<Vehicle> vehicles, List<Technician> technicians,
                                     List<FaultType> faultTypes, List<Material> materials, Random random, int count) {
        for (int i = 0; i < count; i++) {
            User user = users.get(random.nextInt(users.size()));
            Vehicle vehicle = vehicles.stream()
                    .filter(v -> v.getUser().getUserId().equals(user.getUserId()))
                    .findFirst().orElse(vehicles.get(0));
            FaultType faultType = faultTypes.get(random.nextInt(faultTypes.size()));

            RepairOrder order = new RepairOrder();
            order.setDescription(testDataHelper.generateOrderDescription(faultType));
            order.setUrgencyLevel(RepairOrder.UrgencyLevel.values()[random.nextInt(4)]);
            order.setUser(user);
            order.setVehicle(vehicle);
            order.setFaultType(faultType);
            order.setContactPhone(user.getPhone());
            order.setStatus(RepairOrder.OrderStatus.COMPLETED);
            order.setPaymentStatus(RepairOrder.PaymentStatus.PAID);

            // 设置时间
            LocalDateTime submitTime = LocalDateTime.now().minusDays(random.nextInt(30) + 1);
            order.setSubmitTime(submitTime);
            order.setEstimatedCompletionTime(submitTime.plusHours(faultType.getEstimatedHours()));
            order.setActualCompletionTime(submitTime.plusHours(faultType.getEstimatedHours() + random.nextInt(4)));

            // 设置工作结果和工时
            order.setWorkResult(testDataHelper.generateWorkResult(faultType));
            BigDecimal workingHours = new BigDecimal(faultType.getEstimatedHours() + random.nextDouble() * 2);
            order.setWorkingHours(workingHours);

            RepairOrder savedOrder = repairOrderRepository.save(order);

            // 分配技师
            testDataHelper.assignTechniciansToOrder(savedOrder, technicians, faultType);

            // 添加材料使用记录
            testDataHelper.addMaterialUsage(savedOrder, materials, random);

            // 计算费用
            testDataHelper.calculateOrderCosts(savedOrder, technicians);

            // 添加反馈
            testDataHelper.addOrderFeedback(savedOrder, random);

            // 添加状态历史
            testDataHelper.addOrderStatusHistory(savedOrder);

            // 为已完成的订单创建工时费记录
            if (savedOrder.getStatus() == RepairOrder.OrderStatus.COMPLETED) {
                testDataHelper.createLaborPayments(savedOrder);
            }
        }
    }
}
