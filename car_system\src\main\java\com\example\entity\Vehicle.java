package com.example.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "vehicles")
public class Vehicle extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "vehicle_id")
    private Long vehicleId;

    @NotBlank(message = "车牌号不能为空")
    @Pattern(regexp = "^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4}[A-Z0-9挂学警港澳]$",
             message = "车牌号格式不正确")
    @Column(name = "license_plate", unique = true, nullable = false, length = 10)
    private String licensePlate;

    @NotBlank(message = "品牌不能为空")
    @Size(max = 50, message = "品牌长度不能超过50个字符")
    @Column(name = "brand", nullable = false, length = 50)
    private String brand;

    @NotBlank(message = "型号不能为空")
    @Size(max = 50, message = "型号长度不能超过50个字符")
    @Column(name = "model", nullable = false, length = 50)
    private String model;

    @NotNull(message = "年份不能为空")
    @Min(value = 1900, message = "年份不能早于1900年")
    @Max(value = 2030, message = "年份不能晚于2030年")
    @Column(name = "vehicle_year", nullable = false)
    private Integer year;

    @NotBlank(message = "VIN码不能为空")
    @Pattern(regexp = "^[A-HJ-NPR-Z0-9]{17}$", message = "VIN码格式不正确，必须为17位字符")
    @Column(name = "vin", unique = true, nullable = false, length = 17)
    private String vin;

    @Size(max = 20, message = "颜色长度不能超过20个字符")
    @Column(name = "color", length = 20)
    private String color;

    @Size(max = 50, message = "发动机号长度不能超过50个字符")
    @Column(name = "engine_number", length = 50)
    private String engineNumber;

    @Column(name = "register_date")
    private LocalDate registerDate;

    // 多对一关系：车辆属于一个用户
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    // 一对多关系：车辆有多个维修工单
    @OneToMany(mappedBy = "vehicle", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<RepairOrder> repairOrders = new ArrayList<>();

    // 构造函数
    public Vehicle() {}

    public Vehicle(String licensePlate, String brand, String model, Integer year, String vin, User user) {
        this.licensePlate = licensePlate;
        this.brand = brand;
        this.model = model;
        this.year = year;
        this.vin = vin;
        this.user = user;
    }

    // Getters and Setters
    public Long getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(Long vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getLicensePlate() {
        return licensePlate;
    }

    public void setLicensePlate(String licensePlate) {
        this.licensePlate = licensePlate;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getEngineNumber() {
        return engineNumber;
    }

    public void setEngineNumber(String engineNumber) {
        this.engineNumber = engineNumber;
    }

    public LocalDate getRegisterDate() {
        return registerDate;
    }

    public void setRegisterDate(LocalDate registerDate) {
        this.registerDate = registerDate;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public List<RepairOrder> getRepairOrders() {
        return repairOrders;
    }

    public void setRepairOrders(List<RepairOrder> repairOrders) {
        this.repairOrders = repairOrders;
    }
}
