package com.example.dto.request;

import jakarta.validation.constraints.*;
import java.util.List;

/**
 * 故障类型请求DTO
 */
public class FaultTypeRequest {
    
    @NotBlank(message = "故障类型名称不能为空")
    @Size(max = 100, message = "故障类型名称长度不能超过100个字符")
    private String typeName;
    
    @NotBlank(message = "故障描述不能为空")
    @Size(max = 500, message = "故障描述长度不能超过500个字符")
    private String description;
    
    @NotEmpty(message = "所需工种不能为空")
    private List<String> requiredSpecialties;
    
    @NotNull(message = "所需技师数量不能为空")
    @Min(value = 1, message = "所需技师数量至少为1")
    @Max(value = 10, message = "所需技师数量不能超过10")
    private Integer requiredTechCount;
    
    @NotNull(message = "预估工时不能为空")
    @DecimalMin(value = "0.5", message = "预估工时至少为0.5小时")
    @DecimalMax(value = "100.0", message = "预估工时不能超过100小时")
    private Double estimatedHours;
    
    // 构造函数
    public FaultTypeRequest() {}
    
    public FaultTypeRequest(String typeName, String description, List<String> requiredSpecialties, 
                           Integer requiredTechCount, Double estimatedHours) {
        this.typeName = typeName;
        this.description = description;
        this.requiredSpecialties = requiredSpecialties;
        this.requiredTechCount = requiredTechCount;
        this.estimatedHours = estimatedHours;
    }
    
    // Getters and Setters
    public String getTypeName() {
        return typeName;
    }
    
    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public List<String> getRequiredSpecialties() {
        return requiredSpecialties;
    }
    
    public void setRequiredSpecialties(List<String> requiredSpecialties) {
        this.requiredSpecialties = requiredSpecialties;
    }
    
    public Integer getRequiredTechCount() {
        return requiredTechCount;
    }
    
    public void setRequiredTechCount(Integer requiredTechCount) {
        this.requiredTechCount = requiredTechCount;
    }
    
    public Double getEstimatedHours() {
        return estimatedHours;
    }
    
    public void setEstimatedHours(Double estimatedHours) {
        this.estimatedHours = estimatedHours;
    }
}
