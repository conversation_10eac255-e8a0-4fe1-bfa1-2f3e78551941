package com.example.dto.request;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 工单完成请求DTO
 */
public class OrderCompletionRequest {

    @NotNull(message = "工作时长不能为空")
    @DecimalMin(value = "0.0", message = "工作时长不能为负数")
    private BigDecimal workingHours;

    @NotBlank(message = "工作结果不能为空")
    @Size(max = 1000, message = "工作结果长度不能超过1000个字符")
    private String workResult;

    private List<MaterialUsageRequest> materialsUsed;

    public OrderCompletionRequest() {}

    public OrderCompletionRequest(BigDecimal workingHours, String workResult, List<MaterialUsageRequest> materialsUsed) {
        this.workingHours = workingHours;
        this.workResult = workResult;
        this.materialsUsed = materialsUsed;
    }

    public BigDecimal getWorkingHours() {
        return workingHours;
    }

    public void setWorkingHours(BigDecimal workingHours) {
        this.workingHours = workingHours;
    }

    public String getWorkResult() {
        return workResult;
    }

    public void setWorkResult(String workResult) {
        this.workResult = workResult;
    }

    public List<MaterialUsageRequest> getMaterialsUsed() {
        return materialsUsed;
    }

    public void setMaterialsUsed(List<MaterialUsageRequest> materialsUsed) {
        this.materialsUsed = materialsUsed;
    }

    /**
     * 材料使用请求内部类
     */
    public static class MaterialUsageRequest {

        @NotNull(message = "材料ID不能为空")
        private Long materialId;

        @NotNull(message = "使用数量不能为空")
        @DecimalMin(value = "0.0", message = "使用数量不能为负数")
        private BigDecimal quantity;

        @NotNull(message = "总价不能为空")
        @DecimalMin(value = "0.0", message = "总价不能为负数")
        private BigDecimal totalPrice;

        public MaterialUsageRequest() {}

        public MaterialUsageRequest(Long materialId, BigDecimal quantity, BigDecimal totalPrice) {
            this.materialId = materialId;
            this.quantity = quantity;
            this.totalPrice = totalPrice;
        }

        public Long getMaterialId() {
            return materialId;
        }

        public void setMaterialId(Long materialId) {
            this.materialId = materialId;
        }

        public BigDecimal getQuantity() {
            return quantity;
        }

        public void setQuantity(BigDecimal quantity) {
            this.quantity = quantity;
        }

        public BigDecimal getTotalPrice() {
            return totalPrice;
        }

        public void setTotalPrice(BigDecimal totalPrice) {
            this.totalPrice = totalPrice;
        }
    }
}
