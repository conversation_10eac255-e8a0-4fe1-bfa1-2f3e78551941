package com.example.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;

import java.time.LocalDateTime;

/**
 * 工单催单实体类
 */
@Entity
@Table(name = "order_urgent_requests")
public class OrderUrgentRequest extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "urgent_id")
    private Long urgentId;

    @NotBlank(message = "催单原因不能为空")
    @Size(max = 500, message = "催单原因长度不能超过500个字符")
    @Column(name = "reason", nullable = false, length = 500)
    private String reason;

    @NotNull(message = "催单时间不能为空")
    @Column(name = "urgent_time", nullable = false)
    private LocalDateTime urgentTime;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private UrgentStatus status = UrgentStatus.PENDING;

    // 多对一关系：催单属于一个工单
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id", nullable = false)
    private RepairOrder repairOrder;

    // 枚举：催单状态
    public enum UrgentStatus {
        PENDING("待处理"),
        PROCESSED("已处理");

        private final String displayName;

        UrgentStatus(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    // 构造函数
    public OrderUrgentRequest() {}

    public OrderUrgentRequest(String reason, RepairOrder repairOrder) {
        this.reason = reason;
        this.repairOrder = repairOrder;
        this.urgentTime = LocalDateTime.now();
        this.status = UrgentStatus.PENDING;
    }

    // Getters and Setters
    public Long getUrgentId() {
        return urgentId;
    }

    public void setUrgentId(Long urgentId) {
        this.urgentId = urgentId;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public LocalDateTime getUrgentTime() {
        return urgentTime;
    }

    public void setUrgentTime(LocalDateTime urgentTime) {
        this.urgentTime = urgentTime;
    }

    public UrgentStatus getStatus() {
        return status;
    }

    public void setStatus(UrgentStatus status) {
        this.status = status;
    }

    public RepairOrder getRepairOrder() {
        return repairOrder;
    }

    public void setRepairOrder(RepairOrder repairOrder) {
        this.repairOrder = repairOrder;
    }
}
