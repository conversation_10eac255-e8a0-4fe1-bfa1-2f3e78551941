package com.example.repository;

import com.example.entity.OrderTechnicianAssignment;
import com.example.entity.RepairOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 工单技师分配Repository
 */
@Repository
public interface OrderTechnicianAssignmentRepository extends JpaRepository<OrderTechnicianAssignment, Long> {
    
    /**
     * 根据工单ID查找所有技师分配记录
     */
    List<OrderTechnicianAssignment> findByRepairOrderOrderId(Long orderId);

    /**
     * 根据工单ID查找所有技师分配记录（预加载技师信息）
     */
    @Query("SELECT ota FROM OrderTechnicianAssignment ota JOIN FETCH ota.technician WHERE ota.repairOrder.orderId = :orderId")
    List<OrderTechnicianAssignment> findByRepairOrderOrderIdWithTechnician(@Param("orderId") Long orderId);
    
    /**
     * 根据技师ID查找所有分配记录
     */
    List<OrderTechnicianAssignment> findByTechnicianTechnicianId(Long technicianId);
    
    /**
     * 根据工单ID和技师ID查找分配记录
     */
    Optional<OrderTechnicianAssignment> findByRepairOrderOrderIdAndTechnicianTechnicianId(Long orderId, Long technicianId);
    
    /**
     * 根据工单ID和同意状态查找分配记录
     */
    List<OrderTechnicianAssignment> findByRepairOrderOrderIdAndAgreementStatus(Long orderId, OrderTechnicianAssignment.AgreementStatus status);
    
    /**
     * 根据技师ID和同意状态查找分配记录
     */
    List<OrderTechnicianAssignment> findByTechnicianTechnicianIdAndAgreementStatus(Long technicianId, OrderTechnicianAssignment.AgreementStatus status);
    
    /**
     * 统计工单中已同意的技师数量
     */
    @Query("SELECT COUNT(ota) FROM OrderTechnicianAssignment ota WHERE ota.repairOrder.orderId = :orderId AND ota.agreementStatus = 'ACCEPTED'")
    long countAcceptedByOrderId(@Param("orderId") Long orderId);
    
    /**
     * 统计工单中总的技师分配数量
     */
    long countByRepairOrderOrderId(Long orderId);
    
    /**
     * 检查工单是否所有技师都已同意
     */
    @Query("SELECT CASE WHEN COUNT(ota) = COUNT(CASE WHEN ota.agreementStatus = 'ACCEPTED' THEN 1 END) THEN true ELSE false END " +
           "FROM OrderTechnicianAssignment ota WHERE ota.repairOrder.orderId = :orderId")
    boolean areAllTechniciansAccepted(@Param("orderId") Long orderId);
    
    /**
     * 根据工单状态和技师ID查找分配记录
     */
    @Query("SELECT ota FROM OrderTechnicianAssignment ota WHERE ota.technician.technicianId = :technicianId AND ota.repairOrder.status = :status")
    List<OrderTechnicianAssignment> findByTechnicianIdAndOrderStatus(@Param("technicianId") Long technicianId, @Param("status") RepairOrder.OrderStatus status);
    
    /**
     * 删除工单的所有技师分配记录
     */
    void deleteByRepairOrderOrderId(Long orderId);
    
    /**
     * 删除特定技师的分配记录
     */
    void deleteByRepairOrderOrderIdAndTechnicianTechnicianId(Long orderId, Long technicianId);
}
