package com.example.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "materials")
public class Material extends BaseEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "material_id")
    private Long materialId;
    
    @NotBlank(message = "材料名称不能为空")
    @Size(max = 100, message = "材料名称长度不能超过100个字符")
    @Column(name = "material_name", nullable = false, length = 100)
    private String materialName;
    
    @Size(max = 100, message = "规格长度不能超过100个字符")
    @Column(name = "specification", length = 100)
    private String specification;
    
    @NotBlank(message = "单位不能为空")
    @Size(max = 20, message = "单位长度不能超过20个字符")
    @Column(name = "unit", nullable = false, length = 20)
    private String unit;
    
    @NotNull(message = "单价不能为空")
    @DecimalMin(value = "0.0", inclusive = false, message = "单价必须大于0")
    @Column(name = "unit_price", nullable = false, precision = 10, scale = 2)
    private BigDecimal unitPrice;
    
    @NotNull(message = "库存不能为空")
    @Min(value = 0, message = "库存不能为负数")
    @Column(name = "inventory", nullable = false)
    private Integer inventory;
    
    @Size(max = 50, message = "分类长度不能超过50个字符")
    @Column(name = "category", length = 50)
    private String category;
    
    @Column(name = "status", nullable = false)
    private Integer status = 1; // 1: 正常, 0: 停用
    
    // 一对多关系：材料的使用记录
    @OneToMany(mappedBy = "material", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<OrderMaterialUsage> usageRecords = new ArrayList<>();
    
    // 构造函数
    public Material() {}
    
    public Material(String materialName, String specification, String unit, BigDecimal unitPrice, Integer inventory) {
        this.materialName = materialName;
        this.specification = specification;
        this.unit = unit;
        this.unitPrice = unitPrice;
        this.inventory = inventory;
    }
    
    // Getters and Setters
    public Long getMaterialId() {
        return materialId;
    }
    
    public void setMaterialId(Long materialId) {
        this.materialId = materialId;
    }
    
    public String getMaterialName() {
        return materialName;
    }
    
    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }
    
    public String getSpecification() {
        return specification;
    }
    
    public void setSpecification(String specification) {
        this.specification = specification;
    }
    
    public String getUnit() {
        return unit;
    }
    
    public void setUnit(String unit) {
        this.unit = unit;
    }
    
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }
    
    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }
    
    public Integer getInventory() {
        return inventory;
    }
    
    public void setInventory(Integer inventory) {
        this.inventory = inventory;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public List<OrderMaterialUsage> getUsageRecords() {
        return usageRecords;
    }
    
    public void setUsageRecords(List<OrderMaterialUsage> usageRecords) {
        this.usageRecords = usageRecords;
    }
}
