package com.example.service;

import com.example.dto.request.LoginRequest;
import com.example.dto.response.LoginResponse;
import com.example.entity.Technician;
import com.example.entity.User;
import com.example.exception.BusinessException;
import com.example.repository.TechnicianRepository;
import com.example.repository.UserRepository;
import com.example.security.JwtUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AuthServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private TechnicianRepository technicianRepository;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private JwtUtil jwtUtil;

    @InjectMocks
    private AuthService authService;

    private User testUser;
    private Technician testTechnician;
    private LoginRequest loginRequest;

    @BeforeEach
    void setUp() {
        // 创建测试用户
        testUser = new User();
        testUser.setUserId(1L);
        testUser.setUsername("testuser");
        testUser.setPassword("encodedPassword");
        testUser.setRealName("Test User");
        testUser.setUserType(User.UserType.USER);
        testUser.setStatus(1);

        // 创建测试技师
        testTechnician = new Technician();
        testTechnician.setTechnicianId(1L);
        testTechnician.setUsername("testtechnician");
        testTechnician.setPassword("encodedPassword");
        testTechnician.setRealName("Test Technician");
        testTechnician.setStatus(1);

        // 创建登录请求
        loginRequest = new LoginRequest();
        loginRequest.setUsername("testuser");
        loginRequest.setPassword("password");
        loginRequest.setUserType("user");
    }

    @Test
    void testLoginUser_Success() {
        // Given
        when(userRepository.findByUsernameAndUserType(anyString(), any(User.UserType.class)))
                .thenReturn(Optional.of(testUser));
        when(passwordEncoder.matches(anyString(), anyString())).thenReturn(true);
        when(jwtUtil.generateToken(anyString(), anyString(), any(Long.class))).thenReturn("test-token");
        when(jwtUtil.getExpirationTime()).thenReturn(3600000L); // 1小时的毫秒数

        // When
        LoginResponse response = authService.login(loginRequest);

        // Then
        assertNotNull(response);
        assertEquals("test-token", response.getToken());
        assertEquals(testUser.getUserId(), response.getUser().getUserId());
        assertEquals(testUser.getUsername(), response.getUser().getUsername());
        assertEquals("user", response.getUser().getUserType());

        verify(userRepository).findByUsernameAndUserType("testuser", User.UserType.USER);
        verify(passwordEncoder).matches("password", "encodedPassword");
        verify(jwtUtil).generateToken("testuser", "USER", 1L);
    }

    @Test
    void testLoginUser_UserNotFound() {
        // Given
        when(userRepository.findByUsernameAndUserType(anyString(), any(User.UserType.class)))
                .thenReturn(Optional.empty());

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> authService.login(loginRequest));
        assertEquals("用户名或密码错误", exception.getMessage());
    }

    @Test
    void testLoginUser_UserDisabled() {
        // Given
        testUser.setStatus(0);
        when(userRepository.findByUsernameAndUserType(anyString(), any(User.UserType.class)))
                .thenReturn(Optional.of(testUser));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> authService.login(loginRequest));
        assertEquals("账户已被禁用，请联系管理员", exception.getMessage());
    }

    @Test
    void testLoginUser_WrongPassword() {
        // Given
        when(userRepository.findByUsernameAndUserType(anyString(), any(User.UserType.class)))
                .thenReturn(Optional.of(testUser));
        when(passwordEncoder.matches(anyString(), anyString())).thenReturn(false);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> authService.login(loginRequest));
        assertEquals("用户名或密码错误", exception.getMessage());
    }

    @Test
    void testLoginTechnician_Success() {
        // Given
        loginRequest.setUserType("technician");
        loginRequest.setUsername("testtechnician");

        when(technicianRepository.findByUsername(anyString()))
                .thenReturn(Optional.of(testTechnician));
        when(passwordEncoder.matches(anyString(), anyString())).thenReturn(true);
        when(jwtUtil.generateToken(anyString(), anyString(), any(Long.class))).thenReturn("test-token");
        when(jwtUtil.getExpirationTime()).thenReturn(3600000L); // 1小时的毫秒数

        // When
        LoginResponse response = authService.login(loginRequest);

        // Then
        assertNotNull(response);
        assertEquals("test-token", response.getToken());
        assertEquals(testTechnician.getTechnicianId(), response.getUser().getUserId());
        assertEquals(testTechnician.getUsername(), response.getUser().getUsername());
        assertEquals("technician", response.getUser().getUserType());

        verify(technicianRepository).findByUsername("testtechnician");
        verify(passwordEncoder).matches("password", "encodedPassword");
        verify(jwtUtil).generateToken("testtechnician", "TECHNICIAN", 1L);
    }

    @Test
    void testLoginAdmin_Success() {
        // Given
        testUser.setUserType(User.UserType.ADMIN);
        loginRequest.setUserType("admin");

        when(userRepository.findByUsernameAndUserType(anyString(), any(User.UserType.class)))
                .thenReturn(Optional.of(testUser));
        when(passwordEncoder.matches(anyString(), anyString())).thenReturn(true);
        when(jwtUtil.generateToken(anyString(), anyString(), any(Long.class))).thenReturn("test-token");
        when(jwtUtil.getExpirationTime()).thenReturn(3600000L); // 1小时的毫秒数

        // When
        LoginResponse response = authService.login(loginRequest);

        // Then
        assertNotNull(response);
        assertEquals("test-token", response.getToken());
        assertEquals("admin", response.getUser().getUserType());

        verify(userRepository).findByUsernameAndUserType("testuser", User.UserType.ADMIN);
    }

    @Test
    void testLogin_UnsupportedUserType() {
        // Given
        loginRequest.setUserType("invalid");

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> authService.login(loginRequest));
        assertEquals("不支持的用户类型", exception.getMessage());
    }

    @Test
    void testLogout_ValidToken() {
        // Given
        String token = "valid-token";
        when(jwtUtil.isTokenValid(token)).thenReturn(true);

        // When & Then
        assertDoesNotThrow(() -> authService.logout(token));
        verify(jwtUtil).isTokenValid(token);
    }

    @Test
    void testLogout_InvalidToken() {
        // Given
        String token = "invalid-token";
        when(jwtUtil.isTokenValid(token)).thenReturn(false);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> authService.logout(token));
        assertEquals("无效的token", exception.getMessage());
    }

    @Test
    void testRefreshToken_Success() {
        // Given
        String refreshToken = "refresh-token";
        String newToken = "new-token";

        when(jwtUtil.refreshAccessToken(refreshToken)).thenReturn(newToken);
        when(jwtUtil.getUsernameFromToken(newToken)).thenReturn("testuser");
        when(jwtUtil.getUserTypeFromToken(newToken)).thenReturn("USER");
        when(jwtUtil.getUserIdFromToken(newToken)).thenReturn(1L);
        when(jwtUtil.getExpirationTime()).thenReturn(3600000L); // 1小时的毫秒数

        // When
        LoginResponse response = authService.refreshToken(refreshToken);

        // Then
        assertNotNull(response);
        assertEquals(newToken, response.getToken());
        assertEquals(1L, response.getUser().getUserId());
        assertEquals("testuser", response.getUser().getUsername());
        assertEquals("user", response.getUser().getUserType());
    }

    @Test
    void testRefreshToken_Failure() {
        // Given
        String refreshToken = "invalid-refresh-token";
        when(jwtUtil.refreshAccessToken(refreshToken)).thenThrow(new RuntimeException("Token expired"));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> authService.refreshToken(refreshToken));
        assertTrue(exception.getMessage().contains("刷新token失败"));
    }
}
