<template>
  <BaseLayout :menu-items="menuItems" />
</template>

<script setup>
import {
  House, User, List, Money, Clock
} from '@element-plus/icons-vue'
import BaseLayout from './BaseLayout.vue'

// 技师端菜单配置
const menuItems = [
  {
    path: '/technician/dashboard',
    title: '工作台',
    icon: House
  },
  {
    path: '/technician/profile',
    title: '个人资料',
    icon: User
  },
  {
    path: '/technician/tasks',
    title: '我的任务',
    icon: List
  },
  {
    path: '/technician/payments',
    title: '收入统计',
    icon: Money
  },
  {
    path: '/technician/history',
    title: '工作历史',
    icon: Clock
  }
]
</script>
