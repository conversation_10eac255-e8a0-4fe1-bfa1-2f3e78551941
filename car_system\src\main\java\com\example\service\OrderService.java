package com.example.service;

import com.example.dto.request.OrderRequest;
import com.example.dto.request.OrderCompletionRequest;
import com.example.dto.request.OrderFeedbackRequest;
import com.example.dto.request.OrderStatusUpdateRequest;
import com.example.dto.request.MaterialUsageRequest;
import com.example.dto.response.OrderDTO;
import com.example.dto.response.MaterialUsageDTO;
import com.example.dto.response.UrgentRequestDTO;
import com.example.entity.*;
import com.example.exception.BusinessException;
import com.example.exception.ResourceNotFoundException;
import com.example.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 工单服务
 */
@Service
@Transactional
public class OrderService {

    @Autowired
    private RepairOrderRepository repairOrderRepository;

    @Autowired
    private VehicleRepository vehicleRepository;

    @Autowired
    private FaultTypeRepository faultTypeRepository;

    @Autowired
    private TechnicianRepository technicianRepository;

    @Autowired
    private OrderStatusHistoryRepository orderStatusHistoryRepository;

    @Autowired
    private OrderFeedbackRepository orderFeedbackRepository;

    @Autowired
    private OrderMaterialUsageRepository orderMaterialUsageRepository;

    @Autowired
    private MaterialRepository materialRepository;

    @Autowired
    private OrderTechnicianAssignmentRepository orderTechnicianAssignmentRepository;

    @Autowired
    private LaborPaymentRepository laborPaymentRepository;

    @Autowired
    private OrderUrgentRequestRepository orderUrgentRequestRepository;

    /**
     * 提交维修工单
     */
    public OrderDTO submitOrder(Long userId, OrderRequest request) {
        // 验证车辆是否属于当前用户
        Vehicle vehicle = vehicleRepository.findById(request.getVehicleId())
                .orElseThrow(() -> new ResourceNotFoundException("车辆不存在"));

        if (!vehicle.getUser().getUserId().equals(userId)) {
            throw new BusinessException("无权操作此车辆");
        }

        // 验证故障类型
        FaultType faultType = faultTypeRepository.findById(request.getFaultTypeId())
                .orElseThrow(() -> new ResourceNotFoundException("故障类型不存在"));

        if (faultType.getStatus() != 1) {
            throw new BusinessException("故障类型已停用");
        }

        // 创建工单
        RepairOrder order = new RepairOrder();
        order.setUser(vehicle.getUser());
        order.setVehicle(vehicle);
        order.setFaultType(faultType);
        order.setDescription(request.getDescription());
        order.setUrgencyLevel(RepairOrder.UrgencyLevel.valueOf(request.getUrgencyLevel().toUpperCase()));
        order.setPreferredTime(request.getPreferredTime());
        order.setContactPhone(request.getContactPhone());
        order.setSubmitTime(LocalDateTime.now());
        order.setStatus(RepairOrder.OrderStatus.PENDING);

        // 计算预估完成时间
        LocalDateTime estimatedTime = calculateEstimatedCompletionTime(order);
        order.setEstimatedCompletionTime(estimatedTime);

        RepairOrder savedOrder = repairOrderRepository.save(order);

        // 记录状态历史
        recordStatusHistory(savedOrder, null, RepairOrder.OrderStatus.PENDING, "工单已提交");

        // 自动分配技师
        assignTechnicians(savedOrder);

        return convertToDTO(savedOrder);
    }

    /**
     * 获取工单详情
     */
    @Transactional(readOnly = true)
    public OrderDTO getOrder(Long orderId, Long userId, String userType) {
        RepairOrder order = repairOrderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("工单不存在"));

        // 权限检查
        if ("USER".equals(userType) && !order.getUser().getUserId().equals(userId)) {
            throw new BusinessException("无权查看此工单");
        } else if ("TECHNICIAN".equals(userType)) {
            boolean isAssigned = order.getTechnicianAssignments().stream()
                    .anyMatch(assignment -> assignment.getTechnician().getTechnicianId().equals(userId));
            if (!isAssigned) {
                throw new BusinessException("无权查看此工单");
            }
        }

        return convertToDTO(order, userType);
    }

    /**
     * 技师接受工单
     */
    public void acceptOrder(Long orderId, Long technicianId) {
        RepairOrder order = repairOrderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("工单不存在"));

        Technician technician = technicianRepository.findById(technicianId)
                .orElseThrow(() -> new ResourceNotFoundException("技师不存在"));

        // 查找技师分配记录
        OrderTechnicianAssignment assignment = orderTechnicianAssignmentRepository
                .findByRepairOrderOrderIdAndTechnicianTechnicianId(orderId, technicianId)
                .orElseThrow(() -> new BusinessException("您未被分配到此工单"));

        if (order.getStatus() != RepairOrder.OrderStatus.ASSIGNED) {
            throw new BusinessException("工单状态不允许接受");
        }

        if (assignment.getAgreementStatus() != OrderTechnicianAssignment.AgreementStatus.PENDING) {
            throw new BusinessException("您已经对此工单做出过响应");
        }

        // 更新技师同意状态
        assignment.setAgreementStatus(OrderTechnicianAssignment.AgreementStatus.ACCEPTED);
        assignment.setResponseTime(LocalDateTime.now());
        orderTechnicianAssignmentRepository.save(assignment);

        // 检查是否所有技师都已同意
        boolean allAccepted = orderTechnicianAssignmentRepository.areAllTechniciansAccepted(orderId);

        if (allAccepted) {
            // 所有技师都同意，更新工单状态为已接受
            order.setStatus(RepairOrder.OrderStatus.ACCEPTED);
            repairOrderRepository.save(order);

            // 记录状态历史
            recordStatusHistory(order, RepairOrder.OrderStatus.ASSIGNED, RepairOrder.OrderStatus.ACCEPTED,
                    "所有技师已同意，工单状态更新为已接受");
        } else {
            // 记录技师同意历史
            recordStatusHistory(order, RepairOrder.OrderStatus.ASSIGNED, RepairOrder.OrderStatus.ASSIGNED,
                    "技师 " + technician.getRealName() + " 已同意工单，等待其他技师确认");
        }
    }

    /**
     * 技师拒绝工单
     */
    public void rejectOrder(Long orderId, Long technicianId, String reason) {
        RepairOrder order = repairOrderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("工单不存在"));

        Technician technician = technicianRepository.findById(technicianId)
                .orElseThrow(() -> new ResourceNotFoundException("技师不存在"));

        // 查找技师分配记录
        OrderTechnicianAssignment assignment = orderTechnicianAssignmentRepository
                .findByRepairOrderOrderIdAndTechnicianTechnicianId(orderId, technicianId)
                .orElseThrow(() -> new BusinessException("您未被分配到此工单"));

        if (order.getStatus() != RepairOrder.OrderStatus.ASSIGNED) {
            throw new BusinessException("工单状态不允许拒绝");
        }

        if (assignment.getAgreementStatus() != OrderTechnicianAssignment.AgreementStatus.PENDING) {
            throw new BusinessException("您已经对此工单做出过响应");
        }

        // 更新技师拒绝状态
        assignment.setAgreementStatus(OrderTechnicianAssignment.AgreementStatus.REJECTED);
        assignment.setResponseTime(LocalDateTime.now());
        assignment.setRejectReason(reason);
        orderTechnicianAssignmentRepository.save(assignment);

        // 减少技师工作负载
        technician.setWorkload(technician.getWorkload() - 1);
        technicianRepository.save(technician);

        // 记录状态历史
        recordStatusHistory(order, RepairOrder.OrderStatus.ASSIGNED, RepairOrder.OrderStatus.ASSIGNED,
                "技师 " + technician.getRealName() + " 拒绝工单，原因：" + reason);

        // 重新分配技师
        reassignTechnician(order, technician.getSpecialty());
    }

    /**
     * 开始维修工作
     */
    public void startWork(Long orderId, Long technicianId) {
        RepairOrder order = repairOrderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("工单不存在"));

        Technician technician = technicianRepository.findById(technicianId)
                .orElseThrow(() -> new ResourceNotFoundException("技师不存在"));

        // 检查技师是否被分配到此工单且已同意
        OrderTechnicianAssignment assignment = orderTechnicianAssignmentRepository
                .findByRepairOrderOrderIdAndTechnicianTechnicianId(orderId, technicianId)
                .orElseThrow(() -> new BusinessException("您未被分配到此工单"));

        if (assignment.getAgreementStatus() != OrderTechnicianAssignment.AgreementStatus.ACCEPTED) {
            throw new BusinessException("您尚未同意此工单");
        }

        if (order.getStatus() != RepairOrder.OrderStatus.ACCEPTED) {
            throw new BusinessException("工单状态不允许开始工作");
        }

        // 更新工单状态
        order.setStatus(RepairOrder.OrderStatus.IN_PROGRESS);
        repairOrderRepository.save(order);

        // 记录状态历史
        recordStatusHistory(order, RepairOrder.OrderStatus.ACCEPTED, RepairOrder.OrderStatus.IN_PROGRESS,
                "技师 " + technician.getRealName() + " 开始维修工作");
    }

    /**
     * 完成维修工作
     */
    public void completeWork(Long orderId, Long technicianId, OrderCompletionRequest request) {
        RepairOrder order = repairOrderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("工单不存在"));

        Technician technician = technicianRepository.findById(technicianId)
                .orElseThrow(() -> new ResourceNotFoundException("技师不存在"));

        // 检查技师是否被分配到此工单且已同意
        OrderTechnicianAssignment assignment = orderTechnicianAssignmentRepository
                .findByRepairOrderOrderIdAndTechnicianTechnicianId(orderId, technicianId)
                .orElseThrow(() -> new BusinessException("您未被分配到此工单"));

        if (assignment.getAgreementStatus() != OrderTechnicianAssignment.AgreementStatus.ACCEPTED) {
            throw new BusinessException("您尚未同意此工单");
        }

        if (order.getStatus() != RepairOrder.OrderStatus.IN_PROGRESS) {
            throw new BusinessException("工单状态不允许完成");
        }

        // 更新工单信息
        order.setWorkingHours(request.getWorkingHours());
        order.setWorkResult(request.getWorkResult());
        order.setActualCompletionTime(LocalDateTime.now());
        order.setStatus(RepairOrder.OrderStatus.COMPLETED);
        // 订单完成时默认已支付
        order.setPaymentStatus(RepairOrder.PaymentStatus.PAID);

        // 计算工时费（所有分配技师的工时费总和）
        BigDecimal laborCost = BigDecimal.ZERO;
        for (Technician assignedTech : order.getAssignedTechnicians()) {
            laborCost = laborCost.add(request.getWorkingHours().multiply(assignedTech.getHourlyRate()));
        }
        order.setTotalLaborCost(laborCost);

        // 处理材料使用记录
        if (request.getMaterialsUsed() != null && !request.getMaterialsUsed().isEmpty()) {
            processMaterialUsage(order, request.getMaterialsUsed());
        }

        // 计算总费用
        order.setTotalCost(order.getTotalLaborCost().add(order.getTotalMaterialCost()));

        repairOrderRepository.save(order);

        // 为所有参与的技师创建或更新工时费记录
        createOrUpdateLaborPayments(order);

        // 记录状态历史
        recordStatusHistory(order, RepairOrder.OrderStatus.IN_PROGRESS, RepairOrder.OrderStatus.COMPLETED,
                "技师 " + technician.getRealName() + " 完成维修工作");
    }

    /**
     * 计算预估完成时间
     */
    private LocalDateTime calculateEstimatedCompletionTime(RepairOrder order) {
        LocalDateTime baseTime = order.getPreferredTime() != null ?
                order.getPreferredTime() : LocalDateTime.now().plusDays(1);

        // 根据紧急程度调整时间
        switch (order.getUrgencyLevel()) {
            case URGENT:
                return baseTime.plusHours(2);
            case HIGH:
                return baseTime.plusHours(6);
            case NORMAL:
                return baseTime.plusDays(1);
            case LOW:
                return baseTime.plusDays(3);
            default:
                return baseTime.plusDays(1);
        }
    }

    /**
     * 自动分配技师
     */
    private void assignTechnicians(RepairOrder order) {
        FaultType faultType = order.getFaultType();
        List<Technician> assignedTechnicians = new ArrayList<>();
        Set<Technician.Specialty> requiredSpecialties = faultType.getRequiredSpecialties();
        int requiredTechCount = faultType.getRequiredTechCount();

        // 如果只需要一种工种，按需要的技师数量分配
        if (requiredSpecialties.size() == 1) {
            Technician.Specialty specialty = requiredSpecialties.iterator().next();
            List<Technician> availableTechnicians = technicianRepository
                    .findBySpecialtyAndStatus(specialty, 1);

            if (availableTechnicians.size() < requiredTechCount) {
                throw new BusinessException("可用的 " + specialty.name() + " 技师数量不足，需要 "
                    + requiredTechCount + " 个，但只有 " + availableTechnicians.size() + " 个");
            }

            // 按工作负载排序，选择负载最少的技师
            availableTechnicians.sort((t1, t2) -> Integer.compare(t1.getWorkload(), t2.getWorkload()));

            // 分配所需数量的技师
            for (int i = 0; i < requiredTechCount; i++) {
                Technician selectedTechnician = availableTechnicians.get(i);
                assignedTechnicians.add(selectedTechnician);

                // 更新技师工作负载
                selectedTechnician.setWorkload(selectedTechnician.getWorkload() + 1);
                technicianRepository.save(selectedTechnician);
            }
        } else {
            // 如果需要多种工种，每种工种分配一个技师
            // 总技师数量应该等于工种数量
            if (requiredTechCount != requiredSpecialties.size()) {
                throw new BusinessException("多工种故障的技师数量应该等于工种数量");
            }

            for (Technician.Specialty specialty : requiredSpecialties) {
                List<Technician> availableTechnicians = technicianRepository
                        .findBySpecialtyAndStatus(specialty, 1);

                if (availableTechnicians.isEmpty()) {
                    throw new BusinessException("没有可用的 " + specialty.name() + " 技师");
                }

                // 选择工作负载最少的技师
                Technician selectedTechnician = availableTechnicians.stream()
                        .min((t1, t2) -> Integer.compare(t1.getWorkload(), t2.getWorkload()))
                        .orElseThrow(() -> new BusinessException("技师分配失败"));

                assignedTechnicians.add(selectedTechnician);

                // 更新技师工作负载
                selectedTechnician.setWorkload(selectedTechnician.getWorkload() + 1);
                technicianRepository.save(selectedTechnician);
            }
        }

        // 创建技师分配记录
        List<OrderTechnicianAssignment> assignments = new ArrayList<>();
        for (Technician technician : assignedTechnicians) {
            OrderTechnicianAssignment assignment = new OrderTechnicianAssignment(order, technician);
            assignments.add(assignment);
            orderTechnicianAssignmentRepository.save(assignment);
        }

        order.setTechnicianAssignments(assignments);
        order.setStatus(RepairOrder.OrderStatus.ASSIGNED);

        // 记录状态历史
        String technicianNames = assignedTechnicians.stream()
                .map(Technician::getRealName)
                .collect(Collectors.joining(", "));
        recordStatusHistory(order, RepairOrder.OrderStatus.PENDING, RepairOrder.OrderStatus.ASSIGNED,
                "已分配技师：" + technicianNames);
    }

    /**
     * 重新分配技师（当技师拒绝时）
     */
    private void reassignTechnician(RepairOrder order, Technician.Specialty rejectedSpecialty) {
        // 查找可用的同工种技师
        List<Technician> availableTechnicians = technicianRepository
                .findBySpecialtyAndStatus(rejectedSpecialty, 1);

        // 排除已经分配到此工单的技师
        List<Long> assignedTechnicianIds = order.getTechnicianAssignments().stream()
                .map(assignment -> assignment.getTechnician().getTechnicianId())
                .collect(Collectors.toList());

        availableTechnicians = availableTechnicians.stream()
                .filter(tech -> !assignedTechnicianIds.contains(tech.getTechnicianId()))
                .collect(Collectors.toList());

        if (availableTechnicians.isEmpty()) {
            throw new BusinessException("没有可用的 " + rejectedSpecialty.name() + " 技师进行重新分配");
        }

        // 选择工作负载最少的技师
        Technician selectedTechnician = availableTechnicians.stream()
                .min((t1, t2) -> Integer.compare(t1.getWorkload(), t2.getWorkload()))
                .orElseThrow(() -> new BusinessException("技师重新分配失败"));

        // 创建新的分配记录
        OrderTechnicianAssignment newAssignment = new OrderTechnicianAssignment(order, selectedTechnician);
        orderTechnicianAssignmentRepository.save(newAssignment);

        // 更新技师工作负载
        selectedTechnician.setWorkload(selectedTechnician.getWorkload() + 1);
        technicianRepository.save(selectedTechnician);

        // 记录状态历史
        recordStatusHistory(order, RepairOrder.OrderStatus.ASSIGNED, RepairOrder.OrderStatus.ASSIGNED,
                "重新分配技师：" + selectedTechnician.getRealName() + " 替换 " + rejectedSpecialty.name() + " 工种");
    }

    /**
     * 记录状态历史
     */
    private void recordStatusHistory(RepairOrder order, RepairOrder.OrderStatus oldStatus, RepairOrder.OrderStatus newStatus, String remark) {
        OrderStatusHistory history = new OrderStatusHistory();
        history.setRepairOrder(order);
        history.setFromStatus(oldStatus);
        history.setToStatus(newStatus);
        history.setRemark(remark);
        history.setChangeTime(LocalDateTime.now());
        orderStatusHistoryRepository.save(history);
    }

    /**
     * 验证技师访问权限
     */
    private void validateTechnicianAccess(RepairOrder order, Long technicianId) {
        boolean isAssigned = order.getTechnicianAssignments().stream()
                .anyMatch(assignment -> assignment.getTechnician().getTechnicianId().equals(technicianId));

        if (!isAssigned) {
            throw new BusinessException("您未被分配到此工单");
        }
    }

    /**
     * 处理材料使用记录
     */
    private void processMaterialUsage(RepairOrder order, List<OrderCompletionRequest.MaterialUsageRequest> materialsUsed) {
        BigDecimal newMaterialCost = BigDecimal.ZERO;

        for (OrderCompletionRequest.MaterialUsageRequest usage : materialsUsed) {
            Material material = materialRepository.findById(usage.getMaterialId())
                    .orElseThrow(() -> new ResourceNotFoundException("材料不存在"));

            // 检查库存
            if (material.getInventory() < usage.getQuantity().intValue()) {
                throw new BusinessException("材料 " + material.getMaterialName() + " 库存不足");
            }

            // 创建使用记录
            OrderMaterialUsage materialUsage = new OrderMaterialUsage();
            materialUsage.setRepairOrder(order);
            materialUsage.setMaterial(material);
            materialUsage.setQuantity(usage.getQuantity());
            materialUsage.setTotalPrice(usage.getTotalPrice());
            materialUsage.setUseTime(LocalDateTime.now());

            orderMaterialUsageRepository.save(materialUsage);

            // 更新库存
            material.setInventory(material.getInventory() - usage.getQuantity().intValue());
            materialRepository.save(material);

            newMaterialCost = newMaterialCost.add(usage.getTotalPrice());
        }

        // 累加到现有的材料费用上
        BigDecimal currentMaterialCost = order.getTotalMaterialCost() != null ? order.getTotalMaterialCost() : BigDecimal.ZERO;
        order.setTotalMaterialCost(currentMaterialCost.add(newMaterialCost));
    }

    /**
     * 将RepairOrder实体转换为OrderDTO
     */
    private OrderDTO convertToDTO(RepairOrder order) {
        return convertToDTO(order, null);
    }

    /**
     * 将RepairOrder实体转换为OrderDTO（带用户类型）
     */
    private OrderDTO convertToDTO(RepairOrder order, String userType) {
        OrderDTO dto = new OrderDTO();

        // 基本信息
        dto.setOrderId(order.getOrderId());
        dto.setUserId(order.getUser().getUserId());
        dto.setVehicleId(order.getVehicle().getVehicleId());
        dto.setFaultTypeId(order.getFaultType().getFaultTypeId());
        dto.setDescription(order.getDescription());
        dto.setUrgencyLevel(order.getUrgencyLevel().name().toLowerCase());
        dto.setSubmitTime(order.getSubmitTime());
        dto.setPreferredTime(order.getPreferredTime());
        dto.setEstimatedCompletionTime(order.getEstimatedCompletionTime());
        dto.setActualCompletionTime(order.getActualCompletionTime());
        dto.setStatus(order.getStatus().name().toLowerCase());
        dto.setPaymentStatus(order.getPaymentStatus().name().toLowerCase());
        dto.setContactPhone(order.getContactPhone());
        dto.setTotalLaborCost(order.getTotalLaborCost());
        dto.setTotalMaterialCost(order.getTotalMaterialCost());
        dto.setTotalCost(order.getTotalCost());
        dto.setWorkResult(order.getWorkResult());
        dto.setWorkingHours(order.getWorkingHours());

        // 用户信息
        User user = order.getUser();
        dto.setUser(new OrderDTO.UserInfo(
                user.getUserId(),
                user.getUsername(),
                user.getRealName(),
                user.getPhone()
        ));

        // 车辆信息
        Vehicle vehicle = order.getVehicle();
        dto.setVehicle(new OrderDTO.VehicleInfo(
                vehicle.getVehicleId(),
                vehicle.getLicensePlate(),
                vehicle.getBrand(),
                vehicle.getModel()
        ));

        // 故障类型信息
        FaultType faultType = order.getFaultType();
        dto.setFaultType(new OrderDTO.FaultTypeInfo(
                faultType.getFaultTypeId(),
                faultType.getTypeName(),
                faultType.getRequiredSpecialties().stream()
                        .map(s -> s.name().toLowerCase())
                        .collect(Collectors.toList()),
                faultType.getEstimatedHours().intValue()
        ));

        // 分配的技师信息
        List<OrderDTO.TechnicianInfo> technicianInfos = order.getAssignedTechnicians().stream()
                .map(tech -> new OrderDTO.TechnicianInfo(
                        tech.getTechnicianId(),
                        tech.getRealName(),
                        tech.getSpecialty().name().toLowerCase(),
                        tech.getPhone()
                ))
                .collect(Collectors.toList());
        dto.setAssignedTechnicians(technicianInfos);

        // 材料使用信息（只对技师和管理员可见）
        if (userType != null && ("TECHNICIAN".equals(userType) || "ADMIN".equals(userType))) {
            List<OrderDTO.MaterialUsageInfo> materialUsageInfos = order.getMaterialUsages().stream()
                    .map(usage -> new OrderDTO.MaterialUsageInfo(
                            usage.getUsageId(),
                            usage.getMaterial().getMaterialId(),
                            usage.getMaterial().getMaterialName(),
                            usage.getMaterial().getSpecification(),
                            usage.getMaterial().getUnit(),
                            usage.getMaterial().getUnitPrice(),
                            usage.getQuantity(),
                            usage.getTotalPrice(),
                            usage.getUseTime()
                    ))
                    .collect(Collectors.toList());
            dto.setMaterialUsages(materialUsageInfos);
        }

        // 反馈信息
        if (order.getFeedback() != null) {
            OrderFeedback feedback = order.getFeedback();
            dto.setFeedback(new OrderDTO.FeedbackInfo(
                    feedback.getFeedbackId(),
                    feedback.getRating(),
                    feedback.getComment(),
                    feedback.getFeedbackTime()
            ));
        }

        // 催单信息
        if (order.getUrgentRequests() != null && !order.getUrgentRequests().isEmpty()) {
            List<OrderDTO.UrgentRequestInfo> urgentRequestInfos = order.getUrgentRequests().stream()
                    .map(urgentRequest -> new OrderDTO.UrgentRequestInfo(
                            urgentRequest.getUrgentId(),
                            urgentRequest.getReason(),
                            urgentRequest.getUrgentTime(),
                            urgentRequest.getStatus().name().toLowerCase(),
                            urgentRequest.getStatus().getDisplayName()
                    ))
                    .collect(Collectors.toList());
            dto.setUrgentRequests(urgentRequestInfos);
        }

        return dto;
    }

    /**
     * 更新工单状态（管理员专用）
     */
    public void updateOrderStatus(Long orderId, OrderStatusUpdateRequest request) {
        RepairOrder order = repairOrderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("工单不存在"));

        RepairOrder.OrderStatus newStatus = RepairOrder.OrderStatus.valueOf(request.getStatus().toUpperCase());
        RepairOrder.OrderStatus oldStatus = order.getStatus();

        order.setStatus(newStatus);
        repairOrderRepository.save(order);

        // 记录状态历史
        String remark = request.getReason() != null ? request.getReason() : "管理员手动调整状态";
        recordStatusHistory(order, oldStatus, newStatus, remark);
    }

    /**
     * 提交工单反馈
     */
    public void submitFeedback(Long orderId, Long userId, OrderFeedbackRequest request) {
        RepairOrder order = repairOrderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("工单不存在"));

        // 检查权限
        if (!order.getUser().getUserId().equals(userId)) {
            throw new BusinessException("无权对此工单进行反馈");
        }

        // 检查工单状态
        if (order.getStatus() != RepairOrder.OrderStatus.COMPLETED) {
            throw new BusinessException("只能对已完成的工单进行反馈");
        }

        // 检查是否已经反馈过
        if (order.getFeedback() != null) {
            throw new BusinessException("此工单已经反馈过");
        }

        // 创建反馈记录
        OrderFeedback feedback = new OrderFeedback();
        feedback.setRepairOrder(order);
        feedback.setRating(request.getRating());
        feedback.setComment(request.getComment());
        feedback.setFeedbackTime(LocalDateTime.now());

        orderFeedbackRepository.save(feedback);
        order.setFeedback(feedback);
        repairOrderRepository.save(order);
    }

    /**
     * 提交工单催单
     */
    public void submitUrgentRequest(Long orderId, Long userId, com.example.dto.request.OrderUrgentRequest request) {
        RepairOrder order = repairOrderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("工单不存在"));

        // 检查权限
        if (!order.getUser().getUserId().equals(userId)) {
            throw new BusinessException("无权对此工单进行催单");
        }

        // 检查工单状态 - 只能对进行中的工单催单
        if (order.getStatus() != RepairOrder.OrderStatus.IN_PROGRESS) {
            throw new BusinessException("只能对进行中的工单进行催单");
        }

        // 创建催单记录
        OrderUrgentRequest urgentRequest = new OrderUrgentRequest();
        urgentRequest.setRepairOrder(order);
        urgentRequest.setReason(request.getReason());
        urgentRequest.setUrgentTime(LocalDateTime.now());
        urgentRequest.setStatus(OrderUrgentRequest.UrgentStatus.PENDING);

        orderUrgentRequestRepository.save(urgentRequest);
    }

    /**
     * 获取工单状态历史
     */
    @Transactional(readOnly = true)
    public List<Object> getOrderHistory(Long orderId, Long userId, String userType) {
        RepairOrder order = repairOrderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("工单不存在"));

        // 权限检查
        validateOrderAccess(order, userId, userType);

        return order.getStatusHistories().stream()
                .map(history -> {
                    // 这里应该返回适当的DTO，暂时返回Object
                    return new Object(); // 需要创建StatusHistoryDTO
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取工单技师分配信息
     */
    @Transactional(readOnly = true)
    public List<Object> getOrderAssignments(Long orderId, Long userId, String userType) {
        RepairOrder order = repairOrderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("工单不存在"));

        // 权限检查
        validateOrderAccess(order, userId, userType);

        return order.getTechnicianAssignments().stream()
                .map(assignment -> {
                    // 这里应该返回适当的DTO，暂时返回Object
                    return new Object(); // 需要创建TechnicianAssignmentDTO
                })
                .collect(Collectors.toList());
    }

    /**
     * 添加材料使用记录
     */
    public void addMaterialUsage(Long orderId, Long technicianId, MaterialUsageRequest request) {
        RepairOrder order = repairOrderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("工单不存在"));

        // 检查技师权限
        validateTechnicianAccess(order, technicianId);

        // 检查工单状态
        if (order.getStatus() != RepairOrder.OrderStatus.IN_PROGRESS) {
            throw new BusinessException("只能在维修进行中添加材料使用记录");
        }

        Material material = materialRepository.findById(request.getMaterialId())
                .orElseThrow(() -> new ResourceNotFoundException("材料不存在"));

        // 检查库存
        if (material.getInventory() < request.getQuantity().intValue()) {
            throw new BusinessException("材料库存不足");
        }

        // 创建使用记录
        OrderMaterialUsage usage = new OrderMaterialUsage();
        usage.setRepairOrder(order);
        usage.setMaterial(material);
        usage.setQuantity(request.getQuantity());
        usage.setTotalPrice(request.getTotalPrice());
        usage.setUseTime(request.getUseTime() != null ? request.getUseTime() : LocalDateTime.now());

        orderMaterialUsageRepository.save(usage);

        // 更新库存
        material.setInventory(material.getInventory() - request.getQuantity().intValue());
        materialRepository.save(material);

        // 更新工单材料费用
        BigDecimal currentMaterialCost = order.getTotalMaterialCost() != null ? order.getTotalMaterialCost() : BigDecimal.ZERO;
        BigDecimal currentLaborCost = order.getTotalLaborCost() != null ? order.getTotalLaborCost() : BigDecimal.ZERO;

        order.setTotalMaterialCost(currentMaterialCost.add(request.getTotalPrice()));
        order.setTotalCost(currentLaborCost.add(order.getTotalMaterialCost()));
        repairOrderRepository.save(order);
    }

    /**
     * 获取工单材料使用记录
     */
    @Transactional(readOnly = true)
    public List<MaterialUsageDTO> getOrderMaterials(Long orderId, Long userId, String userType) {
        RepairOrder order = repairOrderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("工单不存在"));

        // 权限检查
        validateOrderAccess(order, userId, userType);

        return order.getMaterialUsages().stream()
                .map(this::convertMaterialUsageToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将OrderMaterialUsage实体转换为MaterialUsageDTO
     */
    private MaterialUsageDTO convertMaterialUsageToDTO(OrderMaterialUsage usage) {
        Material material = usage.getMaterial();
        MaterialUsageDTO.MaterialInfo materialInfo = new MaterialUsageDTO.MaterialInfo(
                material.getMaterialId(),
                material.getMaterialName(),
                material.getSpecification(),
                material.getUnit(),
                material.getUnitPrice(),
                material.getCategory()
        );

        return new MaterialUsageDTO(
                usage.getUsageId(),
                usage.getRepairOrder().getOrderId(),
                materialInfo,
                usage.getQuantity(),
                usage.getTotalPrice(),
                usage.getUseTime(),
                usage.getCreateTime(),
                usage.getUpdateTime()
        );
    }

    /**
     * 验证工单访问权限
     */
    private void validateOrderAccess(RepairOrder order, Long userId, String userType) {
        if ("USER".equals(userType) && !order.getUser().getUserId().equals(userId)) {
            throw new BusinessException("无权访问此工单");
        } else if ("TECHNICIAN".equals(userType)) {
            boolean isAssigned = order.getTechnicianAssignments().stream()
                    .anyMatch(assignment -> assignment.getTechnician().getTechnicianId().equals(userId));
            if (!isAssigned) {
                throw new BusinessException("无权访问此工单");
            }
        }
        // ADMIN用户可以访问所有工单
    }

    /**
     * 为所有参与的技师创建或更新工时费记录
     */
    private void createOrUpdateLaborPayments(RepairOrder order) {
        LocalDateTime completionTime = order.getActualCompletionTime();
        int year = completionTime.getYear();
        int month = completionTime.getMonthValue();
        BigDecimal workingHours = order.getWorkingHours();

        // 为每个分配的技师创建或更新工时费记录
        for (Technician technician : order.getAssignedTechnicians()) {
            // 查找该技师当月的工时费记录
            LaborPayment existingPayment = laborPaymentRepository
                    .findByTechnicianTechnicianIdAndYearAndMonth(technician.getTechnicianId(), year, month)
                    .orElse(null);

            if (existingPayment != null) {
                // 更新现有记录
                existingPayment.setTotalHours(existingPayment.getTotalHours().add(workingHours));
                existingPayment.setTotalAmount(existingPayment.getTotalHours().multiply(existingPayment.getHourlyRate()));
                laborPaymentRepository.save(existingPayment);
            } else {
                // 创建新记录
                LaborPayment newPayment = new LaborPayment(year, month, workingHours, technician.getHourlyRate(), technician);
                laborPaymentRepository.save(newPayment);
            }
        }
    }
}
