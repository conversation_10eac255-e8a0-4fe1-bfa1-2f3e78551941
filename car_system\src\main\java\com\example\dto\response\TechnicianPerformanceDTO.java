package com.example.dto.response;

import java.math.BigDecimal;

/**
 * 技师绩效统计DTO
 */
public class TechnicianPerformanceDTO {
    
    private Long technicianId;
    private String realName;
    private String specialty;
    private Integer completedOrders;
    private BigDecimal totalWorkingHours;
    private BigDecimal avgRating;
    private BigDecimal totalEarnings;
    
    public TechnicianPerformanceDTO() {}
    
    public TechnicianPerformanceDTO(Long technicianId, String realName, String specialty,
                                   Integer completedOrders, BigDecimal totalWorkingHours,
                                   BigDecimal avgRating, BigDecimal totalEarnings) {
        this.technicianId = technicianId;
        this.realName = realName;
        this.specialty = specialty;
        this.completedOrders = completedOrders;
        this.totalWorkingHours = totalWorkingHours;
        this.avgRating = avgRating;
        this.totalEarnings = totalEarnings;
    }
    
    // Getters and Setters
    public Long getTechnicianId() {
        return technicianId;
    }
    
    public void setTechnicianId(Long technicianId) {
        this.technicianId = technicianId;
    }
    
    public String getRealName() {
        return realName;
    }
    
    public void setRealName(String realName) {
        this.realName = realName;
    }
    
    public String getSpecialty() {
        return specialty;
    }
    
    public void setSpecialty(String specialty) {
        this.specialty = specialty;
    }
    
    public Integer getCompletedOrders() {
        return completedOrders;
    }
    
    public void setCompletedOrders(Integer completedOrders) {
        this.completedOrders = completedOrders;
    }
    
    public BigDecimal getTotalWorkingHours() {
        return totalWorkingHours;
    }
    
    public void setTotalWorkingHours(BigDecimal totalWorkingHours) {
        this.totalWorkingHours = totalWorkingHours;
    }
    
    public BigDecimal getAvgRating() {
        return avgRating;
    }
    
    public void setAvgRating(BigDecimal avgRating) {
        this.avgRating = avgRating;
    }
    
    public BigDecimal getTotalEarnings() {
        return totalEarnings;
    }
    
    public void setTotalEarnings(BigDecimal totalEarnings) {
        this.totalEarnings = totalEarnings;
    }
}
