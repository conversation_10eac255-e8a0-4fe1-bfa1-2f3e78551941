<template>
  <div class="analytics-page">
    <div class="page-header">
      <h1>数据统计分析</h1>
      <p>系统运营数据统计与分析</p>
    </div>

    <!-- 时间范围选择 -->
    <el-card class="filter-card">
      <div class="filter-row">
        <div class="filter-item">
          <label>统计时间范围：</label>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="onDateRangeChange"
          />
        </div>
        <div class="filter-item">
          <label>年份：</label>
          <el-select v-model="selectedYear" placeholder="选择年份" @change="fetchCostAnalysis">
            <el-option
              v-for="year in availableYears"
              :key="year"
              :label="year"
              :value="year"
            />
          </el-select>
        </div>
        <div class="filter-item">
          <el-button type="primary" @click="refreshAllData">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon repair">
            <el-icon size="24"><Tools /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ totalRepairs }}</div>
            <div class="stat-label">总维修次数</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon cost">
            <el-icon size="24"><Money /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">¥{{ formatCurrency(totalCost) }}</div>
            <div class="stat-label">总维修费用</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon technician">
            <el-icon size="24"><User /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ activeTechnicians }}</div>
            <div class="stat-label">活跃技师</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon efficiency">
            <el-icon size="24"><TrendCharts /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ avgCompletionRate }}%</div>
            <div class="stat-label">平均完成率</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 图表区域 -->
    <div class="charts-grid">
      <!-- 维修统计图表 -->
      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>维修统计</span>
            <el-select v-model="repairGroupBy" size="small" @change="fetchRepairStats">
              <el-option label="按品牌" value="brand" />
              <el-option label="按型号" value="model" />
              <el-option label="按故障类型" value="faultType" />
            </el-select>
          </div>
        </template>
        <div v-loading="repairStatsLoading" class="chart-container">
          <Bar v-if="repairChartData.labels.length > 0" :key="repairChartKey" :data="repairChartData" :options="chartOptions" />
          <el-empty v-else description="暂无数据" />
        </div>
      </el-card>

      <!-- 成本分析图表 -->
      <el-card class="chart-card">
        <template #header>
          <span>成本分析</span>
        </template>
        <div v-loading="costAnalysisLoading" class="chart-container">
          <Doughnut v-if="costChartData.datasets[0].data.some(val => val > 0)" :key="costChartKey" :data="costChartData" :options="doughnutOptions" />
          <el-empty v-else description="暂无数据" />
        </div>
      </el-card>
    </div>

    <!-- 工作负载统计 -->
    <el-card class="workload-card">
      <template #header>
        <span>工作负载统计</span>
      </template>
      <div v-loading="workloadLoading" class="workload-content">
        <div v-if="workloadStats.length > 0" class="workload-grid">
          <div v-for="item in workloadStats" :key="item.specialty" class="workload-item">
            <div class="workload-header">
              <h4>{{ item.specialtyName }}</h4>
              <el-tag :type="getWorkloadType(item.completionRate)">
                完成率 {{ item.completionRate }}%
              </el-tag>
            </div>
            <div class="workload-stats">
              <div class="stat-item">
                <span class="label">分配任务:</span>
                <span class="value">{{ item.assignedCount }}</span>
              </div>
              <div class="stat-item">
                <span class="label">完成任务:</span>
                <span class="value">{{ item.completedCount }}</span>
              </div>
              <div class="stat-item">
                <span class="label">平均工时:</span>
                <span class="value">{{ item.avgWorkingHours }}h</span>
              </div>
              <div class="stat-item">
                <span class="label">任务占比:</span>
                <span class="value">{{ item.assignmentPercentage }}%</span>
              </div>
            </div>
            <el-progress
              :percentage="item.completionRate"
              :color="getProgressColor(item.completionRate)"
              :stroke-width="8"
            />
          </div>
        </div>
        <el-empty v-else description="暂无数据" />
      </div>
    </el-card>

    <!-- 故障模式统计 -->
    <el-card class="patterns-card">
      <template #header>
        <div class="card-header">
          <span>故障模式统计</span>
          <div class="filter-controls">
            <el-select v-model="patternBrand" placeholder="选择品牌" size="small" clearable @change="fetchPatternStats">
              <el-option
                v-for="brand in availableBrands"
                :key="brand"
                :label="brand"
                :value="brand"
              />
            </el-select>
            <el-select v-model="patternModel" placeholder="选择型号" size="small" clearable @change="fetchPatternStats">
              <el-option
                v-for="model in availableModels"
                :key="model"
                :label="model"
                :value="model"
              />
            </el-select>
          </div>
        </div>
      </template>
      <div v-loading="patternStatsLoading" class="patterns-content">
        <div v-if="patternStats.length > 0" class="patterns-table">
          <el-table :data="patternStats" stripe>
            <el-table-column prop="brand" label="品牌" width="100" />
            <el-table-column prop="model" label="型号" width="120" />
            <el-table-column prop="faultType" label="故障类型" min-width="150" />
            <el-table-column prop="occurrenceCount" label="发生次数" width="100" align="center" />
            <el-table-column prop="faultPercentage" label="故障占比" width="100" align="center">
              <template #default="{ row }">
                {{ row.faultPercentage }}%
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-empty v-else description="暂无数据" />
      </div>
    </el-card>

    <!-- 技师绩效统计 -->
    <el-card class="performance-card">
      <template #header>
        <div class="card-header">
          <span>技师绩效统计</span>
          <el-select v-model="performanceSpecialty" placeholder="选择工种" size="small" clearable @change="fetchTechnicianPerformance">
            <el-option
              v-for="specialty in specialtyOptions"
              :key="specialty.value"
              :label="specialty.label"
              :value="specialty.value"
            />
          </el-select>
        </div>
      </template>
      <div v-loading="performanceLoading" class="performance-content">
        <div v-if="performanceStats.length > 0" class="performance-table">
          <el-table :data="performanceStats" stripe>
            <el-table-column prop="realName" label="技师姓名" width="120" />
            <el-table-column prop="specialty" label="工种" width="120">
              <template #default="{ row }">
                {{ getSpecialtyName(row.specialty) }}
              </template>
            </el-table-column>
            <el-table-column prop="completedOrders" label="完成工单" width="100" align="center" />
            <el-table-column prop="totalWorkingHours" label="总工时" width="100" align="center">
              <template #default="{ row }">
                {{ row.totalWorkingHours }}h
              </template>
            </el-table-column>
            <el-table-column prop="avgRating" label="平均评分" width="100" align="center">
              <template #default="{ row }">
                <el-rate
                  v-model="row.avgRating"
                  disabled
                  show-score
                  text-color="#ff9900"
                  score-template="{value}"
                />
              </template>
            </el-table-column>
            <el-table-column prop="totalEarnings" label="总收入" width="120" align="center">
              <template #default="{ row }">
                ¥{{ formatCurrency(row.totalEarnings) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-empty v-else description="暂无数据" />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh, Tools, Money, User, TrendCharts
} from '@element-plus/icons-vue'
import { Bar, Doughnut } from 'vue-chartjs'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
} from 'chart.js'
import { analyticsAPI } from '@/api/analytics'
import dayjs from 'dayjs'

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
)

// 响应式数据
const dateRange = ref([])
const selectedYear = ref(new Date().getFullYear())
const repairGroupBy = ref('brand')
const patternBrand = ref('')
const patternModel = ref('')
const performanceSpecialty = ref('')

// 加载状态
const repairStatsLoading = ref(false)
const costAnalysisLoading = ref(false)
const workloadLoading = ref(false)
const patternStatsLoading = ref(false)
const performanceLoading = ref(false)

// 图表重新渲染key
const repairChartKey = ref(0)
const costChartKey = ref(0)

// 统计数据
const totalRepairs = ref(0)
const totalCost = ref(0)
const activeTechnicians = ref(0)
const avgCompletionRate = ref(0)

// 图表数据
const repairChartData = reactive({
  labels: [],
  datasets: [{
    label: '维修次数',
    data: [],
    backgroundColor: 'rgba(54, 162, 235, 0.6)',
    borderColor: 'rgba(54, 162, 235, 1)',
    borderWidth: 1
  }]
})

const costChartData = reactive({
  labels: ['人工费用', '材料费用'],
  datasets: [{
    data: [0, 0],
    backgroundColor: [
      'rgba(255, 99, 132, 0.6)',
      'rgba(54, 162, 235, 0.6)'
    ],
    borderColor: [
      'rgba(255, 99, 132, 1)',
      'rgba(54, 162, 235, 1)'
    ],
    borderWidth: 1
  }]
})

// 其他数据
const workloadStats = ref([])
const patternStats = ref([])
const performanceStats = ref([])
const availableBrands = ref([])
const availableModels = ref([])

// 计算属性
const availableYears = computed(() => {
  const currentYear = new Date().getFullYear()
  const years = []
  for (let i = currentYear; i >= currentYear - 5; i--) {
    years.push(i)
  }
  return years
})

const specialtyOptions = computed(() => [
  { label: '发动机维修', value: 'engine' },
  { label: '变速箱维修', value: 'transmission' },
  { label: '制动系统维修', value: 'brake' },
  { label: '电气系统维修', value: 'electrical' },
  { label: '空调系统维修', value: 'hvac' },
  { label: '底盘维修', value: 'chassis' },
  { label: '车身维修', value: 'body' },
  { label: '轮胎维修', value: 'tire' }
])

// 图表配置
const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top'
    },
    title: {
      display: false
    }
  },
  scales: {
    y: {
      beginAtZero: true
    }
  }
}

const doughnutOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom'
    }
  }
}

// 工具方法
const formatCurrency = (amount) => {
  return (amount || 0).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const getSpecialtyName = (specialty) => {
  const specialtyMap = {
    engine: '发动机维修',
    transmission: '变速箱维修',
    brake: '制动系统维修',
    electrical: '电气系统维修',
    hvac: '空调系统维修',
    chassis: '底盘维修',
    body: '车身维修',
    tire: '轮胎维修'
  }
  return specialtyMap[specialty] || specialty
}

const getWorkloadType = (completionRate) => {
  if (completionRate >= 90) return 'success'
  if (completionRate >= 70) return 'warning'
  return 'danger'
}

const getProgressColor = (percentage) => {
  if (percentage >= 90) return '#67c23a'
  if (percentage >= 70) return '#e6a23c'
  return '#f56c6c'
}

// API调用方法
const fetchRepairStats = async () => {
  try {
    repairStatsLoading.value = true
    const params = {
      groupBy: repairGroupBy.value
    }
    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    }

    const response = await analyticsAPI.getRepairStats(params)
    const data = response.data.data || response.data || []

    console.log('Repair stats data:', data)
    console.log('Group by:', repairGroupBy.value)

    if (data.length > 0) {
      // 更新图表数据
      const newLabels = data.map(item => {
        if (repairGroupBy.value === 'brand') {
          return item.brand || '未知品牌'
        } else if (repairGroupBy.value === 'model') {
          return `${item.brand || '未知品牌'} ${item.model || '未知型号'}`
        } else if (repairGroupBy.value === 'faultType') {
          return item.faultType || item.typeName || '未知故障类型'
        }
        return '未知'
      })
      const newData = data.map(item => item.repairCount || 0)

      console.log('New labels:', newLabels)
      console.log('New data:', newData)

      // 完全重新创建图表数据对象
      Object.assign(repairChartData, {
        labels: [...newLabels],
        datasets: [{
          label: '维修次数',
          data: [...newData],
          backgroundColor: 'rgba(54, 162, 235, 0.6)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1
        }]
      })

      // 强制重新渲染图表
      repairChartKey.value++

      console.log('Updated repair chart data:', repairChartData)

      // 计算总维修次数
      totalRepairs.value = data.reduce((sum, item) => sum + (item.repairCount || 0), 0)
      totalCost.value = data.reduce((sum, item) => sum + (item.totalCost || 0), 0)
    } else {
      // 如果没有数据，重置图表
      console.log('No repair stats data, resetting chart')
      Object.assign(repairChartData, {
        labels: [],
        datasets: [{
          label: '维修次数',
          data: [],
          backgroundColor: 'rgba(54, 162, 235, 0.6)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1
        }]
      })
      totalRepairs.value = 0
      totalCost.value = 0
    }
  } catch (error) {
    console.error('Failed to fetch repair stats:', error)
    ElMessage.error('获取维修统计失败: ' + (error.response?.data?.message || error.message))
    // 出错时重置数据
    Object.assign(repairChartData, {
      labels: [],
      datasets: [{
        label: '维修次数',
        data: [],
        backgroundColor: 'rgba(54, 162, 235, 0.6)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1
      }]
    })
    totalRepairs.value = 0
    totalCost.value = 0
  } finally {
    repairStatsLoading.value = false
  }
}

const fetchCostAnalysis = async () => {
  try {
    costAnalysisLoading.value = true
    const params = { year: selectedYear.value }

    console.log('Fetching cost analysis with params:', params)
    const response = await analyticsAPI.getCostAnalysis(params)
    console.log('Cost analysis response:', response)

    // 正确解析响应数据结构
    const data = response.data.data || response.data || []
    console.log('Cost analysis data:', data)

    if (data.length > 0) {
      const item = data[0]
      console.log('Cost analysis item:', item)

      const laborCost = Number(item.totalLaborCost) || 0
      const materialCost = Number(item.totalMaterialCost) || 0

      console.log('Labor cost:', laborCost, 'Material cost:', materialCost)

      // 安全地更新图表数据
      costChartData.datasets[0].data[0] = laborCost
      costChartData.datasets[0].data[1] = materialCost

      // 强制重新渲染图表
      costChartKey.value++

      console.log('Updated cost chart data:', costChartData)
    } else {
      // 如果没有数据，重置图表
      console.log('No cost analysis data, resetting chart')
      costChartData.datasets[0].data[0] = 0
      costChartData.datasets[0].data[1] = 0
    }
  } catch (error) {
    console.error('Failed to fetch cost analysis:', error)
    ElMessage.error('获取成本分析失败: ' + (error.response?.data?.message || error.message))
    // 出错时重置图表数据
    costChartData.datasets[0].data[0] = 0
    costChartData.datasets[0].data[1] = 0
  } finally {
    costAnalysisLoading.value = false
  }
}

const fetchWorkloadStats = async () => {
  try {
    workloadLoading.value = true
    const params = {}
    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    }

    const response = await analyticsAPI.getWorkloadStats(params)
    workloadStats.value = response.data.data || response.data || []

    // 计算平均完成率
    if (workloadStats.value.length > 0) {
      const totalRate = workloadStats.value.reduce((sum, item) => sum + (item.completionRate || 0), 0)
      avgCompletionRate.value = Math.round(totalRate / workloadStats.value.length)
      activeTechnicians.value = workloadStats.value.length
    } else {
      // 如果没有数据，重置统计值
      avgCompletionRate.value = 0
      activeTechnicians.value = 0
    }
  } catch (error) {
    console.error('Failed to fetch workload stats:', error)
    ElMessage.error('获取工作负载统计失败')
    // 出错时重置数据
    workloadStats.value = []
    avgCompletionRate.value = 0
    activeTechnicians.value = 0
  } finally {
    workloadLoading.value = false
  }
}

const fetchPatternStats = async () => {
  try {
    patternStatsLoading.value = true
    const params = {}
    if (patternBrand.value) params.brand = patternBrand.value
    if (patternModel.value) params.model = patternModel.value
    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    }

    const response = await analyticsAPI.getPatternStats(params)
    patternStats.value = response.data.data || response.data || []
  } catch (error) {
    console.error('Failed to fetch pattern stats:', error)
    ElMessage.error('获取故障模式统计失败')
  } finally {
    patternStatsLoading.value = false
  }
}

const fetchTechnicianPerformance = async () => {
  try {
    performanceLoading.value = true
    const params = {}
    if (performanceSpecialty.value) params.specialty = performanceSpecialty.value
    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    }

    const response = await analyticsAPI.getTechnicianPerformance(params)
    performanceStats.value = response.data.data || response.data || []
  } catch (error) {
    console.error('Failed to fetch technician performance:', error)
    ElMessage.error('获取技师绩效统计失败')
  } finally {
    performanceLoading.value = false
  }
}

const fetchBrandsAndModels = async () => {
  try {
    const response = await analyticsAPI.getBrandsAndModels()
    const data = response.data.data || response.data || {}
    availableBrands.value = data.brands || []
    availableModels.value = data.models || []
  } catch (error) {
    console.error('Failed to fetch brands and models:', error)
  }
}

// 事件处理方法
const onDateRangeChange = () => {
  refreshAllData()
}

const refreshAllData = async () => {
  await Promise.all([
    fetchRepairStats(),
    fetchCostAnalysis(),
    fetchWorkloadStats(),
    fetchPatternStats(),
    fetchTechnicianPerformance()
  ])
}

// 初始化数据
const initializeData = async () => {
  // 设置默认时间范围为最近30天
  const endDate = dayjs().format('YYYY-MM-DD')
  const startDate = dayjs().subtract(30, 'day').format('YYYY-MM-DD')
  dateRange.value = [startDate, endDate]

  // 获取品牌和型号数据
  await fetchBrandsAndModels()

  // 获取所有统计数据
  await refreshAllData()
}

// 生命周期
onMounted(() => {
  initializeData()
})
</script>

<style scoped>
.analytics-page {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

/* 过滤器卡片 */
.filter-card {
  margin-bottom: 20px;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  border-radius: 8px;
  overflow: hidden;
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
}

.stat-icon.repair {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.cost {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.technician {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.efficiency {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

/* 图表网格 */
.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.chart-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.filter-controls {
  display: flex;
  gap: 10px;
}

.chart-container {
  height: 300px;
  padding: 20px 0;
}

/* 工作负载卡片 */
.workload-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.workload-content {
  padding: 20px;
}

.workload-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.workload-item {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: #fafafa;
}

.workload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.workload-header h4 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.workload-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-bottom: 15px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.stat-item .label {
  color: #909399;
}

.stat-item .value {
  font-weight: 600;
  color: #303133;
}

/* 故障模式统计 */
.patterns-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.patterns-content {
  padding: 20px;
}

.patterns-table {
  margin-top: 10px;
}

/* 技师绩效统计 */
.performance-card {
  border-radius: 8px;
}

.performance-content {
  padding: 20px;
}

.performance-table {
  margin-top: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .analytics-page {
    padding: 15px;
  }

  .page-header h1 {
    font-size: 20px;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .filter-item {
    flex-direction: column;
    align-items: stretch;
    gap: 5px;
  }

  .filter-item label {
    font-size: 13px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .stat-content {
    padding: 15px;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    margin-right: 12px;
  }

  .stat-number {
    font-size: 24px;
  }

  .charts-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .chart-container {
    height: 250px;
    padding: 15px 0;
  }

  .card-header {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .filter-controls {
    flex-direction: column;
    gap: 8px;
  }

  .workload-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .workload-item {
    padding: 15px;
  }

  .workload-stats {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .patterns-content,
  .performance-content {
    padding: 15px;
  }

  :deep(.el-table) {
    font-size: 12px;
  }

  :deep(.el-table .el-table__cell) {
    padding: 8px 4px;
  }
}

@media (max-width: 480px) {
  .analytics-page {
    padding: 10px;
  }

  .page-header h1 {
    font-size: 18px;
  }

  .page-header p {
    font-size: 13px;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
    padding: 12px;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 8px;
    width: 40px;
    height: 40px;
  }

  .stat-number {
    font-size: 20px;
  }

  .stat-label {
    font-size: 12px;
  }

  .chart-container {
    height: 200px;
    padding: 10px 0;
  }

  .workload-item {
    padding: 12px;
  }

  .workload-header {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .patterns-content,
  .performance-content {
    padding: 10px;
  }

  :deep(.el-date-picker) {
    width: 100% !important;
  }

  :deep(.el-select) {
    width: 100% !important;
  }

  :deep(.el-button) {
    width: 100%;
  }

  :deep(.el-table) {
    font-size: 11px;
  }

  :deep(.el-table .el-table__cell) {
    padding: 6px 2px;
  }

  :deep(.el-table .el-table__header-wrapper) {
    font-size: 11px;
  }
}
</style>