package com.example.repository;

import com.example.entity.Vehicle;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface VehicleRepository extends JpaRepository<Vehicle, Long>, JpaSpecificationExecutor<Vehicle> {

    /**
     * 根据车牌号查找车辆
     */
    Optional<Vehicle> findByLicensePlate(String licensePlate);

    /**
     * 根据VIN码查找车辆
     */
    Optional<Vehicle> findByVin(String vin);

    /**
     * 检查车牌号是否存在
     */
    boolean existsByLicensePlate(String licensePlate);

    /**
     * 检查VIN码是否存在
     */
    boolean existsByVin(String vin);

    /**
     * 根据用户ID查找车辆列表
     */
    List<Vehicle> findByUserUserId(Long userId);

    /**
     * 根据用户ID查找车辆列表（分页）
     */
    Page<Vehicle> findByUserUserId(Long userId, Pageable pageable);

    /**
     * 根据品牌查找车辆（分页）
     */
    Page<Vehicle> findByBrand(String brand, Pageable pageable);

    /**
     * 根据品牌和型号查找车辆（分页）
     */
    Page<Vehicle> findByBrandAndModel(String brand, String model, Pageable pageable);

    /**
     * 搜索车辆（根据车牌号或品牌）
     */
    @Query("SELECT v FROM Vehicle v WHERE " +
           "LOWER(v.licensePlate) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(v.brand) LIKE LOWER(CONCAT('%', :search, '%'))")
    Page<Vehicle> searchByLicensePlateOrBrand(@Param("search") String search, Pageable pageable);

    /**
     * 搜索车辆（根据车牌号或品牌，包含品牌过滤）
     */
    @Query("SELECT v FROM Vehicle v WHERE " +
           "(LOWER(v.licensePlate) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(v.brand) LIKE LOWER(CONCAT('%', :search, '%'))) AND " +
           "v.brand = :brand")
    Page<Vehicle> searchByLicensePlateOrBrandAndBrand(@Param("search") String search,
                                                      @Param("brand") String brand,
                                                      Pageable pageable);

    /**
     * 根据用户ID和车辆ID查找车辆（用于权限验证）
     */
    Optional<Vehicle> findByVehicleIdAndUserUserId(Long vehicleId, Long userId);

    /**
     * 统计用户的车辆数量
     */
    long countByUserUserId(Long userId);

    /**
     * 根据用户ID删除所有车辆
     */
    void deleteByUserUserId(Long userId);

    /**
     * 根据年份范围查找车辆
     */
    @Query("SELECT v FROM Vehicle v WHERE v.year BETWEEN :startYear AND :endYear")
    Page<Vehicle> findByYearBetween(@Param("startYear") Integer startYear,
                                   @Param("endYear") Integer endYear,
                                   Pageable pageable);
}
