import api from './index'

export const orderAPI = {
  // 提交维修工单
  create(data) {
    return api.post('/orders', data)
  },

  // 获取工单详情
  getById(id) {
    return api.get(`/orders/${id}`)
  },

  // 更新工单状态（管理员专用）
  updateStatus(id, data) {
    return api.patch(`/orders/${id}/status`, data)
  },

  // 提交工单反馈
  submitFeedback(id, data) {
    return api.post(`/orders/${id}/feedback`, data)
  },



  // 技师接受工单
  accept(id) {
    return api.patch(`/orders/${id}/accept`)
  },

  // 技师拒绝工单
  reject(id, data) {
    return api.patch(`/orders/${id}/reject`, data)
  },

  // 开始维修工作
  start(id) {
    return api.patch(`/orders/${id}/start`)
  },

  // 完成维修工作
  complete(id, data) {
    return api.patch(`/orders/${id}/complete`, data)
  },

  // 添加材料使用记录
  addMaterial(id, data) {
    return api.post(`/orders/${id}/materials`, data)
  },

  // 获取工单材料使用记录
  getMaterials(id) {
    return api.get(`/orders/${id}/materials`)
  }
}
