package com.example.service;

import com.example.dto.response.*;
import com.example.repository.*;
import com.example.config.AppProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据统计分析服务
 */
@Service
@Transactional(readOnly = true)
public class AnalyticsService {

    @Autowired
    private RepairOrderRepository repairOrderRepository;

    @Autowired
    private OrderMaterialUsageRepository materialUsageRepository;

    @Autowired
    private LaborPaymentRepository laborPaymentRepository;

    @Autowired
    private OrderFeedbackRepository feedbackRepository;

    @Autowired
    private AppProperties appProperties;

    /**
     * 获取维修统计
     */
    public List<RepairStatisticsDTO> getRepairStatistics(LocalDate startDate, LocalDate endDate, String groupBy) {
        LocalDateTime startDateTime = startDate != null ? startDate.atStartOfDay() : LocalDateTime.now().minusMonths(12);
        LocalDateTime endDateTime = endDate != null ? endDate.atTime(23, 59, 59) : LocalDateTime.now();

        List<Object[]> results;

        if ("faultType".equals(groupBy)) {
            results = repairOrderRepository.getRepairStatsByFaultType(startDateTime, endDateTime);
        } else if ("model".equals(groupBy)) {
            results = repairOrderRepository.getRepairStatsByModel(startDateTime, endDateTime);
        } else {
            // 默认按品牌分组
            results = repairOrderRepository.getRepairStatsByBrand(startDateTime, endDateTime);
        }

        return results.stream().map(result -> {
            try {
                if ("faultType".equals(groupBy)) {
                    return new RepairStatisticsDTO(
                        null, // brand
                        null, // model
                        (String) result[0], // faultType
                        result[1] != null ? ((Number) result[1]).intValue() : 0, // repairCount
                        result[2] != null ? convertToBigDecimal(result[2]) : BigDecimal.ZERO, // avgRepairCost
                        result[3] != null ? convertToBigDecimal(result[3]) : BigDecimal.ZERO  // totalCost
                    );
                } else if ("model".equals(groupBy)) {
                    return new RepairStatisticsDTO(
                        (String) result[0], // brand
                        (String) result[1], // model
                        null, // faultType
                        result[2] != null ? ((Number) result[2]).intValue() : 0, // repairCount
                        result[3] != null ? convertToBigDecimal(result[3]) : BigDecimal.ZERO, // avgRepairCost
                        result[4] != null ? convertToBigDecimal(result[4]) : BigDecimal.ZERO  // totalCost
                    );
                } else {
                    // 按品牌分组
                    return new RepairStatisticsDTO(
                        (String) result[0], // brand
                        null, // model
                        null, // faultType
                        result[1] != null ? ((Number) result[1]).intValue() : 0, // repairCount
                        result[2] != null ? convertToBigDecimal(result[2]) : BigDecimal.ZERO, // avgRepairCost
                        result[3] != null ? convertToBigDecimal(result[3]) : BigDecimal.ZERO  // totalCost
                    );
                }
            } catch (Exception e) {
                // 记录错误并返回默认值
                System.err.println("Error processing repair statistics result: " + e.getMessage());
                return new RepairStatisticsDTO(
                    "未知", null, null, 0, BigDecimal.ZERO, BigDecimal.ZERO
                );
            }
        }).collect(Collectors.toList());
    }

    /**
     * 获取成本分析
     */
    public List<CostAnalysisDTO> getCostAnalysis(Integer year, Integer quarter) {
        if (year == null) {
            year = LocalDate.now().getYear();
        }

        BigDecimal totalLaborCost;
        BigDecimal totalMaterialCost;

        if (quarter != null) {
            // 季度分析
            int startMonth = (quarter - 1) * 3 + 1;
            int endMonth = quarter * 3;
            totalLaborCost = laborPaymentRepository.calculateQuarterlyTotalPayments(year, startMonth, endMonth);
            totalMaterialCost = materialUsageRepository.calculateQuarterlyMaterialCost(year, startMonth, endMonth);
        } else {
            // 年度分析
            totalLaborCost = laborPaymentRepository.calculateYearlyTotalPayments(year);
            totalMaterialCost = materialUsageRepository.calculateYearlyMaterialCost(year);
        }

        BigDecimal totalCost = totalLaborCost.add(totalMaterialCost);
        BigDecimal laborPercentage = totalCost.compareTo(BigDecimal.ZERO) > 0
            ? totalLaborCost.divide(totalCost, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100))
            : BigDecimal.ZERO;
        BigDecimal materialPercentage = totalCost.compareTo(BigDecimal.ZERO) > 0
            ? totalMaterialCost.divide(totalCost, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100))
            : BigDecimal.ZERO;

        List<CostAnalysisDTO> result = new ArrayList<>();
        result.add(new CostAnalysisDTO(
            year,
            quarter,
            totalLaborCost,
            totalMaterialCost,
            totalCost,
            laborPercentage,
            materialPercentage
        ));

        return result;
    }

    /**
     * 获取工作负载统计
     */
    public List<WorkloadStatisticsDTO> getWorkloadStatistics(LocalDate startDate, LocalDate endDate) {
        LocalDateTime startDateTime = startDate != null ? startDate.atStartOfDay() : LocalDateTime.now().minusMonths(3);
        LocalDateTime endDateTime = endDate != null ? endDate.atTime(23, 59, 59) : LocalDateTime.now();

        List<Object[]> results = repairOrderRepository.getWorkloadStatsBySpecialty(startDateTime, endDateTime);

        return results.stream().map(result -> {
            String specialty = (String) result[0];
            String specialtyName = getSpecialtyName(specialty);
            Integer assignedCount = ((Number) result[1]).intValue();
            Integer completedCount = ((Number) result[2]).intValue();
            BigDecimal avgWorkingHours = (BigDecimal) result[3];

            // 计算完成率
            BigDecimal completionRate = assignedCount > 0
                ? BigDecimal.valueOf(completedCount).divide(BigDecimal.valueOf(assignedCount), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100))
                : BigDecimal.ZERO;

            // 计算分配百分比（需要总数）
            int totalAssigned = results.stream().mapToInt(r -> ((Number) r[1]).intValue()).sum();
            BigDecimal assignmentPercentage = totalAssigned > 0
                ? BigDecimal.valueOf(assignedCount).divide(BigDecimal.valueOf(totalAssigned), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100))
                : BigDecimal.ZERO;

            return new WorkloadStatisticsDTO(
                specialty,
                specialtyName,
                assignedCount,
                completedCount,
                assignmentPercentage,
                completionRate,
                avgWorkingHours
            );
        }).collect(Collectors.toList());
    }

    /**
     * 获取故障模式统计
     */
    public List<PatternStatisticsDTO> getPatternStatistics(String brand, String model) {
        List<Object[]> results = repairOrderRepository.getFaultPatternStats(brand, model);

        // 计算总数用于百分比计算
        int totalOccurrences = results.stream().mapToInt(r -> ((Number) r[3]).intValue()).sum();

        return results.stream().map(result -> {
            String vehicleBrand = (String) result[0];
            String vehicleModel = (String) result[1];
            String faultType = (String) result[2];
            Integer occurrenceCount = ((Number) result[3]).intValue();

            BigDecimal faultPercentage = totalOccurrences > 0
                ? BigDecimal.valueOf(occurrenceCount).divide(BigDecimal.valueOf(totalOccurrences), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100))
                : BigDecimal.ZERO;

            return new PatternStatisticsDTO(
                vehicleBrand,
                vehicleModel,
                faultType,
                occurrenceCount,
                faultPercentage
            );
        }).collect(Collectors.toList());
    }

    /**
     * 获取技师绩效统计
     */
    public List<TechnicianPerformanceDTO> getTechnicianPerformance(String specialty, LocalDate startDate, LocalDate endDate) {
        LocalDateTime startDateTime = startDate != null ? startDate.atStartOfDay() : LocalDateTime.now().minusMonths(3);
        LocalDateTime endDateTime = endDate != null ? endDate.atTime(23, 59, 59) : LocalDateTime.now();

        List<Object[]> results = repairOrderRepository.getTechnicianPerformanceStats(specialty, startDateTime, endDateTime);

        return results.stream().map(result -> {
            Long technicianId = ((Number) result[0]).longValue();
            String realName = (String) result[1];
            String techSpecialty = (String) result[2];
            Integer completedOrders = ((Number) result[3]).intValue();
            BigDecimal totalWorkingHours = (BigDecimal) result[4];
            BigDecimal totalEarnings = (BigDecimal) result[5];

            // 获取平均评分
            BigDecimal avgRating = feedbackRepository.calculateAverageRatingByTechnicianId(technicianId);
            if (avgRating == null) {
                avgRating = BigDecimal.ZERO;
            }

            return new TechnicianPerformanceDTO(
                technicianId,
                realName,
                techSpecialty,
                completedOrders,
                totalWorkingHours,
                avgRating,
                totalEarnings
            );
        }).collect(Collectors.toList());
    }

    /**
     * 获取工种名称
     */
    private String getSpecialtyName(String specialtyCode) {
        return appProperties.getSpecialties().stream()
                .filter(s -> s.getCode().equals(specialtyCode))
                .findFirst()
                .map(AppProperties.Specialty::getName)
                .orElse(specialtyCode);
    }

    /**
     * 将对象转换为BigDecimal
     */
    private BigDecimal convertToBigDecimal(Object value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Number) {
            return BigDecimal.valueOf(((Number) value).doubleValue());
        }
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }
}
