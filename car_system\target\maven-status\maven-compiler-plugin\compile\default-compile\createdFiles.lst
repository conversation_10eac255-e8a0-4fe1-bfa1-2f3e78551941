com\example\entity\OrderStatusHistory.class
com\example\entity\User.class
com\example\dto\request\OrderCompletionRequest$MaterialUsageRequest.class
com\example\entity\LaborPayment$PaymentStatus.class
com\example\security\JwtAuthenticationEntryPoint.class
com\example\entity\User$UserType.class
com\example\controller\VehicleController.class
com\example\dto\response\LoginResponse.class
com\example\dto\response\OrderDTO$VehicleInfo.class
com\example\dto\response\RepairStatisticsDTO.class
com\example\dto\request\LoginRequest.class
com\example\dto\response\SpecialtyDTO.class
com\example\security\JwtAuthenticationFilter.class
com\example\controller\FaultTypeController.class
com\example\service\VehicleService.class
com\example\dto\request\OrderFeedbackRequest.class
com\example\exception\GlobalExceptionHandler.class
com\example\dto\response\OrderDTO$TechnicianInfo.class
com\example\security\JwtUtil.class
com\example\dto\response\MaterialDTO.class
com\example\repository\OrderStatusHistoryRepository.class
com\example\dto\request\OrderStatusUpdateRequest.class
com\example\service\DataInitializationService.class
com\example\config\AppProperties.class
com\example\security\JwtAuthenticationFilter$UserPrincipal.class
com\example\dto\request\FaultTypeRequest.class
com\example\dto\request\TechnicianRegistrationRequest.class
com\example\dto\response\ApiResponse$ErrorDetail.class
com\example\controller\TechnicianController.class
com\example\repository\OrderMaterialUsageRepository.class
com\example\dto\response\FaultTypeDTO.class
com\example\service\AuthService.class
com\example\dto\request\MaterialRequest.class
com\example\service\TestDataHelper.class
com\example\dto\response\CostAnalysisDTO.class
com\example\dto\response\UserDTO.class
com\example\repository\OrderFeedbackRepository.class
com\example\controller\AdminController.class
com\example\dto\request\OrderRequest.class
com\example\entity\BaseEntity.class
com\example\config\AppProperties$DefaultAdmin.class
com\example\dto\request\OrderCompletionRequest.class
com\example\entity\OrderFeedback.class
com\example\entity\LaborPayment.class
com\example\exception\BusinessException.class
com\example\entity\Technician.class
com\example\repository\MaterialRepository.class
com\example\service\AdminService.class
com\example\entity\OrderMaterialUsage.class
com\example\controller\OrderController$RejectRequest.class
com\example\VehicleRepairManagementApplication.class
com\example\exception\ResourceNotFoundException.class
com\example\service\MaterialService.class
com\example\dto\response\VehicleDTO.class
com\example\controller\AuthController.class
com\example\entity\RepairOrder$OrderStatus.class
com\example\dto\request\MaterialUsageRequest.class
com\example\dto\response\PatternStatisticsDTO.class
com\example\dto\response\PageResponse$PageInfo.class
com\example\config\AppProperties$Specialty.class
com\example\dto\response\OrderDTO$FaultTypeInfo.class
com\example\dto\request\VehicleRequest.class
com\example\service\TechnicianService.class
com\example\controller\OrderController.class
com\example\dto\request\UserRegistrationRequest.class
com\example\service\OrderService$1.class
com\example\service\UserService.class
com\example\service\AnalyticsService.class
com\example\dto\response\ApiResponse.class
com\example\controller\AnalyticsController.class
com\example\dto\response\LoginResponse$UserInfo.class
com\example\dto\response\WorkloadStatisticsDTO.class
com\example\dto\response\OrderDTO.class
com\example\repository\LaborPaymentRepository.class
com\example\controller\MaterialController.class
com\example\repository\FaultTypeRepository.class
com\example\dto\response\PageResponse.class
com\example\controller\UserController.class
com\example\entity\Technician$Specialty.class
com\example\entity\Material.class
com\example\repository\RepairOrderRepository.class
com\example\dto\response\PageResponse$PageData.class
com\example\service\TestDataInitializer.class
com\example\dto\response\TechnicianDTO.class
com\example\entity\RepairOrder.class
com\example\service\FaultTypeService.class
com\example\security\SecurityConfig.class
com\example\config\JwtProperties.class
com\example\repository\UserRepository.class
com\example\entity\RepairOrder$UrgencyLevel.class
com\example\dto\response\OrderDTO$UserInfo.class
com\example\dto\response\TechnicianPerformanceDTO.class
com\example\entity\RepairOrder$PaymentStatus.class
com\example\service\OrderService.class
com\example\entity\FaultType.class
com\example\entity\Vehicle.class
com\example\repository\TechnicianRepository.class
com\example\repository\VehicleRepository.class
