package com.example.dto.response;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 故障类型DTO
 */
public class FaultTypeDTO {

    private Long faultTypeId;
    private String typeName;
    private String description;
    private String specialty; // 主要工种（为了兼容前端）
    private List<String> requiredSpecialties;
    private Integer requiredTechCount;
    private Integer estimatedHours;
    private Integer status;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;

    public FaultTypeDTO() {}

    public FaultTypeDTO(Long faultTypeId, String typeName, String description,
                       String specialty, List<String> requiredSpecialties, Integer requiredTechCount,
                       Integer estimatedHours, Integer status,
                       LocalDateTime createTime, LocalDateTime updateTime) {
        this.faultTypeId = faultTypeId;
        this.typeName = typeName;
        this.description = description;
        this.specialty = specialty;
        this.requiredSpecialties = requiredSpecialties;
        this.requiredTechCount = requiredTechCount;
        this.estimatedHours = estimatedHours;
        this.status = status;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }

    public Long getFaultTypeId() {
        return faultTypeId;
    }

    public void setFaultTypeId(Long faultTypeId) {
        this.faultTypeId = faultTypeId;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSpecialty() {
        return specialty;
    }

    public void setSpecialty(String specialty) {
        this.specialty = specialty;
    }

    public List<String> getRequiredSpecialties() {
        return requiredSpecialties;
    }

    public void setRequiredSpecialties(List<String> requiredSpecialties) {
        this.requiredSpecialties = requiredSpecialties;
    }

    public Integer getRequiredTechCount() {
        return requiredTechCount;
    }

    public void setRequiredTechCount(Integer requiredTechCount) {
        this.requiredTechCount = requiredTechCount;
    }

    public Integer getEstimatedHours() {
        return estimatedHours;
    }

    public void setEstimatedHours(Integer estimatedHours) {
        this.estimatedHours = estimatedHours;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}
