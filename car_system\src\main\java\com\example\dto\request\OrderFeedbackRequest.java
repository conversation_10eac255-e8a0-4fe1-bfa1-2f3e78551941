package com.example.dto.request;

import jakarta.validation.constraints.*;

/**
 * 工单反馈请求DTO
 */
public class OrderFeedbackRequest {

    @NotNull(message = "评分不能为空")
    @Min(value = 1, message = "评分不能低于1分")
    @Max(value = 5, message = "评分不能高于5分")
    private Integer rating;

    @Size(max = 1000, message = "评价内容长度不能超过1000个字符")
    private String comment;

    public OrderFeedbackRequest() {}

    public OrderFeedbackRequest(Integer rating, String comment) {
        this.rating = rating;
        this.comment = comment;
    }

    public Integer getRating() {
        return rating;
    }

    public void setRating(Integer rating) {
        this.rating = rating;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }
}
