package com.example.controller;

import com.example.dto.request.UserRegistrationRequest;
import com.example.dto.response.ApiResponse;
import com.example.dto.response.PageResponse;
import com.example.dto.response.UserDTO;
import com.example.dto.response.TechnicianDTO;
import com.example.dto.response.VehicleDTO;
import com.example.dto.response.OrderDTO;
import com.example.service.AdminService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

/**
 * 管理员控制器
 */
@RestController
@RequestMapping("/admin")
@PreAuthorize("hasRole('ADMIN')")
public class AdminController {

    @Autowired
    private AdminService adminService;

    /**
     * 创建管理员账户
     */
    @PostMapping("/administrators")
    public ApiResponse<UserDTO> createAdministrator(@Valid @RequestBody UserRegistrationRequest request) {
        UserDTO admin = adminService.createAdministrator(request);
        return ApiResponse.success("管理员账户创建成功", admin);
    }

    /**
     * 获取所有用户
     */
    @GetMapping("/users")
    public ApiResponse<PageResponse<UserDTO>> getAllUsers(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) Integer status) {

        Pageable pageable = PageRequest.of(page - 1, size, Sort.by("createTime").descending());
        PageResponse<UserDTO> users = adminService.getAllUsers(search, status, pageable);
        return ApiResponse.success("获取用户列表成功", users);
    }

    /**
     * 获取所有技师
     */
    @GetMapping("/technicians")
    public ApiResponse<PageResponse<TechnicianDTO>> getAllTechnicians(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String specialty,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {

        Pageable pageable = PageRequest.of(page - 1, size, Sort.by("createTime").descending());
        PageResponse<TechnicianDTO> technicians = adminService.getAllTechnicians(search, specialty, status, startDate, endDate, pageable);
        return ApiResponse.success("获取技师列表成功", technicians);
    }

    /**
     * 获取所有车辆
     */
    @GetMapping("/vehicles")
    public ApiResponse<PageResponse<VehicleDTO>> getAllVehicles(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String brand) {

        Pageable pageable = PageRequest.of(page - 1, size, Sort.by("createTime").descending());
        PageResponse<VehicleDTO> vehicles = adminService.getAllVehicles(search, brand, pageable);
        return ApiResponse.success("获取车辆列表成功", vehicles);
    }

    /**
     * 获取所有工单
     */
    @GetMapping("/orders")
    public ApiResponse<PageResponse<OrderDTO>> getAllOrders(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) Long orderId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String urgencyLevel,
            @RequestParam(required = false) Long faultTypeId,
            @RequestParam(required = false) String specialty,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {

        Pageable pageable = PageRequest.of(page - 1, size, Sort.by("submitTime").descending());
        PageResponse<OrderDTO> orders = adminService.getAllOrders(orderId, status, urgencyLevel, faultTypeId, specialty, startDate, endDate, pageable);
        return ApiResponse.success("获取工单列表成功", orders);
    }

    /**
     * 禁用用户
     */
    @PutMapping("/users/{id}/disable")
    public ApiResponse<Void> disableUser(@PathVariable Long id) {
        adminService.disableUser(id);
        return ApiResponse.success("禁用用户成功", null);
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/users/{id}")
    public ApiResponse<Void> deleteUser(@PathVariable Long id) {
        adminService.deleteUser(id);
        return ApiResponse.success("删除用户成功", null);
    }

    /**
     * 启用用户
     */
    @PutMapping("/users/{id}/enable")
    public ApiResponse<Void> enableUser(@PathVariable Long id) {
        adminService.enableUser(id);
        return ApiResponse.success("启用用户成功", null);
    }

    /**
     * 删除技师
     */
    @DeleteMapping("/technicians/{id}")
    public ApiResponse<Void> deleteTechnician(@PathVariable Long id) {
        adminService.deleteTechnician(id);
        return ApiResponse.success("删除技师成功", null);
    }
}
