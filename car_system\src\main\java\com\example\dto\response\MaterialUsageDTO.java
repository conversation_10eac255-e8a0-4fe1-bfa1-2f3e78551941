package com.example.dto.response;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 材料使用记录DTO
 */
public class MaterialUsageDTO {

    private Long usageId;
    private Long orderId;
    private MaterialInfo material;
    private BigDecimal quantity;
    private BigDecimal totalPrice;
    private LocalDateTime useTime;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;

    public MaterialUsageDTO() {}

    public MaterialUsageDTO(Long usageId, Long orderId, MaterialInfo material, BigDecimal quantity,
                           BigDecimal totalPrice, LocalDateTime useTime, LocalDateTime createTime,
                           LocalDateTime updateTime) {
        this.usageId = usageId;
        this.orderId = orderId;
        this.material = material;
        this.quantity = quantity;
        this.totalPrice = totalPrice;
        this.useTime = useTime;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }

    public Long getUsageId() {
        return usageId;
    }

    public void setUsageId(Long usageId) {
        this.usageId = usageId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public MaterialInfo getMaterial() {
        return material;
    }

    public void setMaterial(MaterialInfo material) {
        this.material = material;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public LocalDateTime getUseTime() {
        return useTime;
    }

    public void setUseTime(LocalDateTime useTime) {
        this.useTime = useTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    // 内部类：材料信息
    public static class MaterialInfo {
        private Long materialId;
        private String materialName;
        private String specification;
        private String unit;
        private BigDecimal unitPrice;
        private String category;

        public MaterialInfo() {}

        public MaterialInfo(Long materialId, String materialName, String specification,
                          String unit, BigDecimal unitPrice, String category) {
            this.materialId = materialId;
            this.materialName = materialName;
            this.specification = specification;
            this.unit = unit;
            this.unitPrice = unitPrice;
            this.category = category;
        }

        public Long getMaterialId() {
            return materialId;
        }

        public void setMaterialId(Long materialId) {
            this.materialId = materialId;
        }

        public String getMaterialName() {
            return materialName;
        }

        public void setMaterialName(String materialName) {
            this.materialName = materialName;
        }

        public String getSpecification() {
            return specification;
        }

        public void setSpecification(String specification) {
            this.specification = specification;
        }

        public String getUnit() {
            return unit;
        }

        public void setUnit(String unit) {
            this.unit = unit;
        }

        public BigDecimal getUnitPrice() {
            return unitPrice;
        }

        public void setUnitPrice(BigDecimal unitPrice) {
            this.unitPrice = unitPrice;
        }

        public String getCategory() {
            return category;
        }

        public void setCategory(String category) {
            this.category = category;
        }
    }
}
