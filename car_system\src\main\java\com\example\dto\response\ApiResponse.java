package com.example.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 统一API响应格式
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse<T> {
    
    private boolean success;
    private String message;
    private T data;
    private List<ErrorDetail> errors;
    private Integer code;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    private LocalDateTime timestamp;
    
    private String path;
    
    public ApiResponse() {
        this.timestamp = LocalDateTime.now();
    }
    
    public ApiResponse(boolean success, String message, T data, Integer code) {
        this();
        this.success = success;
        this.message = message;
        this.data = data;
        this.code = code;
    }
    
    // 成功响应
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(true, "操作成功", data, 200);
    }
    
    public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<>(true, message, data, 200);
    }
    
    public static <T> ApiResponse<T> success(String message, T data, Integer code) {
        return new ApiResponse<>(true, message, data, code);
    }
    
    // 失败响应
    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>(false, message, null, 400);
    }
    
    public static <T> ApiResponse<T> error(String message, Integer code) {
        return new ApiResponse<>(false, message, null, code);
    }
    
    public static <T> ApiResponse<T> error(String message, List<ErrorDetail> errors, Integer code) {
        ApiResponse<T> response = new ApiResponse<>(false, message, null, code);
        response.setErrors(errors);
        return response;
    }
    
    // 创建响应
    public static <T> ApiResponse<T> created(T data) {
        return new ApiResponse<>(true, "创建成功", data, 201);
    }
    
    public static <T> ApiResponse<T> created(String message, T data) {
        return new ApiResponse<>(true, message, data, 201);
    }
    
    // 无内容响应
    public static <T> ApiResponse<T> noContent() {
        return new ApiResponse<>(true, "操作成功", null, 204);
    }
    
    public static <T> ApiResponse<T> noContent(String message) {
        return new ApiResponse<>(true, message, null, 204);
    }
    
    // 未找到响应
    public static <T> ApiResponse<T> notFound(String message) {
        return new ApiResponse<>(false, message, null, 404);
    }
    
    // 权限不足响应
    public static <T> ApiResponse<T> forbidden(String message) {
        return new ApiResponse<>(false, message, null, 403);
    }
    
    // 业务逻辑错误响应
    public static <T> ApiResponse<T> businessError(String message) {
        return new ApiResponse<>(false, message, null, 422);
    }
    
    // 冲突响应
    public static <T> ApiResponse<T> conflict(String message) {
        return new ApiResponse<>(false, message, null, 409);
    }
    
    // 服务器错误响应
    public static <T> ApiResponse<T> serverError(String message) {
        return new ApiResponse<>(false, message, null, 500);
    }
    
    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public T getData() {
        return data;
    }
    
    public void setData(T data) {
        this.data = data;
    }
    
    public List<ErrorDetail> getErrors() {
        return errors;
    }
    
    public void setErrors(List<ErrorDetail> errors) {
        this.errors = errors;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public void setCode(Integer code) {
        this.code = code;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getPath() {
        return path;
    }
    
    public void setPath(String path) {
        this.path = path;
    }
    
    /**
     * 错误详情类
     */
    public static class ErrorDetail {
        private String field;
        private String message;
        
        public ErrorDetail() {}
        
        public ErrorDetail(String field, String message) {
            this.field = field;
            this.message = message;
        }
        
        public String getField() {
            return field;
        }
        
        public void setField(String field) {
            this.field = field;
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
    }
}
