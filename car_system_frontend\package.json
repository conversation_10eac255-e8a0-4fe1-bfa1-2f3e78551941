{"name": "car_system_frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "car_system_frontend": "file:", "chart.js": "^4.4.1", "dayjs": "^1.11.10", "element-plus": "^2.4.4", "pinia": "^2.1.7", "vue": "^3.5.13", "vue-chartjs": "^5.3.0", "vue-router": "^4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "unplugin-auto-import": "^0.16.7", "unplugin-vue-components": "^0.25.2", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}