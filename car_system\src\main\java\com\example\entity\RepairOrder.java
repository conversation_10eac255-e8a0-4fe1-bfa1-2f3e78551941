package com.example.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "repair_orders")
public class RepairOrder extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "order_id")
    private Long orderId;

    @NotBlank(message = "故障描述不能为空")
    @Size(max = 1000, message = "故障描述长度不能超过1000个字符")
    @Column(name = "description", nullable = false, length = 1000)
    private String description;

    @NotNull(message = "紧急程度不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "urgency_level", nullable = false, length = 20)
    private UrgencyLevel urgencyLevel;

    @NotNull(message = "提交时间不能为空")
    @Column(name = "submit_time", nullable = false)
    private LocalDateTime submitTime;

    @Column(name = "preferred_time")
    private LocalDateTime preferredTime;

    @Column(name = "estimated_completion_time")
    private LocalDateTime estimatedCompletionTime;

    @Column(name = "actual_completion_time")
    private LocalDateTime actualCompletionTime;

    @NotNull(message = "工单状态不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private OrderStatus status = OrderStatus.PENDING;

    @Enumerated(EnumType.STRING)
    @Column(name = "payment_status", nullable = false, length = 20)
    private PaymentStatus paymentStatus = PaymentStatus.PAID;

    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "联系电话格式不正确")
    @Column(name = "contact_phone", length = 11)
    private String contactPhone;

    @Column(name = "total_labor_cost", precision = 10, scale = 2)
    private BigDecimal totalLaborCost = BigDecimal.ZERO;

    @Column(name = "total_material_cost", precision = 10, scale = 2)
    private BigDecimal totalMaterialCost = BigDecimal.ZERO;

    @Column(name = "total_cost", precision = 10, scale = 2)
    private BigDecimal totalCost = BigDecimal.ZERO;

    @Size(max = 1000, message = "工作结果长度不能超过1000个字符")
    @Column(name = "work_result", length = 1000)
    private String workResult;

    @DecimalMin(value = "0.0", message = "工作时长不能为负数")
    @Column(name = "working_hours", precision = 5, scale = 2)
    private BigDecimal workingHours;

    // 多对一关系：工单属于一个用户
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    // 多对一关系：工单对应一辆车
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "vehicle_id", nullable = false)
    private Vehicle vehicle;

    // 多对一关系：工单对应一个故障类型
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "fault_type_id", nullable = false)
    private FaultType faultType;

    // 一对多关系：工单的技师分配记录
    @OneToMany(mappedBy = "repairOrder", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<OrderTechnicianAssignment> technicianAssignments = new ArrayList<>();

    // 一对多关系：工单的材料使用记录
    @OneToMany(mappedBy = "repairOrder", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<OrderMaterialUsage> materialUsages = new ArrayList<>();

    // 一对多关系：工单的状态历史
    @OneToMany(mappedBy = "repairOrder", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<OrderStatusHistory> statusHistories = new ArrayList<>();

    // 一对一关系：工单反馈
    @OneToOne(mappedBy = "repairOrder", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private OrderFeedback feedback;

    // 枚举：紧急程度
    public enum UrgencyLevel {
        LOW("低"),
        NORMAL("普通"),
        HIGH("高"),
        URGENT("紧急");

        private final String displayName;

        UrgencyLevel(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    // 枚举：工单状态
    public enum OrderStatus {
        PENDING("待处理"),
        ASSIGNED("已分配"),
        ACCEPTED("已接受"),
        IN_PROGRESS("进行中"),
        COMPLETED("已完成"),
        CANCELLED("已取消");

        private final String displayName;

        OrderStatus(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    // 枚举：支付状态
    public enum PaymentStatus {
        UNPAID("未支付"),
        PAID("已支付"),
        REFUNDED("已退款");

        private final String displayName;

        PaymentStatus(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    // 构造函数
    public RepairOrder() {}

    public RepairOrder(String description, UrgencyLevel urgencyLevel, User user, Vehicle vehicle, FaultType faultType) {
        this.description = description;
        this.urgencyLevel = urgencyLevel;
        this.user = user;
        this.vehicle = vehicle;
        this.faultType = faultType;
        this.submitTime = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public UrgencyLevel getUrgencyLevel() {
        return urgencyLevel;
    }

    public void setUrgencyLevel(UrgencyLevel urgencyLevel) {
        this.urgencyLevel = urgencyLevel;
    }

    public LocalDateTime getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(LocalDateTime submitTime) {
        this.submitTime = submitTime;
    }

    public LocalDateTime getPreferredTime() {
        return preferredTime;
    }

    public void setPreferredTime(LocalDateTime preferredTime) {
        this.preferredTime = preferredTime;
    }

    public LocalDateTime getEstimatedCompletionTime() {
        return estimatedCompletionTime;
    }

    public void setEstimatedCompletionTime(LocalDateTime estimatedCompletionTime) {
        this.estimatedCompletionTime = estimatedCompletionTime;
    }

    public LocalDateTime getActualCompletionTime() {
        return actualCompletionTime;
    }

    public void setActualCompletionTime(LocalDateTime actualCompletionTime) {
        this.actualCompletionTime = actualCompletionTime;
    }

    public OrderStatus getStatus() {
        return status;
    }

    public void setStatus(OrderStatus status) {
        this.status = status;
    }

    public PaymentStatus getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(PaymentStatus paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public BigDecimal getTotalLaborCost() {
        return totalLaborCost;
    }

    public void setTotalLaborCost(BigDecimal totalLaborCost) {
        this.totalLaborCost = totalLaborCost;
    }

    public BigDecimal getTotalMaterialCost() {
        return totalMaterialCost;
    }

    public void setTotalMaterialCost(BigDecimal totalMaterialCost) {
        this.totalMaterialCost = totalMaterialCost;
    }

    public BigDecimal getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(BigDecimal totalCost) {
        this.totalCost = totalCost;
    }

    public String getWorkResult() {
        return workResult;
    }

    public void setWorkResult(String workResult) {
        this.workResult = workResult;
    }

    public BigDecimal getWorkingHours() {
        return workingHours;
    }

    public void setWorkingHours(BigDecimal workingHours) {
        this.workingHours = workingHours;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Vehicle getVehicle() {
        return vehicle;
    }

    public void setVehicle(Vehicle vehicle) {
        this.vehicle = vehicle;
    }

    public FaultType getFaultType() {
        return faultType;
    }

    public void setFaultType(FaultType faultType) {
        this.faultType = faultType;
    }

    public List<OrderTechnicianAssignment> getTechnicianAssignments() {
        return technicianAssignments;
    }

    public void setTechnicianAssignments(List<OrderTechnicianAssignment> technicianAssignments) {
        this.technicianAssignments = technicianAssignments;
    }

    // 便利方法：获取已分配的技师列表
    public List<Technician> getAssignedTechnicians() {
        return technicianAssignments.stream()
                .map(OrderTechnicianAssignment::getTechnician)
                .collect(java.util.stream.Collectors.toList());
    }

    // 便利方法：获取已同意的技师列表
    public List<Technician> getAcceptedTechnicians() {
        return technicianAssignments.stream()
                .filter(assignment -> assignment.getAgreementStatus() == OrderTechnicianAssignment.AgreementStatus.ACCEPTED)
                .map(OrderTechnicianAssignment::getTechnician)
                .collect(java.util.stream.Collectors.toList());
    }

    public List<OrderMaterialUsage> getMaterialUsages() {
        return materialUsages;
    }

    public void setMaterialUsages(List<OrderMaterialUsage> materialUsages) {
        this.materialUsages = materialUsages;
    }

    public List<OrderStatusHistory> getStatusHistories() {
        return statusHistories;
    }

    public void setStatusHistories(List<OrderStatusHistory> statusHistories) {
        this.statusHistories = statusHistories;
    }

    public OrderFeedback getFeedback() {
        return feedback;
    }

    public void setFeedback(OrderFeedback feedback) {
        this.feedback = feedback;
    }
}
