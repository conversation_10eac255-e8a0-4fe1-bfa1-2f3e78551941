import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// 路由配置
const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/Register.vue'),
    meta: { requiresAuth: false }
  },
  // 用户端路由
  {
    path: '/user',
    component: () => import('@/layouts/UserLayout.vue'),
    meta: { requiresAuth: true, userType: 'user' },
    children: [
      {
        path: '',
        redirect: '/user/dashboard'
      },
      {
        path: 'dashboard',
        name: 'UserDashboard',
        component: () => import('@/views/user/Dashboard.vue')
      },
      {
        path: 'profile',
        name: 'UserProfile',
        component: () => import('@/views/user/Profile.vue')
      },
      {
        path: 'vehicles',
        name: 'UserVehicles',
        component: () => import('@/views/user/Vehicles.vue')
      },
      {
        path: 'orders',
        name: 'UserOrders',
        component: () => import('@/views/user/Orders.vue')
      },
      {
        path: 'orders/create',
        name: 'CreateOrder',
        component: () => import('@/views/user/CreateOrder.vue')
      },
      {
        path: 'orders/:id',
        name: 'OrderDetail',
        component: () => import('@/views/user/OrderDetail.vue')
      },
      {
        path: 'repair-history',
        name: 'RepairHistory',
        component: () => import('@/views/user/RepairHistory.vue')
      },
      {
        path: 'feedback-history',
        name: 'FeedbackHistory',
        component: () => import('@/views/user/FeedbackHistory.vue')
      }
    ]
  },
  // 技师端路由
  {
    path: '/technician',
    component: () => import('@/layouts/TechnicianLayout.vue'),
    meta: { requiresAuth: true, userType: 'technician' },
    children: [
      {
        path: '',
        redirect: '/technician/dashboard'
      },
      {
        path: 'dashboard',
        name: 'TechnicianDashboard',
        component: () => import('@/views/technician/Dashboard.vue')
      },
      {
        path: 'profile',
        name: 'TechnicianProfile',
        component: () => import('@/views/technician/Profile.vue')
      },
      {
        path: 'tasks',
        name: 'TechnicianTasks',
        component: () => import('@/views/technician/Tasks.vue')
      },
      {
        path: 'tasks/:id',
        name: 'TaskDetail',
        component: () => import('@/views/technician/TaskDetail.vue')
      },
      {
        path: 'payments',
        name: 'TechnicianPayments',
        component: () => import('@/views/technician/Payments.vue')
      },
      {
        path: 'history',
        name: 'TechnicianHistory',
        component: () => import('@/views/technician/History.vue')
      }
    ]
  },
  // 管理员端路由
  {
    path: '/admin',
    component: () => import('@/layouts/AdminLayout.vue'),
    meta: { requiresAuth: true, userType: 'admin' },
    children: [
      {
        path: '',
        redirect: '/admin/dashboard'
      },
      {
        path: 'dashboard',
        name: 'AdminDashboard',
        component: () => import('@/views/admin/Dashboard.vue')
      },
      {
        path: 'users',
        name: 'AdminUsers',
        component: () => import('@/views/admin/Users.vue')
      },
      {
        path: 'technicians',
        name: 'AdminTechnicians',
        component: () => import('@/views/admin/Technicians.vue')
      },
      {
        path: 'vehicles',
        name: 'AdminVehicles',
        component: () => import('@/views/admin/Vehicles.vue')
      },
      {
        path: 'orders',
        name: 'AdminOrders',
        component: () => import('@/views/admin/Orders.vue')
      },
      {
        path: 'fault-types',
        name: 'AdminFaultTypes',
        component: () => import('@/views/admin/FaultTypes.vue')
      },
      {
        path: 'materials',
        name: 'AdminMaterials',
        component: () => import('@/views/admin/Materials.vue')
      },
      {
        path: 'analytics',
        name: 'AdminAnalytics',
        component: () => import('@/views/admin/Analytics.vue')
      }
    ]
  },
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    if (!authStore.isLoggedIn) {
      next('/login')
      return
    }

    // 检查用户类型权限
    if (to.meta.userType && to.meta.userType !== authStore.userType) {
      // 根据用户类型重定向到对应的首页
      switch (authStore.userType) {
        case 'user':
          next('/user/dashboard')
          break
        case 'technician':
          next('/technician/dashboard')
          break
        case 'admin':
          next('/admin/dashboard')
          break
        default:
          next('/login')
      }
      return
    }
  }

  // 如果已登录用户访问登录页，重定向到对应首页
  if ((to.path === '/login' || to.path === '/register') && authStore.isLoggedIn) {
    switch (authStore.userType) {
      case 'user':
        next('/user/dashboard')
        break
      case 'technician':
        next('/technician/dashboard')
        break
      case 'admin':
        next('/admin/dashboard')
        break
      default:
        next()
    }
    return
  }

  next()
})

export default router
