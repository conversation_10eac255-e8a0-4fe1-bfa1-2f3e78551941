<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>欢迎回来，{{ user?.realName || user?.username }}</h1>
      <p>这里是您的个人仪表盘</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon vehicle">
            <el-icon size="24"><Van /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.vehicleCount }}</div>
            <div class="stat-label">我的车辆</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon order">
            <el-icon size="24"><Document /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.orderCount }}</div>
            <div class="stat-label">维修工单</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon pending">
            <el-icon size="24"><Clock /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.pendingCount }}</div>
            <div class="stat-label">待处理工单</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon completed">
            <el-icon size="24"><Check /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.completedCount }}</div>
            <div class="stat-label">已完成工单</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <h2>快速操作</h2>
      <div class="action-grid">
        <el-card class="action-card" @click="$router.push('/user/orders/create')">
          <div class="action-content">
            <el-icon size="32" color="#409eff"><Plus /></el-icon>
            <div class="action-title">提交工单</div>
            <div class="action-desc">报修车辆故障</div>
          </div>
        </el-card>

        <el-card class="action-card" @click="$router.push('/user/vehicles')">
          <div class="action-content">
            <el-icon size="32" color="#67c23a"><Van /></el-icon>
            <div class="action-title">管理车辆</div>
            <div class="action-desc">添加或编辑车辆信息</div>
          </div>
        </el-card>

        <el-card class="action-card" @click="$router.push('/user/orders')">
          <div class="action-content">
            <el-icon size="32" color="#e6a23c"><Document /></el-icon>
            <div class="action-title">查看工单</div>
            <div class="action-desc">查看工单状态和进度</div>
          </div>
        </el-card>

        <el-card class="action-card" @click="$router.push('/user/repair-history')">
          <div class="action-content">
            <el-icon size="32" color="#909399"><Clock /></el-icon>
            <div class="action-title">维修历史</div>
            <div class="action-desc">查看历史维修记录</div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 最近工单 -->
    <div class="recent-orders">
      <h2>最近工单</h2>
      <el-card>
        <el-table
          v-loading="ordersLoading"
          :data="recentOrders"
          style="width: 100%"
        >
          <el-table-column prop="orderId" label="工单号" width="100" />
          <el-table-column prop="vehicle.licensePlate" label="车牌号" width="120" />
          <el-table-column prop="faultType.typeName" label="故障类型" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="submitTime" label="提交时间" width="180">
            <template #default="{ row }">
              {{ formatDate(row.submitTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="{ row }">
              <el-button
                type="text"
                size="small"
                @click="$router.push(`/user/orders/${row.orderId}`)"
              >
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div v-if="!ordersLoading && recentOrders.length === 0" class="empty-state">
          <el-empty description="暂无工单记录" />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Van, Document, Clock, Check, Plus
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { userAPI } from '@/api/user'
import dayjs from 'dayjs'

const authStore = useAuthStore()

// 计算属性
const user = computed(() => authStore.user)

// 响应式数据
const stats = reactive({
  vehicleCount: 0,
  orderCount: 0,
  pendingCount: 0,
  completedCount: 0
})

const recentOrders = ref([])
const ordersLoading = ref(false)

// 获取统计数据
const fetchStats = async () => {
  try {
    // 获取车辆数量
    const vehiclesResponse = await userAPI.getCurrentUserVehicles({ page: 1, size: 1 })
    stats.vehicleCount = vehiclesResponse.data.data?.page?.totalElements || 0

    // 获取工单统计
    const ordersResponse = await userAPI.getCurrentUserOrders({ page: 1, size: 1 })
    stats.orderCount = ordersResponse.data.data?.page?.totalElements || 0

    // 获取待处理工单数量
    const pendingResponse = await userAPI.getCurrentUserOrders({
      page: 1,
      size: 1,
      status: 'pending,assigned,accepted,in_progress'
    })
    stats.pendingCount = pendingResponse.data.data?.page?.totalElements || 0

    // 获取已完成工单数量
    const completedResponse = await userAPI.getCurrentUserOrders({
      page: 1,
      size: 1,
      status: 'completed'
    })
    stats.completedCount = completedResponse.data.data?.page?.totalElements || 0
  } catch (error) {
    console.error('Failed to fetch stats:', error)
  }
}

// 获取最近工单
const fetchRecentOrders = async () => {
  try {
    ordersLoading.value = true
    const response = await userAPI.getCurrentUserOrders({ page: 1, size: 5 })
    recentOrders.value = response.data.data?.content || []
  } catch (error) {
    console.error('Failed to fetch recent orders:', error)
    ElMessage.error('获取工单列表失败')
  } finally {
    ordersLoading.value = false
  }
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    pending: 'warning',
    assigned: 'info',
    accepted: 'primary',
    in_progress: 'warning',
    completed: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    pending: '待处理',
    assigned: '已分配',
    accepted: '已接受',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

// 格式化日期
const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchStats()
  fetchRecentOrders()
})
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 30px;
}

.dashboard-header h1 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 28px;
  font-weight: 600;
}

.dashboard-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  cursor: default;
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: white;
}

.stat-icon.vehicle {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.order {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.pending {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  line-height: 1;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

/* 快速操作 */
.quick-actions {
  margin-bottom: 30px;
}

.quick-actions h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.action-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.action-content {
  text-align: center;
  padding: 20px;
}

.action-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 15px 0 5px 0;
}

.action-desc {
  font-size: 14px;
  color: #666;
}

/* 最近工单 */
.recent-orders h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.empty-state {
  padding: 40px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .action-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .stat-number {
    font-size: 24px;
  }

  .dashboard-header h1 {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .action-grid {
    grid-template-columns: 1fr;
  }

  .action-content {
    padding: 15px;
  }
}
</style>
