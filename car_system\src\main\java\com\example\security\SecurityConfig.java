package com.example.security;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Autowired
    private JwtAuthenticationFilter jwtAuthenticationFilter;

    @Autowired
    private JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.cors(cors -> cors.configurationSource(corsConfigurationSource()))
            .csrf(csrf -> csrf.disable())
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .exceptionHandling(ex -> ex.authenticationEntryPoint(jwtAuthenticationEntryPoint))
            .authorizeHttpRequests(authz -> authz
                // 公开接口
                .requestMatchers("/auth/login").permitAll()
                .requestMatchers(HttpMethod.POST, "/users").permitAll() // 用户注册
                .requestMatchers(HttpMethod.POST, "/technicians").permitAll() // 技师注册
                .requestMatchers(HttpMethod.GET, "/technicians/specialties").permitAll() // 获取工种列表
                .requestMatchers("/h2-console/**").permitAll() // H2控制台
                .requestMatchers("/actuator/**").permitAll() // 监控端点

                // 认证后的公共接口
                .requestMatchers("/auth/logout", "/auth/refresh").authenticated()
                .requestMatchers(HttpMethod.GET, "/fault-types/**").authenticated() // 故障类型查询

                // 用户接口
                .requestMatchers("/users/me/**").hasRole("USER")
                .requestMatchers(HttpMethod.PUT, "/users/me").hasRole("USER")
                .requestMatchers("/vehicles/**").hasRole("USER")
                .requestMatchers(HttpMethod.POST, "/orders").hasRole("USER")
                .requestMatchers("/orders/*/feedback").hasRole("USER")

                // 技师接口
                .requestMatchers("/technicians/me/**").hasRole("TECHNICIAN")
                .requestMatchers(HttpMethod.PUT, "/technicians/me").hasRole("TECHNICIAN")
                .requestMatchers("/orders/*/accept", "/orders/*/reject",
                               "/orders/*/start", "/orders/*/complete").hasRole("TECHNICIAN")
                .requestMatchers("/orders/*/materials").hasAnyRole("TECHNICIAN", "ADMIN")
                .requestMatchers("/materials/**").hasAnyRole("TECHNICIAN", "ADMIN")

                // 管理员接口
                .requestMatchers("/admin/**").hasRole("ADMIN")
                .requestMatchers("/analytics/**").hasRole("ADMIN")
                .requestMatchers(HttpMethod.POST, "/fault-types").hasRole("ADMIN")
                .requestMatchers(HttpMethod.PUT, "/fault-types/**").hasRole("ADMIN")
                .requestMatchers(HttpMethod.DELETE, "/fault-types/**").hasRole("ADMIN")
                .requestMatchers(HttpMethod.POST, "/materials").hasRole("ADMIN")
                .requestMatchers(HttpMethod.PUT, "/materials/**").hasRole("ADMIN")
                .requestMatchers(HttpMethod.DELETE, "/materials/**").hasRole("ADMIN")
                .requestMatchers(HttpMethod.PATCH, "/orders/*/status").hasRole("ADMIN")

                // 其他需要认证的接口
                .requestMatchers("/orders/**").authenticated()

                // 其他所有请求都需要认证
                .anyRequest().authenticated()
            );

        // 添加JWT过滤器
        http.addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        // 禁用H2控制台的frame选项
        http.headers(headers -> headers.frameOptions(frameOptions -> frameOptions.disable()));

        return http.build();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(Arrays.asList("*"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
