package com.example.controller;

import com.example.config.TestSecurityConfig;
import com.example.dto.request.VehicleRequest;
import com.example.dto.response.VehicleDTO;
import com.example.security.JwtAuthenticationFilter;
import com.example.service.VehicleService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(VehicleController.class)
@Import(TestSecurityConfig.class)
class VehicleControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private VehicleService vehicleService;

    @Autowired
    private ObjectMapper objectMapper;

    private VehicleRequest vehicleRequest;
    private VehicleDTO vehicleDTO;

    @BeforeEach
    void setUp() {
        // 创建车辆请求
        vehicleRequest = new VehicleRequest();
        vehicleRequest.setLicensePlate("京A12345");
        vehicleRequest.setVin("1HGBH41JXMN109186");
        vehicleRequest.setBrand("Toyota");
        vehicleRequest.setModel("Camry");
        vehicleRequest.setYear(2020);
        vehicleRequest.setColor("白色");
        vehicleRequest.setEngineNumber("ENG123456");

        // 创建车辆DTO
        vehicleDTO = new VehicleDTO(
                1L, 1L, "京A12345", "Toyota", "Camry",
                2020, "1HGBH41JXMN109186", "白色", "ENG123456",
                java.time.LocalDate.of(2020, 1, 1), LocalDateTime.now(), LocalDateTime.now()
        );
    }

    @Test
    @WithMockUser(roles = "USER")
    void testAddVehicle_Success() throws Exception {
        // Given
        when(vehicleService.addVehicle(eq(1L), any(VehicleRequest.class))).thenReturn(vehicleDTO);

        // When & Then
        mockMvc.perform(post("/vehicles")
                        .with(csrf())
                        .requestAttr("userPrincipal",
                                new JwtAuthenticationFilter.UserPrincipal("testuser", "USER", 1L))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(vehicleRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("添加车辆成功"))
                .andExpect(jsonPath("$.data.vehicleId").value(1))
                .andExpect(jsonPath("$.data.licensePlate").value("京A12345"))
                .andExpect(jsonPath("$.data.brand").value("Toyota"))
                .andExpect(jsonPath("$.data.model").value("Camry"));

        verify(vehicleService).addVehicle(eq(1L), any(VehicleRequest.class));
    }

    @Test
    @WithMockUser(roles = "USER")
    void testAddVehicle_InvalidInput() throws Exception {
        // Given
        vehicleRequest.setLicensePlate(null); // 缺少必填字段

        // When & Then
        mockMvc.perform(post("/vehicles")
                        .with(csrf())
                        .requestAttr("userPrincipal",
                                createUserPrincipal("testuser", "USER", 1L))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(vehicleRequest)))
                .andExpect(status().isBadRequest());

        verify(vehicleService, never()).addVehicle(anyLong(), any(VehicleRequest.class));
    }

    @Test
    @WithMockUser(roles = "USER")
    void testGetVehicle_Success() throws Exception {
        // Given
        when(vehicleService.getVehicle(1L, 1L)).thenReturn(vehicleDTO);

        // When & Then
        mockMvc.perform(get("/vehicles/1")
                        .requestAttr("userPrincipal",
                                createUserPrincipal("testuser", "USER", 1L)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取车辆信息成功"))
                .andExpect(jsonPath("$.data.vehicleId").value(1))
                .andExpect(jsonPath("$.data.licensePlate").value("京A12345"));

        verify(vehicleService).getVehicle(1L, 1L);
    }

    @Test
    @WithMockUser(roles = "USER")
    void testUpdateVehicle_Success() throws Exception {
        // Given
        when(vehicleService.updateVehicle(eq(1L), eq(1L), any(VehicleRequest.class)))
                .thenReturn(vehicleDTO);

        // When & Then
        mockMvc.perform(put("/vehicles/1")
                        .with(csrf())
                        .requestAttr("userPrincipal",
                                createUserPrincipal("testuser", "USER", 1L))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(vehicleRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("更新车辆信息成功"))
                .andExpect(jsonPath("$.data.vehicleId").value(1));

        verify(vehicleService).updateVehicle(eq(1L), eq(1L), any(VehicleRequest.class));
    }

    @Test
    @WithMockUser(roles = "USER")
    void testDeleteVehicle_Success() throws Exception {
        // Given
        doNothing().when(vehicleService).deleteVehicle(1L, 1L);

        // When & Then
        mockMvc.perform(delete("/vehicles/1")
                        .with(csrf())
                        .requestAttr("userPrincipal",
                                createUserPrincipal("testuser", "USER", 1L)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("删除车辆成功"))
                .andExpect(jsonPath("$.data").isEmpty());

        verify(vehicleService).deleteVehicle(1L, 1L);
    }

    @Test
    void testAddVehicle_Unauthorized() throws Exception {
        // When & Then
        mockMvc.perform(post("/vehicles")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(vehicleRequest)))
                .andExpect(status().isUnauthorized());

        verify(vehicleService, never()).addVehicle(anyLong(), any(VehicleRequest.class));
    }

    @Test
    void testGetVehicle_Unauthorized() throws Exception {
        // When & Then
        mockMvc.perform(get("/vehicles/1"))
                .andExpect(status().isUnauthorized());

        verify(vehicleService, never()).getVehicle(anyLong(), anyLong());
    }

    @Test
    @WithMockUser(roles = "ADMIN") // 错误的角色
    void testAddVehicle_WrongRole() throws Exception {
        // When & Then
        mockMvc.perform(post("/vehicles")
                        .with(csrf())
                        .requestAttr("userPrincipal",
                                createUserPrincipal("admin", "ADMIN", 1L))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(vehicleRequest)))
                .andExpect(status().isForbidden());

        verify(vehicleService, never()).addVehicle(anyLong(), any(VehicleRequest.class));
    }

    @Test
    @WithMockUser(roles = "USER")
    void testAddVehicle_MissingVin() throws Exception {
        // Given
        vehicleRequest.setVin(null);

        // When & Then
        mockMvc.perform(post("/vehicles")
                        .with(csrf())
                        .requestAttr("userPrincipal",
                                createUserPrincipal("testuser", "USER", 1L))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(vehicleRequest)))
                .andExpect(status().isBadRequest());

        verify(vehicleService, never()).addVehicle(anyLong(), any(VehicleRequest.class));
    }

    @Test
    @WithMockUser(roles = "USER")
    void testAddVehicle_MissingBrand() throws Exception {
        // Given
        vehicleRequest.setBrand(null);

        // When & Then
        mockMvc.perform(post("/vehicles")
                        .with(csrf())
                        .requestAttr("userPrincipal",
                                createUserPrincipal("testuser", "USER", 1L))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(vehicleRequest)))
                .andExpect(status().isBadRequest());

        verify(vehicleService, never()).addVehicle(anyLong(), any(VehicleRequest.class));
    }

    @Test
    @WithMockUser(roles = "USER")
    void testAddVehicle_MissingModel() throws Exception {
        // Given
        vehicleRequest.setModel(null);

        // When & Then
        mockMvc.perform(post("/vehicles")
                        .with(csrf())
                        .requestAttr("userPrincipal",
                                createUserPrincipal("testuser", "USER", 1L))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(vehicleRequest)))
                .andExpect(status().isBadRequest());

        verify(vehicleService, never()).addVehicle(anyLong(), any(VehicleRequest.class));
    }

    @Test
    @WithMockUser(roles = "USER")
    void testUpdateVehicle_InvalidInput() throws Exception {
        // Given
        vehicleRequest.setYear(null); // 缺少必填字段

        // When & Then
        mockMvc.perform(put("/vehicles/1")
                        .with(csrf())
                        .requestAttr("userPrincipal",
                                createUserPrincipal("testuser", "USER", 1L))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(vehicleRequest)))
                .andExpect(status().isBadRequest());

        verify(vehicleService, never()).updateVehicle(anyLong(), anyLong(), any(VehicleRequest.class));
    }

    @Test
    @WithMockUser(roles = "USER")
    void testGetVehicle_InvalidId() throws Exception {
        // When & Then
        mockMvc.perform(get("/vehicles/invalid")
                        .requestAttr("userPrincipal",
                                createUserPrincipal("testuser", "USER", 1L)))
                .andExpect(status().isBadRequest());

        verify(vehicleService, never()).getVehicle(anyLong(), anyLong());
    }

    @Test
    @WithMockUser(roles = "USER")
    void testDeleteVehicle_InvalidId() throws Exception {
        // When & Then
        mockMvc.perform(delete("/vehicles/invalid")
                        .with(csrf())
                        .requestAttr("userPrincipal",
                                createUserPrincipal("testuser", "USER", 1L)))
                .andExpect(status().isBadRequest());

        verify(vehicleService, never()).deleteVehicle(anyLong(), anyLong());
    }

    /**
     * 创建UserPrincipal对象的helper方法
     */
    private JwtAuthenticationFilter.UserPrincipal createUserPrincipal(String username, String userType, Long userId) {
        return new JwtAuthenticationFilter.UserPrincipal(username, userType, userId);
    }
}
