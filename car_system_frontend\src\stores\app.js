import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 侧边栏状态
  const sidebarCollapsed = ref(false)
  
  // 移动端侧边栏状态
  const mobileSidebarOpen = ref(false)
  
  // 页面加载状态
  const loading = ref(false)
  
  // 设备类型
  const isMobile = ref(window.innerWidth < 768)
  
  // 切换侧边栏
  const toggleSidebar = () => {
    if (isMobile.value) {
      mobileSidebarOpen.value = !mobileSidebarOpen.value
    } else {
      sidebarCollapsed.value = !sidebarCollapsed.value
    }
  }
  
  // 关闭移动端侧边栏
  const closeMobileSidebar = () => {
    mobileSidebarOpen.value = false
  }
  
  // 设置加载状态
  const setLoading = (status) => {
    loading.value = status
  }
  
  // 更新设备类型
  const updateDeviceType = () => {
    isMobile.value = window.innerWidth < 768
    if (!isMobile.value) {
      mobileSidebarOpen.value = false
    }
  }
  
  // 监听窗口大小变化
  window.addEventListener('resize', updateDeviceType)
  
  return {
    // 状态
    sidebarCollapsed,
    mobileSidebarOpen,
    loading,
    isMobile,
    
    // 方法
    toggleSidebar,
    closeMobileSidebar,
    setLoading,
    updateDeviceType
  }
})
