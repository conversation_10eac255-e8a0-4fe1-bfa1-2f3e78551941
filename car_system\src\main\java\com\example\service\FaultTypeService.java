package com.example.service;

import com.example.dto.request.FaultTypeRequest;
import com.example.dto.response.FaultTypeDTO;
import com.example.dto.response.PageResponse;
import com.example.entity.FaultType;
import com.example.entity.Technician;
import com.example.exception.BusinessException;
import com.example.exception.ResourceNotFoundException;
import com.example.repository.FaultTypeRepository;
import com.example.config.AppProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 故障类型服务
 */
@Service
@Transactional
public class FaultTypeService {

    @Autowired
    private FaultTypeRepository faultTypeRepository;

    @Autowired
    private AppProperties appProperties;

    /**
     * 获取故障类型列表
     */
    @Transactional(readOnly = true)
    public List<FaultTypeDTO> getFaultTypes(Integer status) {
        List<FaultType> faultTypes;
        if (status != null) {
            faultTypes = faultTypeRepository.findByStatus(status);
        } else {
            faultTypes = faultTypeRepository.findByStatus(1); // 默认只返回启用的
        }

        return faultTypes.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 获取故障类型列表（分页）
     */
    @Transactional(readOnly = true)
    public PageResponse<FaultTypeDTO> getFaultTypes(String search, String specialty, Integer status, Pageable pageable) {
        Specification<FaultType> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 状态过滤
            if (status != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), status));
            } else {
                predicates.add(criteriaBuilder.equal(root.get("status"), 1)); // 默认只返回启用的
            }

            // 搜索过滤
            if (search != null && !search.trim().isEmpty()) {
                String searchPattern = "%" + search.trim() + "%";
                Predicate namePredicate = criteriaBuilder.like(root.get("typeName"), searchPattern);
                Predicate descPredicate = criteriaBuilder.like(root.get("description"), searchPattern);
                predicates.add(criteriaBuilder.or(namePredicate, descPredicate));
            }

            // 工种过滤 - 这个比较复杂，暂时跳过，因为需要连接到关联表
            // 如果需要实现，可以后续添加

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        Page<FaultType> faultTypePage = faultTypeRepository.findAll(spec, pageable);
        List<FaultTypeDTO> faultTypeDTOs = faultTypePage.getContent().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        return PageResponse.success(faultTypeDTOs, new PageResponse.PageInfo(
                faultTypePage.getNumber() + 1,
                faultTypePage.getSize(),
                faultTypePage.getTotalElements(),
                faultTypePage.getTotalPages()
        ));
    }

    /**
     * 获取故障类型详情
     */
    @Transactional(readOnly = true)
    public FaultTypeDTO getFaultType(Long id) {
        FaultType faultType = faultTypeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("故障类型不存在"));
        return convertToDTO(faultType);
    }

    /**
     * 创建故障类型
     */
    public FaultTypeDTO createFaultType(FaultTypeRequest request) {
        // 验证工种代码
        validateSpecialties(request.getRequiredSpecialties());

        // 检查故障类型名称是否已存在
        if (faultTypeRepository.existsByTypeName(request.getTypeName())) {
            throw new BusinessException("故障类型名称已存在");
        }

        FaultType faultType = new FaultType();
        faultType.setTypeName(request.getTypeName());
        faultType.setDescription(request.getDescription());
        faultType.setRequiredSpecialties(new HashSet<>(convertSpecialtiesToEnum(request.getRequiredSpecialties())));
        faultType.setRequiredTechCount(request.getRequiredTechCount());
        faultType.setEstimatedHours(request.getEstimatedHours().intValue());
        faultType.setStatus(1); // 默认启用

        FaultType savedFaultType = faultTypeRepository.save(faultType);
        return convertToDTO(savedFaultType);
    }

    /**
     * 更新故障类型
     */
    public FaultTypeDTO updateFaultType(Long id, FaultTypeRequest request) {
        FaultType faultType = faultTypeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("故障类型不存在"));

        // 验证工种代码
        validateSpecialties(request.getRequiredSpecialties());

        // 检查故障类型名称是否已被其他记录使用
        if (!faultType.getTypeName().equals(request.getTypeName()) &&
            faultTypeRepository.existsByTypeName(request.getTypeName())) {
            throw new BusinessException("故障类型名称已存在");
        }

        faultType.setTypeName(request.getTypeName());
        faultType.setDescription(request.getDescription());
        faultType.setRequiredSpecialties(new HashSet<>(convertSpecialtiesToEnum(request.getRequiredSpecialties())));
        faultType.setRequiredTechCount(request.getRequiredTechCount());
        faultType.setEstimatedHours(request.getEstimatedHours().intValue());

        FaultType savedFaultType = faultTypeRepository.save(faultType);
        return convertToDTO(savedFaultType);
    }

    /**
     * 删除故障类型（软删除）
     */
    public void deleteFaultType(Long id) {
        FaultType faultType = faultTypeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("故障类型不存在"));

        faultType.setStatus(0); // 软删除
        faultTypeRepository.save(faultType);
    }

    /**
     * 验证工种代码
     */
    private void validateSpecialties(List<String> specialties) {
        List<String> validSpecialties = appProperties.getSpecialties().stream()
                .map(AppProperties.Specialty::getCode)
                .collect(Collectors.toList());

        for (String specialty : specialties) {
            if (!validSpecialties.contains(specialty)) {
                throw new BusinessException("无效的工种代码: " + specialty);
            }
        }
    }

    /**
     * 将工种代码转换为枚举列表
     */
    private List<Technician.Specialty> convertSpecialtiesToEnum(List<String> specialties) {
        return specialties.stream()
                .map(code -> Technician.Specialty.valueOf(code.toUpperCase()))
                .collect(Collectors.toList());
    }

    /**
     * 将FaultType实体转换为FaultTypeDTO
     */
    private FaultTypeDTO convertToDTO(FaultType faultType) {
        List<String> specialtyCodes = faultType.getRequiredSpecialties().stream()
                .map(s -> s.name().toLowerCase())
                .collect(Collectors.toList());

        // 设置主要工种（取第一个工种，为了兼容前端）
        String primarySpecialty = specialtyCodes.isEmpty() ? null : specialtyCodes.get(0);

        return new FaultTypeDTO(
                faultType.getFaultTypeId(),
                faultType.getTypeName(),
                faultType.getDescription(),
                primarySpecialty,
                specialtyCodes,
                faultType.getRequiredTechCount(),
                faultType.getEstimatedHours(),
                faultType.getStatus(),
                faultType.getCreateTime(),
                faultType.getUpdateTime()
        );
    }
}
