package com.example.dto.request;

import jakarta.validation.constraints.*;
import java.time.LocalDateTime;

/**
 * 维修工单请求DTO
 */
public class OrderRequest {

    @NotNull(message = "车辆ID不能为空")
    private Long vehicleId;

    @NotNull(message = "故障类型ID不能为空")
    private Long faultTypeId;

    @NotBlank(message = "故障描述不能为空")
    @Size(max = 1000, message = "故障描述长度不能超过1000个字符")
    private String description;

    @NotBlank(message = "紧急程度不能为空")
    @Pattern(regexp = "^(low|normal|high|urgent)$", message = "紧急程度必须是low、normal、high或urgent")
    private String urgencyLevel;

    private LocalDateTime preferredTime;

    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "联系电话格式不正确")
    private String contactPhone;

    public OrderRequest() {}

    public OrderRequest(Long vehicleId, Long faultTypeId, String description, 
                       String urgencyLevel, LocalDateTime preferredTime, String contactPhone) {
        this.vehicleId = vehicleId;
        this.faultTypeId = faultTypeId;
        this.description = description;
        this.urgencyLevel = urgencyLevel;
        this.preferredTime = preferredTime;
        this.contactPhone = contactPhone;
    }

    public Long getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(Long vehicleId) {
        this.vehicleId = vehicleId;
    }

    public Long getFaultTypeId() {
        return faultTypeId;
    }

    public void setFaultTypeId(Long faultTypeId) {
        this.faultTypeId = faultTypeId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getUrgencyLevel() {
        return urgencyLevel;
    }

    public void setUrgencyLevel(String urgencyLevel) {
        this.urgencyLevel = urgencyLevel;
    }

    public LocalDateTime getPreferredTime() {
        return preferredTime;
    }

    public void setPreferredTime(LocalDateTime preferredTime) {
        this.preferredTime = preferredTime;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }
}
