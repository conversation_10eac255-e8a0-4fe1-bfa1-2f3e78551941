import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authAPI } from '@/api/auth'
import router from '@/router'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref(localStorage.getItem('token') || '')
  const user = ref(JSON.parse(localStorage.getItem('user') || 'null'))
  const userType = ref(localStorage.getItem('userType') || '')

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const isUser = computed(() => userType.value === 'user')
  const isTechnician = computed(() => userType.value === 'technician')
  const isAdmin = computed(() => userType.value === 'admin')

  // 登录
  const login = async (loginData) => {
    try {
      const response = await authAPI.login(loginData)
      // 后端返回格式: { success: true, data: { token, user, expiresIn } }
      const { token: newToken, user: userData } = response.data
      const type = userData.userType

      // 保存到状态
      token.value = newToken
      user.value = userData
      userType.value = type

      // 保存到本地存储
      localStorage.setItem('token', newToken)
      localStorage.setItem('user', JSON.stringify(userData))
      localStorage.setItem('userType', type)

      return response
    } catch (error) {
      throw error
    }
  }

  // 登出
  const logout = async () => {
    try {
      if (token.value) {
        await authAPI.logout()
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // 清除状态
      token.value = ''
      user.value = null
      userType.value = ''

      // 清除本地存储
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      localStorage.removeItem('userType')

      // 跳转到登录页
      router.push('/login')
    }
  }

  // 刷新Token
  const refreshToken = async () => {
    try {
      const response = await authAPI.refreshToken()
      // 后端返回格式: { success: true, data: { token, user, expiresIn } }
      const { token: newToken, user: userData } = response.data

      token.value = newToken
      localStorage.setItem('token', newToken)

      // 如果返回了用户信息，也更新用户信息
      if (userData) {
        user.value = userData
        userType.value = userData.userType
        localStorage.setItem('user', JSON.stringify(userData))
        localStorage.setItem('userType', userData.userType)
      }

      return response
    } catch (error) {
      // 刷新失败，执行登出
      await logout()
      throw error
    }
  }

  // 更新用户信息
  const updateUser = (userData) => {
    user.value = { ...user.value, ...userData }
    localStorage.setItem('user', JSON.stringify(user.value))
  }

  return {
    // 状态
    token,
    user,
    userType,

    // 计算属性
    isLoggedIn,
    isUser,
    isTechnician,
    isAdmin,

    // 方法
    login,
    logout,
    refreshToken,
    updateUser
  }
})
