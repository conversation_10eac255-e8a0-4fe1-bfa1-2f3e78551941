package com.example.dto.response;

import java.math.BigDecimal;

/**
 * 维修统计DTO
 */
public class RepairStatisticsDTO {

    private String brand;
    private String model;
    private String faultType;
    private Integer repairCount;
    private BigDecimal avgRepairCost;
    private BigDecimal totalCost;

    public RepairStatisticsDTO() {}

    public RepairStatisticsDTO(String brand, String model, Integer repairCount,
                              BigDecimal avgRepairCost, BigDecimal totalCost) {
        this.brand = brand;
        this.model = model;
        this.repairCount = repairCount;
        this.avgRepairCost = avgRepairCost;
        this.totalCost = totalCost;
    }

    public RepairStatisticsDTO(String brand, String model, String faultType, Integer repairCount,
                              BigDecimal avgRepairCost, BigDecimal totalCost) {
        this.brand = brand;
        this.model = model;
        this.faultType = faultType;
        this.repairCount = repairCount;
        this.avgRepairCost = avgRepairCost;
        this.totalCost = totalCost;
    }

    // Getters and Setters
    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getFaultType() {
        return faultType;
    }

    public void setFaultType(String faultType) {
        this.faultType = faultType;
    }

    public Integer getRepairCount() {
        return repairCount;
    }

    public void setRepairCount(Integer repairCount) {
        this.repairCount = repairCount;
    }

    public BigDecimal getAvgRepairCost() {
        return avgRepairCost;
    }

    public void setAvgRepairCost(BigDecimal avgRepairCost) {
        this.avgRepairCost = avgRepairCost;
    }

    public BigDecimal getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(BigDecimal totalCost) {
        this.totalCost = totalCost;
    }
}
