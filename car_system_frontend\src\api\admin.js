import api from './index'

export const adminAPI = {
  // 创建管理员账户
  createAdministrator(data) {
    return api.post('/admin/administrators', data)
  },

  // 获取所有用户
  getAllUsers(params = {}) {
    return api.get('/admin/users', { params })
  },

  // 获取所有技师
  getAllTechnicians(params = {}) {
    return api.get('/admin/technicians', { params })
  },

  // 获取所有车辆
  getAllVehicles(params = {}) {
    return api.get('/admin/vehicles', { params })
  },

  // 获取所有工单
  getAllOrders(params = {}) {
    return api.get('/admin/orders', { params })
  },

  // 禁用用户
  disableUser(id) {
    return api.put(`/admin/users/${id}/disable`)
  },

  // 删除用户
  deleteUser(id) {
    return api.delete(`/admin/users/${id}`)
  },

  // 启用用户
  enableUser(id) {
    return api.put(`/admin/users/${id}/enable`)
  },

  // 删除技师
  deleteTechnician(id) {
    return api.delete(`/admin/technicians/${id}`)
  },

  // 更新技师信息
  updateTechnician(id, data) {
    return api.put(`/admin/technicians/${id}`, data)
  },

  // 材料管理相关API
  // 获取所有材料
  getAllMaterials(params = {}) {
    return api.get('/materials', { params })
  },

  // 创建材料
  createMaterial(data) {
    return api.post('/materials', data)
  },

  // 更新材料
  updateMaterial(id, data) {
    return api.put(`/materials/${id}`, data)
  },

  // 删除材料
  deleteMaterial(id) {
    return api.delete(`/materials/${id}`)
  },

  // 获取材料详情
  getMaterial(id) {
    return api.get(`/materials/${id}`)
  },

  // 获取材料分类
  getMaterialCategories() {
    return api.get('/materials/categories')
  },

}
