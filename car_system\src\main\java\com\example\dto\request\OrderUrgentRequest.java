package com.example.dto.request;

import jakarta.validation.constraints.*;

/**
 * 工单催单请求DTO
 */
public class OrderUrgentRequest {

    @NotBlank(message = "催单原因不能为空")
    @Size(max = 500, message = "催单原因长度不能超过500个字符")
    private String reason;

    public OrderUrgentRequest() {}

    public OrderUrgentRequest(String reason) {
        this.reason = reason;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}
