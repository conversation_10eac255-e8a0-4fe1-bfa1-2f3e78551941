package com.example.dto.response;

/**
 * 登录响应DTO
 */
public class LoginResponse {
    
    private String token;
    private UserInfo user;
    private Long expiresIn;
    
    public LoginResponse() {}
    
    public LoginResponse(String token, UserInfo user, Long expiresIn) {
        this.token = token;
        this.user = user;
        this.expiresIn = expiresIn;
    }
    
    public String getToken() {
        return token;
    }
    
    public void setToken(String token) {
        this.token = token;
    }
    
    public UserInfo getUser() {
        return user;
    }
    
    public void setUser(UserInfo user) {
        this.user = user;
    }
    
    public Long getExpiresIn() {
        return expiresIn;
    }
    
    public void setExpiresIn(Long expiresIn) {
        this.expiresIn = expiresIn;
    }
    
    /**
     * 用户信息类
     */
    public static class UserInfo {
        private Long userId;
        private String username;
        private String realName;
        private String userType;
        
        public UserInfo() {}
        
        public UserInfo(Long userId, String username, String realName, String userType) {
            this.userId = userId;
            this.username = username;
            this.realName = realName;
            this.userType = userType;
        }
        
        public Long getUserId() {
            return userId;
        }
        
        public void setUserId(Long userId) {
            this.userId = userId;
        }
        
        public String getUsername() {
            return username;
        }
        
        public void setUsername(String username) {
            this.username = username;
        }
        
        public String getRealName() {
            return realName;
        }
        
        public void setRealName(String realName) {
            this.realName = realName;
        }
        
        public String getUserType() {
            return userType;
        }
        
        public void setUserType(String userType) {
            this.userType = userType;
        }
    }
}
