package com.example.controller;

import com.example.dto.request.VehicleRequest;
import com.example.dto.response.ApiResponse;
import com.example.dto.response.VehicleDTO;
import com.example.security.JwtAuthenticationFilter;
import com.example.service.VehicleService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

/**
 * 车辆控制器
 */
@RestController
@RequestMapping("/vehicles")
public class VehicleController {

    @Autowired
    private VehicleService vehicleService;

    /**
     * 添加车辆
     */
    @PostMapping
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<VehicleDTO> addVehicle(@Valid @RequestBody VehicleRequest request,
                                             HttpServletRequest httpRequest) {
        Long userId = getCurrentUserId(httpRequest);
        VehicleDTO vehicleDTO = vehicleService.addVehicle(userId, request);
        return ApiResponse.success("添加车辆成功", vehicleDTO);
    }

    /**
     * 获取车辆详情
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<VehicleDTO> getVehicle(@PathVariable Long id,
                                             HttpServletRequest request) {
        Long userId = getCurrentUserId(request);
        VehicleDTO vehicleDTO = vehicleService.getVehicle(id, userId);
        return ApiResponse.success("获取车辆信息成功", vehicleDTO);
    }

    /**
     * 更新车辆信息
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<VehicleDTO> updateVehicle(@PathVariable Long id,
                                                @Valid @RequestBody VehicleRequest request,
                                                HttpServletRequest httpRequest) {
        Long userId = getCurrentUserId(httpRequest);
        VehicleDTO vehicleDTO = vehicleService.updateVehicle(id, userId, request);
        return ApiResponse.success("更新车辆信息成功", vehicleDTO);
    }

    /**
     * 删除车辆
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Void> deleteVehicle(@PathVariable Long id,
                                          HttpServletRequest request) {
        Long userId = getCurrentUserId(request);
        vehicleService.deleteVehicle(id, userId);
        return ApiResponse.success("删除车辆成功", null);
    }

    /**
     * 从SecurityContext中获取当前用户ID
     */
    private Long getCurrentUserId(HttpServletRequest request) {
        JwtAuthenticationFilter.UserPrincipal principal =
            (JwtAuthenticationFilter.UserPrincipal) SecurityContextHolder.getContext()
                .getAuthentication().getPrincipal();
        return principal.getUserId();
    }
}
