package com.example.dto.request;

import jakarta.validation.constraints.*;
import java.time.LocalDate;

/**
 * 车辆请求DTO（用于添加和更新车辆）
 */
public class VehicleRequest {

    @NotBlank(message = "车牌号不能为空")
    @Pattern(regexp = "^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4}[A-Z0-9挂学警港澳]$", 
             message = "车牌号格式不正确")
    private String licensePlate;

    @NotBlank(message = "品牌不能为空")
    @Size(max = 50, message = "品牌长度不能超过50个字符")
    private String brand;

    @NotBlank(message = "型号不能为空")
    @Size(max = 50, message = "型号长度不能超过50个字符")
    private String model;

    @NotNull(message = "年份不能为空")
    @Min(value = 1900, message = "年份不能早于1900年")
    @Max(value = 2030, message = "年份不能晚于2030年")
    private Integer year;

    @NotBlank(message = "VIN码不能为空")
    @Pattern(regexp = "^[A-HJ-NPR-Z0-9]{17}$", message = "VIN码格式不正确，必须为17位字符")
    private String vin;

    @Size(max = 30, message = "颜色长度不能超过30个字符")
    private String color;

    @Size(max = 50, message = "发动机号长度不能超过50个字符")
    private String engineNumber;

    private LocalDate registerDate;

    public VehicleRequest() {}

    public VehicleRequest(String licensePlate, String brand, String model, Integer year, 
                         String vin, String color, String engineNumber, LocalDate registerDate) {
        this.licensePlate = licensePlate;
        this.brand = brand;
        this.model = model;
        this.year = year;
        this.vin = vin;
        this.color = color;
        this.engineNumber = engineNumber;
        this.registerDate = registerDate;
    }

    public String getLicensePlate() {
        return licensePlate;
    }

    public void setLicensePlate(String licensePlate) {
        this.licensePlate = licensePlate;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getEngineNumber() {
        return engineNumber;
    }

    public void setEngineNumber(String engineNumber) {
        this.engineNumber = engineNumber;
    }

    public LocalDate getRegisterDate() {
        return registerDate;
    }

    public void setRegisterDate(LocalDate registerDate) {
        this.registerDate = registerDate;
    }
}
