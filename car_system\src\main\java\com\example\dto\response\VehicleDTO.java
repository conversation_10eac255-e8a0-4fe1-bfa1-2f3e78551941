package com.example.dto.response;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 车辆信息DTO
 */
public class VehicleDTO {

    private Long vehicleId;
    private Long userId;
    private String licensePlate;
    private String brand;
    private String model;
    private Integer year;
    private String vin;
    private String color;
    private String engineNumber;
    private LocalDate registerDate;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private UserInfo user;

    public VehicleDTO() {}

    public VehicleDTO(Long vehicleId, Long userId, String licensePlate, String brand,
                     String model, Integer year, String vin, String color,
                     String engineNumber, LocalDate registerDate,
                     LocalDateTime createTime, LocalDateTime updateTime) {
        this.vehicleId = vehicleId;
        this.userId = userId;
        this.licensePlate = licensePlate;
        this.brand = brand;
        this.model = model;
        this.year = year;
        this.vin = vin;
        this.color = color;
        this.engineNumber = engineNumber;
        this.registerDate = registerDate;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }

    public Long getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(Long vehicleId) {
        this.vehicleId = vehicleId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getLicensePlate() {
        return licensePlate;
    }

    public void setLicensePlate(String licensePlate) {
        this.licensePlate = licensePlate;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getEngineNumber() {
        return engineNumber;
    }

    public void setEngineNumber(String engineNumber) {
        this.engineNumber = engineNumber;
    }

    public LocalDate getRegisterDate() {
        return registerDate;
    }

    public void setRegisterDate(LocalDate registerDate) {
        this.registerDate = registerDate;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public UserInfo getUser() {
        return user;
    }

    public void setUser(UserInfo user) {
        this.user = user;
    }

    // 内部类：用户信息
    public static class UserInfo {
        private Long userId;
        private String realName;
        private String phone;
        private String email;

        public UserInfo() {}

        public UserInfo(Long userId, String realName, String phone, String email) {
            this.userId = userId;
            this.realName = realName;
            this.phone = phone;
            this.email = email;
        }

        public Long getUserId() {
            return userId;
        }

        public void setUserId(Long userId) {
            this.userId = userId;
        }

        public String getRealName() {
            return realName;
        }

        public void setRealName(String realName) {
            this.realName = realName;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }
    }
}
