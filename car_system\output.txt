[INFO] Scanning for projects...
[INFO] 
[INFO] -----------------------< com.example:car_system >-----------------------
[INFO] Building Vehicle Repair Management System 1.0-SNAPSHOT
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] >>> spring-boot:3.2.12:run (default-cli) > test-compile @ car_system >>>
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ car_system ---
[INFO] Copying 1 resource from src\main\resources to target\classes
[INFO] Copying 1 resource from src\main\resources to target\classes
[INFO] 
[INFO] --- compiler:3.11.0:compile (default-compile) @ car_system ---
[INFO] Nothing to compile - all classes are up to date
[INFO] 
[INFO] --- resources:3.3.1:testResources (default-testResources) @ car_system ---
[INFO] Copying 1 resource from src\test\resources to target\test-classes
[INFO] 
[INFO] --- compiler:3.11.0:testCompile (default-testCompile) @ car_system ---
[INFO] Nothing to compile - all classes are up to date
[INFO] 
[INFO] <<< spring-boot:3.2.12:run (default-cli) < test-compile @ car_system <<<
[INFO] 
[INFO] 
[INFO] --- spring-boot:3.2.12:run (default-cli) @ car_system ---
[INFO] Attaching agents: []

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::               (v3.2.12)

2025-06-05 19:47:52 - Starting VehicleRepairManagementApplication using Java 21.0.2 with PID 21312 (C:\Users\<USER>\Desktop\db_pj\car_system\target\classes started by eweawa in C:\Users\<USER>\Desktop\db_pj\car_system)
2025-06-05 19:47:52 - Running with Spring Boot v3.2.12, Spring v6.1.15
2025-06-05 19:47:52 - No active profile set, falling back to 1 default profile: "default"
2025-06-05 19:47:52 - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-05 19:47:52 - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-05 19:47:53 - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-05 19:47:53 - Finished Spring Data repository scanning in 44 ms. Found 11 JPA repository interfaces.
2025-06-05 19:47:53 - Tomcat initialized with port 8080 (http)
2025-06-05 19:47:53 - Starting service [Tomcat]
2025-06-05 19:47:53 - Starting Servlet engine: [Apache Tomcat/10.1.33]
2025-06-05 19:47:54 - Initializing Spring embedded WebApplicationContext
2025-06-05 19:47:54 - Root WebApplicationContext: initialization completed in 1133 ms
2025-06-05 19:47:54 - HikariPool-1 - Starting...
2025-06-05 19:47:54 - HikariPool-1 - Added connection conn0: url=jdbc:h2:mem:testdb user=SA
2025-06-05 19:47:54 - HikariPool-1 - Start completed.
2025-06-05 19:47:54 - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
2025-06-05 19:47:54 - Filter 'jwtAuthenticationFilter' configured for use
2025-06-05 19:47:54 - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-05 19:47:54 - HHH000412: Hibernate ORM core version 6.4.10.Final
2025-06-05 19:47:54 - HHH000026: Second-level cache disabled
2025-06-05 19:47:54 - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-05 19:47:54 - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-05 19:47:55 - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-05 19:47:55 - 
    drop table if exists fault_type_specialties cascade 
Hibernate: 
    drop table if exists fault_type_specialties cascade 
2025-06-05 19:47:55 - 
    drop table if exists fault_types cascade 
Hibernate: 
    drop table if exists fault_types cascade 
2025-06-05 19:47:55 - 
    drop table if exists labor_payments cascade 
Hibernate: 
    drop table if exists labor_payments cascade 
2025-06-05 19:47:55 - 
    drop table if exists materials cascade 
Hibernate: 
    drop table if exists materials cascade 
2025-06-05 19:47:55 - 
    drop table if exists order_feedback cascade 
Hibernate: 
    drop table if exists order_feedback cascade 
2025-06-05 19:47:55 - 
    drop table if exists order_material_usage cascade 
Hibernate: 
    drop table if exists order_material_usage cascade 
2025-06-05 19:47:55 - 
    drop table if exists order_status_history cascade 
Hibernate: 
    drop table if exists order_status_history cascade 
2025-06-05 19:47:55 - 
    drop table if exists order_technician_assignments cascade 
Hibernate: 
    drop table if exists order_technician_assignments cascade 
2025-06-05 19:47:55 - 
    drop table if exists repair_orders cascade 
Hibernate: 
    drop table if exists repair_orders cascade 
2025-06-05 19:47:55 - 
    drop table if exists technicians cascade 
Hibernate: 
    drop table if exists technicians cascade 
2025-06-05 19:47:55 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
2025-06-05 19:47:55 - 
    drop table if exists vehicles cascade 
Hibernate: 
    drop table if exists vehicles cascade 
2025-06-05 19:47:55 - 
    create table fault_type_specialties (
        fault_type_id bigint not null,
        specialty varchar(255) check (specialty in ('ENGINE','TRANSMISSION','BRAKE','ELECTRICAL','HVAC','CHASSIS','BODY','TIRE'))
    )
Hibernate: 
    create table fault_type_specialties (
        fault_type_id bigint not null,
        specialty varchar(255) check (specialty in ('ENGINE','TRANSMISSION','BRAKE','ELECTRICAL','HVAC','CHASSIS','BODY','TIRE'))
    )
2025-06-05 19:47:55 - 
    create table fault_types (
        estimated_hours integer not null check (estimated_hours>=1),
        required_tech_count integer not null check (required_tech_count>=1),
        status integer not null,
        create_time timestamp(6) not null,
        fault_type_id bigint generated by default as identity,
        update_time timestamp(6) not null,
        type_name varchar(100) not null,
        description varchar(500),
        primary key (fault_type_id)
    )
Hibernate: 
    create table fault_types (
        estimated_hours integer not null check (estimated_hours>=1),
        required_tech_count integer not null check (required_tech_count>=1),
        status integer not null,
        create_time timestamp(6) not null,
        fault_type_id bigint generated by default as identity,
        update_time timestamp(6) not null,
        type_name varchar(100) not null,
        description varchar(500),
        primary key (fault_type_id)
    )
2025-06-05 19:47:55 - 
    create table labor_payments (
        hourly_rate numeric(10,2) not null,
        payment_month integer not null check ((payment_month<=12) and (payment_month>=1)),
        payment_year integer not null check (payment_year>=2020),
        total_amount numeric(10,2) not null,
        total_hours numeric(8,2) not null,
        create_time timestamp(6) not null,
        payment_id bigint generated by default as identity,
        payment_time timestamp(6),
        technician_id bigint not null,
        update_time timestamp(6) not null,
        payment_status varchar(20) not null check (payment_status in ('PENDING','PAID','CANCELLED')),
        primary key (payment_id)
    )
Hibernate: 
    create table labor_payments (
        hourly_rate numeric(10,2) not null,
        payment_month integer not null check ((payment_month<=12) and (payment_month>=1)),
        payment_year integer not null check (payment_year>=2020),
        total_amount numeric(10,2) not null,
        total_hours numeric(8,2) not null,
        create_time timestamp(6) not null,
        payment_id bigint generated by default as identity,
        payment_time timestamp(6),
        technician_id bigint not null,
        update_time timestamp(6) not null,
        payment_status varchar(20) not null check (payment_status in ('PENDING','PAID','CANCELLED')),
        primary key (payment_id)
    )
2025-06-05 19:47:55 - 
    create table materials (
        inventory integer not null check (inventory>=0),
        status integer not null,
        unit_price numeric(10,2) not null,
        create_time timestamp(6) not null,
        material_id bigint generated by default as identity,
        update_time timestamp(6) not null,
        unit varchar(20) not null,
        category varchar(50),
        material_name varchar(100) not null,
        specification varchar(100),
        primary key (material_id)
    )
Hibernate: 
    create table materials (
        inventory integer not null check (inventory>=0),
        status integer not null,
        unit_price numeric(10,2) not null,
        create_time timestamp(6) not null,
        material_id bigint generated by default as identity,
        update_time timestamp(6) not null,
        unit varchar(20) not null,
        category varchar(50),
        material_name varchar(100) not null,
        specification varchar(100),
        primary key (material_id)
    )
2025-06-05 19:47:55 - 
    create table order_feedback (
        rating integer not null check ((rating>=1) and (rating<=5)),
        create_time timestamp(6) not null,
        feedback_id bigint generated by default as identity,
        feedback_time timestamp(6) not null,
        order_id bigint not null unique,
        update_time timestamp(6) not null,
        comment varchar(1000),
        primary key (feedback_id)
    )
Hibernate: 
    create table order_feedback (
        rating integer not null check ((rating>=1) and (rating<=5)),
        create_time timestamp(6) not null,
        feedback_id bigint generated by default as identity,
        feedback_time timestamp(6) not null,
        order_id bigint not null unique,
        update_time timestamp(6) not null,
        comment varchar(1000),
        primary key (feedback_id)
    )
2025-06-05 19:47:55 - 
    create table order_material_usage (
        quantity numeric(10,2) not null,
        total_price numeric(10,2) not null,
        create_time timestamp(6) not null,
        material_id bigint not null,
        order_id bigint not null,
        update_time timestamp(6) not null,
        usage_id bigint generated by default as identity,
        use_time timestamp(6),
        primary key (usage_id)
    )
Hibernate: 
    create table order_material_usage (
        quantity numeric(10,2) not null,
        total_price numeric(10,2) not null,
        create_time timestamp(6) not null,
        material_id bigint not null,
        order_id bigint not null,
        update_time timestamp(6) not null,
        usage_id bigint generated by default as identity,
        use_time timestamp(6),
        primary key (usage_id)
    )
2025-06-05 19:47:55 - 
    create table order_status_history (
        change_time timestamp(6) not null,
        create_time timestamp(6) not null,
        history_id bigint generated by default as identity,
        order_id bigint not null,
        update_time timestamp(6) not null,
        from_status varchar(20) check (from_status in ('PENDING','ASSIGNED','ACCEPTED','IN_PROGRESS','COMPLETED','CANCELLED')),
        to_status varchar(20) not null check (to_status in ('PENDING','ASSIGNED','ACCEPTED','IN_PROGRESS','COMPLETED','CANCELLED')),
        operator varchar(100),
        remark varchar(500),
        primary key (history_id)
    )
Hibernate: 
    create table order_status_history (
        change_time timestamp(6) not null,
        create_time timestamp(6) not null,
        history_id bigint generated by default as identity,
        order_id bigint not null,
        update_time timestamp(6) not null,
        from_status varchar(20) check (from_status in ('PENDING','ASSIGNED','ACCEPTED','IN_PROGRESS','COMPLETED','CANCELLED')),
        to_status varchar(20) not null check (to_status in ('PENDING','ASSIGNED','ACCEPTED','IN_PROGRESS','COMPLETED','CANCELLED')),
        operator varchar(100),
        remark varchar(500),
        primary key (history_id)
    )
2025-06-05 19:47:55 - 
    create table order_technician_assignments (
        assigned_time timestamp(6) not null,
        assignment_id bigint generated by default as identity,
        create_time timestamp(6) not null,
        order_id bigint not null,
        response_time timestamp(6),
        technician_id bigint not null,
        update_time timestamp(6) not null,
        agreement_status varchar(20) not null check (agreement_status in ('PENDING','ACCEPTED','REJECTED')),
        reject_reason varchar(500),
        primary key (assignment_id)
    )
Hibernate: 
    create table order_technician_assignments (
        assigned_time timestamp(6) not null,
        assignment_id bigint generated by default as identity,
        create_time timestamp(6) not null,
        order_id bigint not null,
        response_time timestamp(6),
        technician_id bigint not null,
        update_time timestamp(6) not null,
        agreement_status varchar(20) not null check (agreement_status in ('PENDING','ACCEPTED','REJECTED')),
        reject_reason varchar(500),
        primary key (assignment_id)
    )
2025-06-05 19:47:55 - 
    create table repair_orders (
        total_cost numeric(10,2),
        total_labor_cost numeric(10,2),
        total_material_cost numeric(10,2),
        working_hours numeric(5,2),
        actual_completion_time timestamp(6),
        create_time timestamp(6) not null,
        estimated_completion_time timestamp(6),
        fault_type_id bigint not null,
        order_id bigint generated by default as identity,
        preferred_time timestamp(6),
        submit_time timestamp(6) not null,
        update_time timestamp(6) not null,
        user_id bigint not null,
        vehicle_id bigint not null,
        contact_phone varchar(11),
        payment_status varchar(20) not null check (payment_status in ('UNPAID','PAID','REFUNDED')),
        status varchar(20) not null check (status in ('PENDING','ASSIGNED','ACCEPTED','IN_PROGRESS','COMPLETED','CANCELLED')),
        urgency_level varchar(20) not null check (urgency_level in ('LOW','NORMAL','HIGH','URGENT')),
        description varchar(1000) not null,
        work_result varchar(1000),
        primary key (order_id)
    )
Hibernate: 
    create table repair_orders (
        total_cost numeric(10,2),
        total_labor_cost numeric(10,2),
        total_material_cost numeric(10,2),
        working_hours numeric(5,2),
        actual_completion_time timestamp(6),
        create_time timestamp(6) not null,
        estimated_completion_time timestamp(6),
        fault_type_id bigint not null,
        order_id bigint generated by default as identity,
        preferred_time timestamp(6),
        submit_time timestamp(6) not null,
        update_time timestamp(6) not null,
        user_id bigint not null,
        vehicle_id bigint not null,
        contact_phone varchar(11),
        payment_status varchar(20) not null check (payment_status in ('UNPAID','PAID','REFUNDED')),
        status varchar(20) not null check (status in ('PENDING','ASSIGNED','ACCEPTED','IN_PROGRESS','COMPLETED','CANCELLED')),
        urgency_level varchar(20) not null check (urgency_level in ('LOW','NORMAL','HIGH','URGENT')),
        description varchar(1000) not null,
        work_result varchar(1000),
        primary key (order_id)
    )
2025-06-05 19:47:55 - 
    create table technicians (
        hire_date date not null,
        hourly_rate numeric(10,2) not null,
        rating numeric(3,2),
        status integer not null,
        workload integer not null,
        create_time timestamp(6) not null,
        technician_id bigint generated by default as identity,
        update_time timestamp(6) not null,
        phone varchar(11) not null,
        specialty varchar(20) not null check (specialty in ('ENGINE','TRANSMISSION','BRAKE','ELECTRICAL','HVAC','CHASSIS','BODY','TIRE')),
        username varchar(50) not null unique,
        email varchar(100),
        real_name varchar(100) not null,
        password varchar(255) not null,
        primary key (technician_id)
    )
Hibernate: 
    create table technicians (
        hire_date date not null,
        hourly_rate numeric(10,2) not null,
        rating numeric(3,2),
        status integer not null,
        workload integer not null,
        create_time timestamp(6) not null,
        technician_id bigint generated by default as identity,
        update_time timestamp(6) not null,
        phone varchar(11) not null,
        specialty varchar(20) not null check (specialty in ('ENGINE','TRANSMISSION','BRAKE','ELECTRICAL','HVAC','CHASSIS','BODY','TIRE')),
        username varchar(50) not null unique,
        email varchar(100),
        real_name varchar(100) not null,
        password varchar(255) not null,
        primary key (technician_id)
    )
2025-06-05 19:47:55 - 
    create table users (
        status integer not null,
        create_time timestamp(6) not null,
        update_time timestamp(6) not null,
        user_id bigint generated by default as identity,
        phone varchar(11) not null,
        user_type varchar(20) not null check (user_type in ('USER','TECHNICIAN','ADMIN')),
        username varchar(50) not null unique,
        email varchar(100),
        real_name varchar(100) not null,
        address varchar(200),
        password varchar(255) not null,
        primary key (user_id)
    )
Hibernate: 
    create table users (
        status integer not null,
        create_time timestamp(6) not null,
        update_time timestamp(6) not null,
        user_id bigint generated by default as identity,
        phone varchar(11) not null,
        user_type varchar(20) not null check (user_type in ('USER','TECHNICIAN','ADMIN')),
        username varchar(50) not null unique,
        email varchar(100),
        real_name varchar(100) not null,
        address varchar(200),
        password varchar(255) not null,
        primary key (user_id)
    )
2025-06-05 19:47:55 - 
    create table vehicles (
        register_date date,
        vehicle_year integer not null check ((vehicle_year>=1900) and (vehicle_year<=2030)),
        create_time timestamp(6) not null,
        update_time timestamp(6) not null,
        user_id bigint not null,
        vehicle_id bigint generated by default as identity,
        license_plate varchar(10) not null unique,
        vin varchar(17) not null unique,
        color varchar(20),
        brand varchar(50) not null,
        engine_number varchar(50),
        model varchar(50) not null,
        primary key (vehicle_id)
    )
Hibernate: 
    create table vehicles (
        register_date date,
        vehicle_year integer not null check ((vehicle_year>=1900) and (vehicle_year<=2030)),
        create_time timestamp(6) not null,
        update_time timestamp(6) not null,
        user_id bigint not null,
        vehicle_id bigint generated by default as identity,
        license_plate varchar(10) not null unique,
        vin varchar(17) not null unique,
        color varchar(20),
        brand varchar(50) not null,
        engine_number varchar(50),
        model varchar(50) not null,
        primary key (vehicle_id)
    )
2025-06-05 19:47:55 - 
    alter table if exists fault_type_specialties 
       add constraint FKfnv5vfmm6eun1elit0136iq31 
       foreign key (fault_type_id) 
       references fault_types
Hibernate: 
    alter table if exists fault_type_specialties 
       add constraint FKfnv5vfmm6eun1elit0136iq31 
       foreign key (fault_type_id) 
       references fault_types
2025-06-05 19:47:55 - 
    alter table if exists labor_payments 
       add constraint FKtcvxdcuyb8uetmh26gkeangr1 
       foreign key (technician_id) 
       references technicians
Hibernate: 
    alter table if exists labor_payments 
       add constraint FKtcvxdcuyb8uetmh26gkeangr1 
       foreign key (technician_id) 
       references technicians
2025-06-05 19:47:55 - 
    alter table if exists order_feedback 
       add constraint FKjna64ssnxs3lm4qq7fq2tg5uu 
       foreign key (order_id) 
       references repair_orders
Hibernate: 
    alter table if exists order_feedback 
       add constraint FKjna64ssnxs3lm4qq7fq2tg5uu 
       foreign key (order_id) 
       references repair_orders
2025-06-05 19:47:55 - 
    alter table if exists order_material_usage 
       add constraint FKnkoe725r3hcs1cdwrg5mxkqtw 
       foreign key (material_id) 
       references materials
Hibernate: 
    alter table if exists order_material_usage 
       add constraint FKnkoe725r3hcs1cdwrg5mxkqtw 
       foreign key (material_id) 
       references materials
2025-06-05 19:47:55 - 
    alter table if exists order_material_usage 
       add constraint FKeu56w6vwiiwhat4tbyaox7a7o 
       foreign key (order_id) 
       references repair_orders
Hibernate: 
    alter table if exists order_material_usage 
       add constraint FKeu56w6vwiiwhat4tbyaox7a7o 
       foreign key (order_id) 
       references repair_orders
2025-06-05 19:47:55 - 
    alter table if exists order_status_history 
       add constraint FKtdasxpwxfjl6dubidhbdw0lor 
       foreign key (order_id) 
       references repair_orders
Hibernate: 
    alter table if exists order_status_history 
       add constraint FKtdasxpwxfjl6dubidhbdw0lor 
       foreign key (order_id) 
       references repair_orders
2025-06-05 19:47:55 - 
    alter table if exists order_technician_assignments 
       add constraint FKpnvr7thhfg551wob59qlew6d5 
       foreign key (order_id) 
       references repair_orders
Hibernate: 
    alter table if exists order_technician_assignments 
       add constraint FKpnvr7thhfg551wob59qlew6d5 
       foreign key (order_id) 
       references repair_orders
2025-06-05 19:47:55 - 
    alter table if exists order_technician_assignments 
       add constraint FKotdiiemu01skg11crntwc194 
       foreign key (technician_id) 
       references technicians
Hibernate: 
    alter table if exists order_technician_assignments 
       add constraint FKotdiiemu01skg11crntwc194 
       foreign key (technician_id) 
       references technicians
2025-06-05 19:47:55 - 
    alter table if exists repair_orders 
       add constraint FKeoixks5qfbbiqj4xhh5oc0boq 
       foreign key (fault_type_id) 
       references fault_types
Hibernate: 
    alter table if exists repair_orders 
       add constraint FKeoixks5qfbbiqj4xhh5oc0boq 
       foreign key (fault_type_id) 
       references fault_types
2025-06-05 19:47:55 - 
    alter table if exists repair_orders 
       add constraint FK1yqdonr1y62vxslgdkm00xfei 
       foreign key (user_id) 
       references users
Hibernate: 
    alter table if exists repair_orders 
       add constraint FK1yqdonr1y62vxslgdkm00xfei 
       foreign key (user_id) 
       references users
2025-06-05 19:47:55 - 
    alter table if exists repair_orders 
       add constraint FK9m04dl6jgcfxmj8jt9bw5a3s9 
       foreign key (vehicle_id) 
       references vehicles
Hibernate: 
    alter table if exists repair_orders 
       add constraint FK9m04dl6jgcfxmj8jt9bw5a3s9 
       foreign key (vehicle_id) 
       references vehicles
2025-06-05 19:47:55 - 
    alter table if exists vehicles 
       add constraint FKo4u5y92lt2sx8y2dc1bb9sewc 
       foreign key (user_id) 
       references users
Hibernate: 
    alter table if exists vehicles 
       add constraint FKo4u5y92lt2sx8y2dc1bb9sewc 
       foreign key (user_id) 
       references users
2025-06-05 19:47:55 - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 19:47:55 - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-05 19:47:56 - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-05 19:47:56 - 

Using generated security password: 7b3645ba-e8b8-4687-9e5c-3b986dc9151e

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 19:47:56 - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-06-05 19:47:56 - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6071c7bf, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@397fd3ac, org.springframework.security.web.context.SecurityContextHolderFilter@458231e4, org.springframework.security.web.header.HeaderWriterFilter@3bd77f2b, org.springframework.web.filter.CorsFilter@31e506fe, org.springframework.security.web.authentication.logout.LogoutFilter@2dea2fee, com.example.security.JwtAuthenticationFilter@1737922b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@630d253d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@54883bfb, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@19a1f5ce, org.springframework.security.web.session.SessionManagementFilter@5ff978be, org.springframework.security.web.access.ExceptionTranslationFilter@56915b8a, org.springframework.security.web.access.intercept.AuthorizationFilter@634543bd]
2025-06-05 19:47:56 - LiveReload server is running on port 35729
2025-06-05 19:47:56 - Tomcat started on port 8080 (http) with context path '/api'
2025-06-05 19:47:56 - Started VehicleRepairManagementApplication in 4.37 seconds (process running for 4.596)
2025-06-05 19:47:57 - Starting data initialization...
2025-06-05 19:47:57 - 
    select
        u1_0.user_id 
    from
        users u1_0 
    where
        u1_0.username=? 
    fetch
        first ? rows only
Hibernate: 
    select
        u1_0.user_id 
    from
        users u1_0 
    where
        u1_0.username=? 
    fetch
        first ? rows only
2025-06-05 19:47:57 - 
    insert 
    into
        users
        (address, create_time, email, password, phone, real_name, status, update_time, user_type, username, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address, create_time, email, password, phone, real_name, status, update_time, user_type, username, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - Default admin user created: admin
2025-06-05 19:47:57 - 
    select
        count(*) 
    from
        fault_types ft1_0
Hibernate: 
    select
        count(*) 
    from
        fault_types ft1_0
2025-06-05 19:47:57 - 
    insert 
    into
        fault_types
        (create_time, description, estimated_hours, required_tech_count, status, type_name, update_time, fault_type_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        fault_types
        (create_time, description, estimated_hours, required_tech_count, status, type_name, update_time, fault_type_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        fault_type_specialties
        (fault_type_id, specialty) 
    values
        (?, ?)
Hibernate: 
    insert 
    into
        fault_type_specialties
        (fault_type_id, specialty) 
    values
        (?, ?)
2025-06-05 19:47:57 - 
    insert 
    into
        fault_types
        (create_time, description, estimated_hours, required_tech_count, status, type_name, update_time, fault_type_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        fault_types
        (create_time, description, estimated_hours, required_tech_count, status, type_name, update_time, fault_type_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        fault_type_specialties
        (fault_type_id, specialty) 
    values
        (?, ?)
Hibernate: 
    insert 
    into
        fault_type_specialties
        (fault_type_id, specialty) 
    values
        (?, ?)
2025-06-05 19:47:57 - 
    insert 
    into
        fault_types
        (create_time, description, estimated_hours, required_tech_count, status, type_name, update_time, fault_type_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        fault_types
        (create_time, description, estimated_hours, required_tech_count, status, type_name, update_time, fault_type_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        fault_type_specialties
        (fault_type_id, specialty) 
    values
        (?, ?)
Hibernate: 
    insert 
    into
        fault_type_specialties
        (fault_type_id, specialty) 
    values
        (?, ?)
2025-06-05 19:47:57 - 
    insert 
    into
        fault_types
        (create_time, description, estimated_hours, required_tech_count, status, type_name, update_time, fault_type_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        fault_types
        (create_time, description, estimated_hours, required_tech_count, status, type_name, update_time, fault_type_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        fault_type_specialties
        (fault_type_id, specialty) 
    values
        (?, ?)
Hibernate: 
    insert 
    into
        fault_type_specialties
        (fault_type_id, specialty) 
    values
        (?, ?)
2025-06-05 19:47:57 - 
    insert 
    into
        fault_types
        (create_time, description, estimated_hours, required_tech_count, status, type_name, update_time, fault_type_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        fault_types
        (create_time, description, estimated_hours, required_tech_count, status, type_name, update_time, fault_type_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        fault_type_specialties
        (fault_type_id, specialty) 
    values
        (?, ?)
Hibernate: 
    insert 
    into
        fault_type_specialties
        (fault_type_id, specialty) 
    values
        (?, ?)
2025-06-05 19:47:57 - 
    insert 
    into
        fault_types
        (create_time, description, estimated_hours, required_tech_count, status, type_name, update_time, fault_type_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        fault_types
        (create_time, description, estimated_hours, required_tech_count, status, type_name, update_time, fault_type_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        fault_type_specialties
        (fault_type_id, specialty) 
    values
        (?, ?)
Hibernate: 
    insert 
    into
        fault_type_specialties
        (fault_type_id, specialty) 
    values
        (?, ?)
2025-06-05 19:47:57 - 
    insert 
    into
        fault_types
        (create_time, description, estimated_hours, required_tech_count, status, type_name, update_time, fault_type_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        fault_types
        (create_time, description, estimated_hours, required_tech_count, status, type_name, update_time, fault_type_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        fault_type_specialties
        (fault_type_id, specialty) 
    values
        (?, ?)
Hibernate: 
    insert 
    into
        fault_type_specialties
        (fault_type_id, specialty) 
    values
        (?, ?)
2025-06-05 19:47:57 - 
    insert 
    into
        fault_types
        (create_time, description, estimated_hours, required_tech_count, status, type_name, update_time, fault_type_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        fault_types
        (create_time, description, estimated_hours, required_tech_count, status, type_name, update_time, fault_type_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        fault_type_specialties
        (fault_type_id, specialty) 
    values
        (?, ?)
Hibernate: 
    insert 
    into
        fault_type_specialties
        (fault_type_id, specialty) 
    values
        (?, ?)
2025-06-05 19:47:57 - 
    insert 
    into
        fault_types
        (create_time, description, estimated_hours, required_tech_count, status, type_name, update_time, fault_type_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        fault_types
        (create_time, description, estimated_hours, required_tech_count, status, type_name, update_time, fault_type_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        fault_type_specialties
        (fault_type_id, specialty) 
    values
        (?, ?)
Hibernate: 
    insert 
    into
        fault_type_specialties
        (fault_type_id, specialty) 
    values
        (?, ?)
2025-06-05 19:47:57 - 
    insert 
    into
        fault_type_specialties
        (fault_type_id, specialty) 
    values
        (?, ?)
Hibernate: 
    insert 
    into
        fault_type_specialties
        (fault_type_id, specialty) 
    values
        (?, ?)
2025-06-05 19:47:57 - 
    insert 
    into
        fault_types
        (create_time, description, estimated_hours, required_tech_count, status, type_name, update_time, fault_type_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        fault_types
        (create_time, description, estimated_hours, required_tech_count, status, type_name, update_time, fault_type_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        fault_type_specialties
        (fault_type_id, specialty) 
    values
        (?, ?)
Hibernate: 
    insert 
    into
        fault_type_specialties
        (fault_type_id, specialty) 
    values
        (?, ?)
2025-06-05 19:47:57 - 
    insert 
    into
        fault_type_specialties
        (fault_type_id, specialty) 
    values
        (?, ?)
Hibernate: 
    insert 
    into
        fault_type_specialties
        (fault_type_id, specialty) 
    values
        (?, ?)
2025-06-05 19:47:57 - Fault types initialized
2025-06-05 19:47:57 - 
    select
        count(*) 
    from
        materials m1_0
Hibernate: 
    select
        count(*) 
    from
        materials m1_0
2025-06-05 19:47:57 - 
    insert 
    into
        materials
        (category, create_time, inventory, material_name, specification, status, unit, unit_price, update_time, material_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        materials
        (category, create_time, inventory, material_name, specification, status, unit, unit_price, update_time, material_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        materials
        (category, create_time, inventory, material_name, specification, status, unit, unit_price, update_time, material_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        materials
        (category, create_time, inventory, material_name, specification, status, unit, unit_price, update_time, material_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        materials
        (category, create_time, inventory, material_name, specification, status, unit, unit_price, update_time, material_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        materials
        (category, create_time, inventory, material_name, specification, status, unit, unit_price, update_time, material_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        materials
        (category, create_time, inventory, material_name, specification, status, unit, unit_price, update_time, material_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        materials
        (category, create_time, inventory, material_name, specification, status, unit, unit_price, update_time, material_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        materials
        (category, create_time, inventory, material_name, specification, status, unit, unit_price, update_time, material_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        materials
        (category, create_time, inventory, material_name, specification, status, unit, unit_price, update_time, material_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        materials
        (category, create_time, inventory, material_name, specification, status, unit, unit_price, update_time, material_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        materials
        (category, create_time, inventory, material_name, specification, status, unit, unit_price, update_time, material_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        materials
        (category, create_time, inventory, material_name, specification, status, unit, unit_price, update_time, material_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        materials
        (category, create_time, inventory, material_name, specification, status, unit, unit_price, update_time, material_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        materials
        (category, create_time, inventory, material_name, specification, status, unit, unit_price, update_time, material_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        materials
        (category, create_time, inventory, material_name, specification, status, unit, unit_price, update_time, material_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        materials
        (category, create_time, inventory, material_name, specification, status, unit, unit_price, update_time, material_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        materials
        (category, create_time, inventory, material_name, specification, status, unit, unit_price, update_time, material_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        materials
        (category, create_time, inventory, material_name, specification, status, unit, unit_price, update_time, material_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        materials
        (category, create_time, inventory, material_name, specification, status, unit, unit_price, update_time, material_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        materials
        (category, create_time, inventory, material_name, specification, status, unit, unit_price, update_time, material_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        materials
        (category, create_time, inventory, material_name, specification, status, unit, unit_price, update_time, material_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        materials
        (category, create_time, inventory, material_name, specification, status, unit, unit_price, update_time, material_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        materials
        (category, create_time, inventory, material_name, specification, status, unit, unit_price, update_time, material_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        materials
        (category, create_time, inventory, material_name, specification, status, unit, unit_price, update_time, material_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        materials
        (category, create_time, inventory, material_name, specification, status, unit, unit_price, update_time, material_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - Materials initialized
2025-06-05 19:47:57 - Data initialization completed.
2025-06-05 19:47:57 - Application ready, starting test data initialization...
2025-06-05 19:47:57 - 
    select
        count(*) 
    from
        users u1_0
Hibernate: 
    select
        count(*) 
    from
        users u1_0
2025-06-05 19:47:57 - 
    insert 
    into
        users
        (address, create_time, email, password, phone, real_name, status, update_time, user_type, username, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address, create_time, email, password, phone, real_name, status, update_time, user_type, username, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        users
        (address, create_time, email, password, phone, real_name, status, update_time, user_type, username, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address, create_time, email, password, phone, real_name, status, update_time, user_type, username, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        users
        (address, create_time, email, password, phone, real_name, status, update_time, user_type, username, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address, create_time, email, password, phone, real_name, status, update_time, user_type, username, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        users
        (address, create_time, email, password, phone, real_name, status, update_time, user_type, username, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address, create_time, email, password, phone, real_name, status, update_time, user_type, username, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        users
        (address, create_time, email, password, phone, real_name, status, update_time, user_type, username, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address, create_time, email, password, phone, real_name, status, update_time, user_type, username, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        users
        (address, create_time, email, password, phone, real_name, status, update_time, user_type, username, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address, create_time, email, password, phone, real_name, status, update_time, user_type, username, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        users
        (address, create_time, email, password, phone, real_name, status, update_time, user_type, username, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address, create_time, email, password, phone, real_name, status, update_time, user_type, username, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        users
        (address, create_time, email, password, phone, real_name, status, update_time, user_type, username, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address, create_time, email, password, phone, real_name, status, update_time, user_type, username, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        users
        (address, create_time, email, password, phone, real_name, status, update_time, user_type, username, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address, create_time, email, password, phone, real_name, status, update_time, user_type, username, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - 
    insert 
    into
        users
        (address, create_time, email, password, phone, real_name, status, update_time, user_type, username, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address, create_time, email, password, phone, real_name, status, update_time, user_type, username, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:57 - Created 10 test users
2025-06-05 19:47:57 - 
    select
        count(*) 
    from
        technicians t1_0
Hibernate: 
    select
        count(*) 
    from
        technicians t1_0
2025-06-05 19:47:57 - 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        technicians
        (create_time, email, hire_date, hourly_rate, password, phone, rating, real_name, specialty, status, update_time, username, workload, technician_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - Created 17 test technicians
2025-06-05 19:47:58 - 
    select
        count(*) 
    from
        vehicles v1_0
Hibernate: 
    select
        count(*) 
    from
        vehicles v1_0
2025-06-05 19:47:58 - 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        vehicles
        (brand, color, create_time, engine_number, license_plate, model, register_date, update_time, user_id, vin, vehicle_year, vehicle_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - Created 20 test vehicles
2025-06-05 19:47:58 - 
    select
        count(*) 
    from
        repair_orders ro1_0
Hibernate: 
    select
        count(*) 
    from
        repair_orders ro1_0
2025-06-05 19:47:58 - 
    select
        ft1_0.fault_type_id,
        ft1_0.create_time,
        ft1_0.description,
        ft1_0.estimated_hours,
        ft1_0.required_tech_count,
        ft1_0.status,
        ft1_0.type_name,
        ft1_0.update_time 
    from
        fault_types ft1_0
Hibernate: 
    select
        ft1_0.fault_type_id,
        ft1_0.create_time,
        ft1_0.description,
        ft1_0.estimated_hours,
        ft1_0.required_tech_count,
        ft1_0.status,
        ft1_0.type_name,
        ft1_0.update_time 
    from
        fault_types ft1_0
2025-06-05 19:47:58 - 
    select
        rs1_0.fault_type_id,
        rs1_0.specialty 
    from
        fault_type_specialties rs1_0 
    where
        rs1_0.fault_type_id=?
Hibernate: 
    select
        rs1_0.fault_type_id,
        rs1_0.specialty 
    from
        fault_type_specialties rs1_0 
    where
        rs1_0.fault_type_id=?
2025-06-05 19:47:58 - 
    select
        rs1_0.fault_type_id,
        rs1_0.specialty 
    from
        fault_type_specialties rs1_0 
    where
        rs1_0.fault_type_id=?
Hibernate: 
    select
        rs1_0.fault_type_id,
        rs1_0.specialty 
    from
        fault_type_specialties rs1_0 
    where
        rs1_0.fault_type_id=?
2025-06-05 19:47:58 - 
    select
        rs1_0.fault_type_id,
        rs1_0.specialty 
    from
        fault_type_specialties rs1_0 
    where
        rs1_0.fault_type_id=?
Hibernate: 
    select
        rs1_0.fault_type_id,
        rs1_0.specialty 
    from
        fault_type_specialties rs1_0 
    where
        rs1_0.fault_type_id=?
2025-06-05 19:47:58 - 
    select
        rs1_0.fault_type_id,
        rs1_0.specialty 
    from
        fault_type_specialties rs1_0 
    where
        rs1_0.fault_type_id=?
Hibernate: 
    select
        rs1_0.fault_type_id,
        rs1_0.specialty 
    from
        fault_type_specialties rs1_0 
    where
        rs1_0.fault_type_id=?
2025-06-05 19:47:58 - 
    select
        rs1_0.fault_type_id,
        rs1_0.specialty 
    from
        fault_type_specialties rs1_0 
    where
        rs1_0.fault_type_id=?
Hibernate: 
    select
        rs1_0.fault_type_id,
        rs1_0.specialty 
    from
        fault_type_specialties rs1_0 
    where
        rs1_0.fault_type_id=?
2025-06-05 19:47:58 - 
    select
        rs1_0.fault_type_id,
        rs1_0.specialty 
    from
        fault_type_specialties rs1_0 
    where
        rs1_0.fault_type_id=?
Hibernate: 
    select
        rs1_0.fault_type_id,
        rs1_0.specialty 
    from
        fault_type_specialties rs1_0 
    where
        rs1_0.fault_type_id=?
2025-06-05 19:47:58 - 
    select
        rs1_0.fault_type_id,
        rs1_0.specialty 
    from
        fault_type_specialties rs1_0 
    where
        rs1_0.fault_type_id=?
Hibernate: 
    select
        rs1_0.fault_type_id,
        rs1_0.specialty 
    from
        fault_type_specialties rs1_0 
    where
        rs1_0.fault_type_id=?
2025-06-05 19:47:58 - 
    select
        rs1_0.fault_type_id,
        rs1_0.specialty 
    from
        fault_type_specialties rs1_0 
    where
        rs1_0.fault_type_id=?
Hibernate: 
    select
        rs1_0.fault_type_id,
        rs1_0.specialty 
    from
        fault_type_specialties rs1_0 
    where
        rs1_0.fault_type_id=?
2025-06-05 19:47:58 - 
    select
        rs1_0.fault_type_id,
        rs1_0.specialty 
    from
        fault_type_specialties rs1_0 
    where
        rs1_0.fault_type_id=?
Hibernate: 
    select
        rs1_0.fault_type_id,
        rs1_0.specialty 
    from
        fault_type_specialties rs1_0 
    where
        rs1_0.fault_type_id=?
2025-06-05 19:47:58 - 
    select
        rs1_0.fault_type_id,
        rs1_0.specialty 
    from
        fault_type_specialties rs1_0 
    where
        rs1_0.fault_type_id=?
Hibernate: 
    select
        rs1_0.fault_type_id,
        rs1_0.specialty 
    from
        fault_type_specialties rs1_0 
    where
        rs1_0.fault_type_id=?
2025-06-05 19:47:58 - 
    select
        m1_0.material_id,
        m1_0.category,
        m1_0.create_time,
        m1_0.inventory,
        m1_0.material_name,
        m1_0.specification,
        m1_0.status,
        m1_0.unit,
        m1_0.unit_price,
        m1_0.update_time 
    from
        materials m1_0
Hibernate: 
    select
        m1_0.material_id,
        m1_0.category,
        m1_0.create_time,
        m1_0.inventory,
        m1_0.material_name,
        m1_0.specification,
        m1_0.status,
        m1_0.unit,
        m1_0.unit_price,
        m1_0.update_time 
    from
        materials m1_0
2025-06-05 19:47:58 - 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:58 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:58 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:58 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:58 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:58 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:58 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
2025-06-05 19:47:59 - 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
2025-06-05 19:47:59 - 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
2025-06-05 19:47:59 - 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
2025-06-05 19:47:59 - 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
2025-06-05 19:47:59 - 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
2025-06-05 19:47:59 - 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
2025-06-05 19:47:59 - 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
2025-06-05 19:47:59 - 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
2025-06-05 19:47:59 - 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.payment_id=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.payment_id=?
2025-06-05 19:47:59 - 
    update
        labor_payments 
    set
        hourly_rate=?,
        payment_month=?,
        payment_status=?,
        payment_time=?,
        technician_id=?,
        total_amount=?,
        total_hours=?,
        update_time=?,
        payment_year=? 
    where
        payment_id=?
Hibernate: 
    update
        labor_payments 
    set
        hourly_rate=?,
        payment_month=?,
        payment_status=?,
        payment_time=?,
        technician_id=?,
        total_amount=?,
        total_hours=?,
        update_time=?,
        payment_year=? 
    where
        payment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
2025-06-05 19:47:59 - 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
2025-06-05 19:47:59 - 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
2025-06-05 19:47:59 - 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
2025-06-05 19:47:59 - 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.payment_id=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.payment_id=?
2025-06-05 19:47:59 - 
    update
        labor_payments 
    set
        hourly_rate=?,
        payment_month=?,
        payment_status=?,
        payment_time=?,
        technician_id=?,
        total_amount=?,
        total_hours=?,
        update_time=?,
        payment_year=? 
    where
        payment_id=?
Hibernate: 
    update
        labor_payments 
    set
        hourly_rate=?,
        payment_month=?,
        payment_status=?,
        payment_time=?,
        technician_id=?,
        total_amount=?,
        total_hours=?,
        update_time=?,
        payment_year=? 
    where
        payment_id=?
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.payment_id=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.payment_id=?
2025-06-05 19:47:59 - 
    update
        labor_payments 
    set
        hourly_rate=?,
        payment_month=?,
        payment_status=?,
        payment_time=?,
        technician_id=?,
        total_amount=?,
        total_hours=?,
        update_time=?,
        payment_year=? 
    where
        payment_id=?
Hibernate: 
    update
        labor_payments 
    set
        hourly_rate=?,
        payment_month=?,
        payment_status=?,
        payment_time=?,
        technician_id=?,
        total_amount=?,
        total_hours=?,
        update_time=?,
        payment_year=? 
    where
        payment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
2025-06-05 19:47:59 - 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.payment_id=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.payment_id=?
2025-06-05 19:47:59 - 
    update
        labor_payments 
    set
        hourly_rate=?,
        payment_month=?,
        payment_status=?,
        payment_time=?,
        technician_id=?,
        total_amount=?,
        total_hours=?,
        update_time=?,
        payment_year=? 
    where
        payment_id=?
Hibernate: 
    update
        labor_payments 
    set
        hourly_rate=?,
        payment_month=?,
        payment_status=?,
        payment_time=?,
        technician_id=?,
        total_amount=?,
        total_hours=?,
        update_time=?,
        payment_year=? 
    where
        payment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
2025-06-05 19:47:59 - 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.payment_id=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.payment_id=?
2025-06-05 19:47:59 - 
    update
        labor_payments 
    set
        hourly_rate=?,
        payment_month=?,
        payment_status=?,
        payment_time=?,
        technician_id=?,
        total_amount=?,
        total_hours=?,
        update_time=?,
        payment_year=? 
    where
        payment_id=?
Hibernate: 
    update
        labor_payments 
    set
        hourly_rate=?,
        payment_month=?,
        payment_status=?,
        payment_time=?,
        technician_id=?,
        total_amount=?,
        total_hours=?,
        update_time=?,
        payment_year=? 
    where
        payment_id=?
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.payment_id=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.payment_id=?
2025-06-05 19:47:59 - 
    update
        labor_payments 
    set
        hourly_rate=?,
        payment_month=?,
        payment_status=?,
        payment_time=?,
        technician_id=?,
        total_amount=?,
        total_hours=?,
        update_time=?,
        payment_year=? 
    where
        payment_id=?
Hibernate: 
    update
        labor_payments 
    set
        hourly_rate=?,
        payment_month=?,
        payment_status=?,
        payment_time=?,
        technician_id=?,
        total_amount=?,
        total_hours=?,
        update_time=?,
        payment_year=? 
    where
        payment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_material_usage
        (create_time, material_id, quantity, order_id, total_price, update_time, use_time, usage_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_feedback
        (comment, create_time, feedback_time, rating, order_id, update_time, feedback_id) 
    values
        (?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
Hibernate: 
    select
        lp1_0.payment_id,
        lp1_0.create_time,
        lp1_0.hourly_rate,
        lp1_0.payment_month,
        lp1_0.payment_status,
        lp1_0.payment_time,
        lp1_0.technician_id,
        lp1_0.total_amount,
        lp1_0.total_hours,
        lp1_0.update_time,
        lp1_0.payment_year 
    from
        labor_payments lp1_0 
    where
        lp1_0.technician_id=? 
        and lp1_0.payment_year=? 
        and lp1_0.payment_month=?
2025-06-05 19:47:59 - 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        labor_payments
        (create_time, hourly_rate, payment_month, payment_status, payment_time, technician_id, total_amount, total_hours, update_time, payment_year, payment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        repair_orders
        (actual_completion_time, contact_phone, create_time, description, estimated_completion_time, fault_type_id, payment_status, preferred_time, status, submit_time, total_cost, total_labor_cost, total_material_cost, update_time, urgency_level, user_id, vehicle_id, work_result, working_hours, order_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_technician_assignments
        (agreement_status, assigned_time, create_time, reject_reason, order_id, response_time, technician_id, update_time, assignment_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
Hibernate: 
    select
        ro1_0.order_id,
        ro1_0.actual_completion_time,
        ro1_0.contact_phone,
        ro1_0.create_time,
        ro1_0.description,
        ro1_0.estimated_completion_time,
        ro1_0.fault_type_id,
        f1_0.feedback_id,
        f1_0.comment,
        f1_0.create_time,
        f1_0.feedback_time,
        f1_0.rating,
        f1_0.order_id,
        f1_0.update_time,
        ro1_0.payment_status,
        ro1_0.preferred_time,
        ro1_0.status,
        ro1_0.submit_time,
        ro1_0.total_cost,
        ro1_0.total_labor_cost,
        ro1_0.total_material_cost,
        ro1_0.update_time,
        ro1_0.urgency_level,
        ro1_0.user_id,
        ro1_0.vehicle_id,
        ro1_0.work_result,
        ro1_0.working_hours,
        mu1_0.order_id,
        mu1_0.usage_id,
        mu1_0.create_time,
        mu1_0.material_id,
        mu1_0.quantity,
        mu1_0.total_price,
        mu1_0.update_time,
        mu1_0.use_time 
    from
        repair_orders ro1_0 
    left join
        order_feedback f1_0 
            on ro1_0.order_id=f1_0.order_id 
    left join
        order_material_usage mu1_0 
            on ro1_0.order_id=mu1_0.order_id 
    where
        ro1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
Hibernate: 
    select
        ta1_0.order_id,
        ta1_0.assignment_id,
        ta1_0.agreement_status,
        ta1_0.assigned_time,
        ta1_0.create_time,
        ta1_0.reject_reason,
        ta1_0.response_time,
        ta1_0.technician_id,
        ta1_0.update_time 
    from
        order_technician_assignments ta1_0 
    where
        ta1_0.order_id=?
2025-06-05 19:47:59 - 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
Hibernate: 
    select
        sh1_0.order_id,
        sh1_0.history_id,
        sh1_0.change_time,
        sh1_0.create_time,
        sh1_0.from_status,
        sh1_0.operator,
        sh1_0.remark,
        sh1_0.to_status,
        sh1_0.update_time 
    from
        order_status_history sh1_0 
    where
        sh1_0.order_id=?
2025-06-05 19:47:59 - 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
Hibernate: 
    update
        repair_orders 
    set
        actual_completion_time=?,
        contact_phone=?,
        description=?,
        estimated_completion_time=?,
        fault_type_id=?,
        payment_status=?,
        preferred_time=?,
        status=?,
        submit_time=?,
        total_cost=?,
        total_labor_cost=?,
        total_material_cost=?,
        update_time=?,
        urgency_level=?,
        user_id=?,
        vehicle_id=?,
        work_result=?,
        working_hours=? 
    where
        order_id=?
2025-06-05 19:47:59 - 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
Hibernate: 
    update
        order_technician_assignments 
    set
        agreement_status=?,
        assigned_time=?,
        reject_reason=?,
        order_id=?,
        response_time=?,
        technician_id=?,
        update_time=? 
    where
        assignment_id=?
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        order_status_history
        (change_time, create_time, from_status, operator, remark, order_id, to_status, update_time, history_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, default)
2025-06-05 19:47:59 - Test orders initialization completed
2025-06-05 19:47:59 - Test data initialization completed successfully.
2025-06-05 19:49:37 - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 19:49:37 - 
    drop table if exists fault_type_specialties cascade 
Hibernate: 
    drop table if exists fault_type_specialties cascade 
2025-06-05 19:49:37 - 
    drop table if exists fault_types cascade 
Hibernate: 
    drop table if exists fault_types cascade 
2025-06-05 19:49:37 - 
    drop table if exists labor_payments cascade 
Hibernate: 
    drop table if exists labor_payments cascade 
2025-06-05 19:49:37 - 
    drop table if exists materials cascade 
Hibernate: 
    drop table if exists materials cascade 
2025-06-05 19:49:37 - 
    drop table if exists order_feedback cascade 
Hibernate: 
    drop table if exists order_feedback cascade 
2025-06-05 19:49:37 - 
    drop table if exists order_material_usage cascade 
Hibernate: 
    drop table if exists order_material_usage cascade 
2025-06-05 19:49:37 - 
    drop table if exists order_status_history cascade 
Hibernate: 
    drop table if exists order_status_history cascade 
2025-06-05 19:49:37 - 
    drop table if exists order_technician_assignments cascade 
Hibernate: 
    drop table if exists order_technician_assignments cascade 
2025-06-05 19:49:37 - 
    drop table if exists repair_orders cascade 
Hibernate: 
    drop table if exists repair_orders cascade 
2025-06-05 19:49:37 - 
    drop table if exists technicians cascade 
Hibernate: 
    drop table if exists technicians cascade 
2025-06-05 19:49:37 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
2025-06-05 19:49:37 - 
    drop table if exists vehicles cascade 
Hibernate: 
    drop table if exists vehicles cascade 
2025-06-05 19:49:37 - Invocation of destroy method failed on bean with name 'inMemoryDatabaseShutdownExecutor': org.h2.jdbc.JdbcSQLNonTransientConnectionException: Database is already closed (to disable automatic closing at VM shutdown, add ";DB_CLOSE_ON_EXIT=FALSE" to the db URL) [90121-224]
2025-06-05 19:49:37 - HikariPool-1 - Shutdown initiated...
2025-06-05 19:49:37 - HikariPool-1 - Shutdown completed.
[INFO] ------------------------------------------------------------------------
[INFO] BUILD SUCCESS
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  01:46 min
[INFO] Finished at: 2025-06-05T19:49:37+08:00
[INFO] ------------------------------------------------------------------------
