package com.example.repository;

import com.example.entity.OrderUrgentRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 工单催单Repository
 */
@Repository
public interface OrderUrgentRequestRepository extends JpaRepository<OrderUrgentRequest, Long> {

    /**
     * 根据工单ID查找催单记录
     */
    @Query("SELECT ur FROM OrderUrgentRequest ur WHERE ur.repairOrder.orderId = :orderId ORDER BY ur.urgentTime DESC")
    List<OrderUrgentRequest> findByOrderIdOrderByUrgentTimeDesc(@Param("orderId") Long orderId);

    /**
     * 统计工单的催单次数
     */
    @Query("SELECT COUNT(ur) FROM OrderUrgentRequest ur WHERE ur.repairOrder.orderId = :orderId")
    Long countByOrderId(@Param("orderId") Long orderId);
}
