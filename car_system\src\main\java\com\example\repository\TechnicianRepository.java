package com.example.repository;

import com.example.entity.Technician;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TechnicianRepository extends JpaRepository<Technician, Long>, JpaSpecificationExecutor<Technician> {

    /**
     * 根据用户名查找技师
     */
    Optional<Technician> findByUsername(String username);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查手机号是否存在
     */
    boolean existsByPhone(String phone);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 根据工种查找技师
     */
    List<Technician> findBySpecialty(Technician.Specialty specialty);

    /**
     * 根据工种和状态查找技师
     */
    List<Technician> findBySpecialtyAndStatus(Technician.Specialty specialty, Integer status);

    /**
     * 根据工种查找技师（分页）
     */
    Page<Technician> findBySpecialty(Technician.Specialty specialty, Pageable pageable);

    /**
     * 根据状态查找技师
     */
    Page<Technician> findByStatus(Integer status, Pageable pageable);

    /**
     * 根据工种和状态查找技师（分页）
     */
    Page<Technician> findBySpecialtyAndStatus(Technician.Specialty specialty, Integer status, Pageable pageable);

    /**
     * 根据工种查找可用技师（按工作负载排序）
     */
    @Query("SELECT t FROM Technician t WHERE t.specialty = :specialty AND t.status = 1 ORDER BY t.workload ASC")
    List<Technician> findAvailableTechniciansBySpecialty(@Param("specialty") Technician.Specialty specialty);

    /**
     * 根据工种查找可用技师（限制数量，按工作负载排序）
     */
    @Query("SELECT t FROM Technician t WHERE t.specialty = :specialty AND t.status = 1 ORDER BY t.workload ASC")
    List<Technician> findAvailableTechniciansBySpecialtyWithLimit(@Param("specialty") Technician.Specialty specialty,
                                                                  Pageable pageable);

    /**
     * 搜索技师（根据用户名或真实姓名）
     */
    @Query("SELECT t FROM Technician t WHERE " +
           "LOWER(t.username) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(t.realName) LIKE LOWER(CONCAT('%', :search, '%'))")
    Page<Technician> searchByUsernameOrRealName(@Param("search") String search, Pageable pageable);

    /**
     * 搜索技师（根据用户名或真实姓名，包含工种过滤）
     */
    @Query("SELECT t FROM Technician t WHERE " +
           "(LOWER(t.username) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(t.realName) LIKE LOWER(CONCAT('%', :search, '%'))) AND " +
           "t.specialty = :specialty")
    Page<Technician> searchByUsernameOrRealNameAndSpecialty(@Param("search") String search,
                                                            @Param("specialty") Technician.Specialty specialty,
                                                            Pageable pageable);

    /**
     * 搜索技师（根据用户名或真实姓名，包含状态过滤）
     */
    @Query("SELECT t FROM Technician t WHERE " +
           "(LOWER(t.username) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(t.realName) LIKE LOWER(CONCAT('%', :search, '%'))) AND " +
           "t.status = :status")
    Page<Technician> searchByUsernameOrRealNameAndStatus(@Param("search") String search,
                                                         @Param("status") Integer status,
                                                         Pageable pageable);
}
