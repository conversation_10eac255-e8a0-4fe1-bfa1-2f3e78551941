import api from './index'

export const materialAPI = {
  // 获取材料列表
  getList(params = {}) {
    return api.get('/materials', { params })
  },

  // 获取材料详情
  getById(id) {
    return api.get(`/materials/${id}`)
  },

  // 添加材料
  create(data) {
    return api.post('/materials', data)
  },

  // 更新材料信息
  update(id, data) {
    return api.put(`/materials/${id}`, data)
  },

  // 删除材料
  delete(id) {
    return api.delete(`/materials/${id}`)
  }
}
